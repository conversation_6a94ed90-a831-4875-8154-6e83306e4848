[package]
name = "kernel"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[features]
net = []

[build-dependencies]
toml = "0.5.2"
serde = "1.0.136"
serde_derive = "1.0.136"

[dependencies]
log = "0.4"
devices = { workspace = true }
executor = { workspace = true }
polyhal = { workspace = true }
polyhal-boot = { workspace = true }
polyhal-trap = { workspace = true }
xmas-elf = "0.9.0"
signal = { workspace = true }
sync = { workspace = true }
bitflags = "2.0.2"
bit_field = "0.10.1"
lose-net-stack = { git = "https://github.com/byte-os/lose-net-stack", rev = "bb99460", features = [
    "log",
] }
async-recursion = "1.1.0"
futures-lite = { version = "1.13.0", default-features = false, features = [
    "alloc",
] }
hashbrown = "0.14"
cfg-if = "1.0.0"

num-traits = { version = "0.2", default-features = false }
num-derive = "0.4"

syscalls = { workspace = true }
runtime = { workspace = true }

# filesystem
fs = { workspace = true }
vfscore = { workspace = true }

# drivers
kvirtio = { workspace = true }
kgoldfish-rtc = { workspace = true }
kramdisk = { workspace = true }
general-plic = { workspace = true }
ns16550a = { workspace = true }
