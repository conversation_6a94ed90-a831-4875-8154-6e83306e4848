# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "acpi"
version = "5.2.0"
authors = ["<PERSON>"]
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A pure-Rust library for parsing ACPI tables"
readme = "README.md"
categories = [
    "hardware-support",
    "no-std",
]
license = "MIT/Apache-2.0"
repository = "https://github.com/rust-osdev/acpi"

[features]
alloc = ["allocator_api"]
allocator_api = []
default = [
    "allocator_api",
    "alloc",
]

[lib]
name = "acpi"
path = "src/lib.rs"

[dependencies.bit_field]
version = "0.10.2"

[dependencies.bitflags]
version = "2.5.0"

[dependencies.log]
version = "0.4.20"
