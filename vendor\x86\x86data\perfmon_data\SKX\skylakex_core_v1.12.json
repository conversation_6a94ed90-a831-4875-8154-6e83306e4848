[{"EventCode": "0x00", "UMask": "0x01", "EventName": "INST_RETIRED.ANY", "BriefDescription": "Instructions retired from execution.", "PublicDescription": "Counts the number of instructions retired from execution. For instructions that consist of multiple micro-ops, Counts the retirement of the last micro-op of the instruction. Counting continues during hardware interrupts, traps, and inside interrupt handlers. Notes: INST_RETIRED.ANY is counted by a designated fixed counter, leaving the four (eight when Hyperthreading is disabled) programmable counters available for other events. INST_RETIRED.ANY_P is counted by a programmable counter and it is an architectural performance event. Counting: Faulting executions of GETSEC/VM entry/VM Exit/MWait will not count as retired instructions.", "Counter": "Fixed counter 0", "CounterHTOff": "Fixed counter 0", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x00", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.THREAD", "BriefDescription": "Core cycles when the thread is not in halt state", "PublicDescription": "Counts the number of core cycles while the thread is not in a halt state. The thread enters the halt state when it is running the HLT instruction. This event is a component in many key event ratios. The core frequency may change from time to time due to transitions associated with Enhanced Intel SpeedStep Technology or TM2. For this reason this event may have a changing ratio with regards to time. When the core frequency is constant, this event can approximate elapsed time while the core was not in the halt state. It is counted on a dedicated fixed counter, leaving the four (eight when Hyperthreading is disabled) programmable counters available for other events.", "Counter": "Fixed counter 1", "CounterHTOff": "Fixed counter 1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x00", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.THREAD_ANY", "BriefDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "PublicDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "Counter": "Fixed counter 1", "CounterHTOff": "Fixed counter 1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x00", "UMask": "0x03", "EventName": "CPU_CLK_UNHALTED.REF_TSC", "BriefDescription": "Reference cycles when the core is not in halt state.", "PublicDescription": "Counts the number of reference cycles when the core is not in a halt state. The core enters the halt state when it is running the HLT instruction or the MWAIT instruction. This event is not affected by core frequency changes (for example, P states, TM2 transitions) but has the same incrementing frequency as the time stamp counter. This event can approximate elapsed time while the core was not in a halt state. This event has a constant ratio with the CPU_CLK_UNHALTED.REF_XCLK event. It is counted on a dedicated fixed counter, leaving the four (eight when Hyperthreading is disabled) programmable counters available for other events. Note: On all current platforms this event stops counting during 'throttling (TM)' states duty off periods the processor is 'halted'.  The counter update is done at a lower clock rate then the core clock the overflow status bit for this counter may appear 'sticky'.  After the counter has overflowed and software clears the overflow status bit and resets the counter to less than MAX. The reset value to the counter is not clocked immediately so the overflow status bit will flip 'high (1)' and generate another PMI (if enabled) after which the reset value gets clocked into the counter. Therefore, software will get the interrupt, read the overflow status bit '1 for bit 34 while the counter value is less than MAX. Software should ignore this case.", "Counter": "Fixed counter 2", "CounterHTOff": "Fixed counter 2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x03", "UMask": "0x02", "EventName": "LD_BLOCKS.STORE_FORWARD", "BriefDescription": "Loads blocked by overlapping with store buffer that cannot be forwarded .", "PublicDescription": "Counts how many times the load operation got the true Block-on-Store blocking code preventing store forwarding. This includes cases when:a. preceding store conflicts with the load (incomplete overlap),b. store forwarding is impossible due to u-arch limitations,c. preceding lock RMW operations are not forwarded,d. store has the no-forward bit set (uncacheable/page-split/masked stores),e. all-blocking stores are used (mostly, fences and port I/O), and others.The most common case is a load blocked due to its address range overlapping with a preceding smaller uncompleted store. Note: This event does not take into account cases of out-of-SW-control (for example, SbTailHit), unknown physical STA, and cases of blocking loads on store due to being non-WB memory type or a lock. These cases are covered by other events. See the table of not supported store forwards in the Optimization Guide.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x03", "UMask": "0x08", "EventName": "LD_BLOCKS.NO_SR", "BriefDescription": "The number of times that split load operations are temporarily blocked because all resources for handling the split accesses are in use", "PublicDescription": "The number of times that split load operations are temporarily blocked because all resources for handling the split accesses are in use.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x07", "UMask": "0x01", "EventName": "LD_BLOCKS_PARTIAL.ADDRESS_ALIAS", "BriefDescription": "False dependencies in MOB due to partial compare on address.", "PublicDescription": "Counts false dependencies in MOB when the partial comparison upon loose net check and dependency was resolved by the Enhanced Loose net mechanism. This may not result in high performance penalties. Loose net checks can fail when loads and stores are 4k aliased.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x01", "EventName": "DTLB_LOAD_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Load misses in all DTLB levels that cause page walks", "PublicDescription": "Counts demand data loads that caused a page walk of any page size (4K/2M/4M/1G). This implies it missed in all TLB levels, but the walk need not have completed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x02", "EventName": "DTLB_LOAD_MISSES.WALK_COMPLETED_4K", "BriefDescription": "Page walk completed due to a demand data load to a 4K page", "PublicDescription": "Counts page walks completed due to demand data loads whose address translations missed in the TLB and were mapped to 4K pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x04", "EventName": "DTLB_LOAD_MISSES.WALK_COMPLETED_2M_4M", "BriefDescription": "Page walk completed due to a demand data load to a 2M/4M page", "PublicDescription": "Counts page walks completed due to demand data loads whose address translations missed in the TLB and were mapped to 2M/4M pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x08", "EventName": "DTLB_LOAD_MISSES.WALK_COMPLETED_1G", "BriefDescription": "Page walk completed due to a demand data load to a 1G page", "PublicDescription": "Counts page walks completed due to demand data loads whose address translations missed in the TLB and were mapped to 4K pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x0E", "EventName": "DTLB_LOAD_MISSES.WALK_COMPLETED", "BriefDescription": "Load miss in all TLB levels causes a page walk that completes. (All page sizes)", "PublicDescription": "Counts demand data loads that caused a completed page walk of any page size (4K/2M/4M/1G). This implies it missed in all TLB levels. The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x10", "EventName": "DTLB_LOAD_MISSES.WALK_PENDING", "BriefDescription": "Counts 1 per cycle for each PMH that is busy with a page walk for a load. EPT page walk duration are excluded in Skylake. ", "PublicDescription": "Counts 1 per cycle for each PMH that is busy with a page walk for a load. EPT page walk duration are excluded in Skylake microarchitecture. ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x10", "EventName": "DTLB_LOAD_MISSES.WALK_ACTIVE", "BriefDescription": "Cycles when at least one PMH is busy with a page walk for a load. EPT page walk duration are excluded in Skylake. ", "PublicDescription": "Counts cycles when at least one PMH (<PERSON>) is busy with a page walk for a load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x08", "UMask": "0x20", "EventName": "DTLB_LOAD_MISSES.STLB_HIT", "BriefDescription": "Loads that miss the DTLB and hit the STLB.", "PublicDescription": "Counts loads that miss the DTLB (Data TLB) and hit the STLB (Second level TLB).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0D", "UMask": "0x01", "EventName": "INT_MISC.RECOVERY_CYCLES", "BriefDescription": "Core cycles the allocator was stalled due to recovery from earlier clear event for this thread (e.g. misprediction or memory nuke)", "PublicDescription": "Core cycles the Resource allocator was stalled due to recovery from an earlier branch misprediction or machine clear event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0D", "UMask": "0x01", "EventName": "INT_MISC.RECOVERY_CYCLES_ANY", "BriefDescription": "Core cycles the allocator was stalled due to recovery from earlier clear event for any thread running on the physical core (e.g. misprediction or memory nuke).", "PublicDescription": "Core cycles the allocator was stalled due to recovery from earlier clear event for any thread running on the physical core (e.g. misprediction or memory nuke).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0D", "UMask": "0x80", "EventName": "INT_MISC.CLEAR_RESTEER_CYCLES", "BriefDescription": "Cycles the issue-stage is waiting for front-end to fetch from resteered path following branch misprediction or machine clear events.", "PublicDescription": "Cycles the issue-stage is waiting for front-end to fetch from resteered path following branch misprediction or machine clear events.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0E", "UMask": "0x01", "EventName": "UOPS_ISSUED.STALL_CYCLES", "BriefDescription": "Cycles when Resource Allocation Table (RAT) does not issue Uops to Reservation Station (RS) for the thread", "PublicDescription": "Counts cycles during which the Resource Allocation Table (RAT) does not issue any Uops to the reservation station (RS) for the current thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0E", "UMask": "0x01", "EventName": "UOPS_ISSUED.ANY", "BriefDescription": "Uops that Resource Allocation Table (RAT) issues to Reservation Station (RS)", "PublicDescription": "Counts the number of uops that the Resource Allocation Table (RAT) issues to the Reservation Station (RS).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0E", "UMask": "0x02", "EventName": "UOPS_ISSUED.VECTOR_WIDTH_MISMATCH", "BriefDescription": "Uops inserted at issue-stage in order to preserve upper bits of vector registers.", "PublicDescription": "Counts the number of Blend Uops issued by the Resource Allocation Table (RAT) to the reservation station (RS) in order to preserve upper bits of vector registers. Starting with the Skylake microarchitecture, these Blend uops are needed since every Intel SSE instruction executed in Dirty Upper State needs to preserve bits 128-255 of the destination register. For more information, refer to “Mixing Intel AVX and Intel SSE Code” section of the Optimization Guide.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x0E", "UMask": "0x20", "EventName": "UOPS_ISSUED.SLOW_LEA", "BriefDescription": "Number of slow LEA uops being allocated. A uop is generally considered SlowLea if it has 3 sources (e.g. 2 sources + immediate) regardless if as a result of LEA instruction or not.", "PublicDescription": "Number of slow LEA uops being allocated. A uop is generally considered SlowLea if it has 3 sources (e.g. 2 sources + immediate) regardless if as a result of LEA instruction or not.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x14", "UMask": "0x01", "EventName": "ARITH.DIVIDER_ACTIVE", "BriefDescription": "Cycles when divide unit is busy executing divide or square root operations. Accounts for integer and floating-point operations.", "PublicDescription": "Cycles when divide unit is busy executing divide or square root operations. Accounts for integer and floating-point operations.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x21", "EventName": "L2_RQSTS.DEMAND_DATA_RD_MISS", "BriefDescription": "Demand Data Read miss L2, no rejects", "PublicDescription": "Counts the number of demand Data Read requests that miss L2 cache. Only not rejected loads are counted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x22", "EventName": "L2_RQSTS.RFO_MISS", "BriefDescription": "RFO requests that miss L2 cache", "PublicDescription": "Counts the RFO (Read-for-Ownership) requests that miss L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x24", "EventName": "L2_RQSTS.CODE_RD_MISS", "BriefDescription": "L2 cache misses when fetching instructions", "PublicDescription": "Counts L2 cache misses when fetching instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x27", "EventName": "L2_RQSTS.ALL_DEMAND_MISS", "BriefDescription": "Demand requests that miss L2 cache", "PublicDescription": "Demand requests that miss L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x38", "EventName": "L2_RQSTS.PF_MISS", "BriefDescription": "Requests from the L1/L2/L3 hardware prefetchers or Load software prefetches that miss L2 cache", "PublicDescription": "Counts requests from the L1/L2/L3 hardware prefetchers or Load software prefetches that miss L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0x3F", "EventName": "L2_RQSTS.MISS", "BriefDescription": "All requests that miss L2 cache", "PublicDescription": "All requests that miss L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xc1", "EventName": "L2_RQSTS.DEMAND_DATA_RD_HIT", "BriefDescription": "Demand Data Read requests that hit L2 cache", "PublicDescription": "Counts the number of demand Data Read requests, initiated by load instructions, that hit L2 cache", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xc2", "EventName": "L2_RQSTS.RFO_HIT", "BriefDescription": "RFO requests that hit L2 cache", "PublicDescription": "Counts the RFO (Read-for-Ownership) requests that hit L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xc4", "EventName": "L2_RQSTS.CODE_RD_HIT", "BriefDescription": "L2 cache hits when fetching instructions, code reads.", "PublicDescription": "Counts L2 cache hits when fetching instructions, code reads.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xd8", "EventName": "L2_RQSTS.PF_HIT", "BriefDescription": "Requests from the L1/L2/L3 hardware prefetchers or Load software prefetches that hit L2 cache", "PublicDescription": "Counts requests from the L1/L2/L3 hardware prefetchers or Load software prefetches that hit L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xE1", "EventName": "L2_RQSTS.ALL_DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests", "PublicDescription": "Counts the number of demand Data Read requests (including requests from L1D hardware prefetchers). These loads may hit or miss L2 cache. Only non rejected loads are counted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xE2", "EventName": "L2_RQSTS.ALL_RFO", "BriefDescription": "RFO requests to L2 cache", "PublicDescription": "Counts the total number of RFO (read for ownership) requests to L2 cache. L2 RFO requests include both L1D demand RFO misses as well as L1D RFO prefetches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xE4", "EventName": "L2_RQSTS.ALL_CODE_RD", "BriefDescription": "L2 code requests", "PublicDescription": "Counts the total number of L2 code requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xe7", "EventName": "L2_RQSTS.ALL_DEMAND_REFERENCES", "BriefDescription": "Demand requests to L2 cache", "PublicDescription": "Demand requests to L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xF8", "EventName": "L2_RQSTS.ALL_PF", "BriefDescription": "Requests from the L1/L2/L3 hardware prefetchers or Load software prefetches", "PublicDescription": "Counts the total number of requests from the L2 hardware prefetchers.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x24", "UMask": "0xFF", "EventName": "L2_RQSTS.REFERENCES", "BriefDescription": "All L2 requests", "PublicDescription": "All L2 requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x28", "UMask": "0x07", "EventName": "CORE_POWER.LVL0_TURBO_LICENSE", "BriefDescription": "Core cycles where the core was running in a manner where Turbo may be clipped to the Non-AVX turbo schedule.", "PublicDescription": "Core cycles where the core was running with power-delivery for baseline license level 0.  This includes non-AVX codes, SSE, AVX 128-bit, and low-current AVX 256-bit codes.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x28", "UMask": "0x18", "EventName": "CORE_POWER.LVL1_TURBO_LICENSE", "BriefDescription": "Core cycles where the core was running in a manner where Turbo may be clipped to the AVX2 turbo schedule.", "PublicDescription": "Core cycles where the core was running with power-delivery for license level 1.  This includes high current AVX 256-bit instructions as well as low current AVX 512-bit instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x28", "UMask": "0x20", "EventName": "CORE_POWER.LVL2_TURBO_LICENSE", "BriefDescription": "Core cycles where the core was running in a manner where Turbo may be clipped to the AVX512 turbo schedule.", "PublicDescription": "Core cycles where the core was running with power-delivery for license level 2 (introduced in Skylake Server michroarchtecture).  This includes high current AVX 512-bit instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x28", "UMask": "0x40", "EventName": "CORE_POWER.THROTTLE", "BriefDescription": "Core cycles the core was throttled due to a pending power level request.", "PublicDescription": "Core cycles the out-of-order engine was throttled due to a pending power level request.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x2E", "UMask": "0x41", "EventName": "LONGEST_LAT_CACHE.MISS", "BriefDescription": "Core-originated cacheable demand requests missed L3", "PublicDescription": "Counts core-originated cacheable requests that miss the L3 cache (Longest Latency cache). Requests include data and code reads, Reads-for-Ownership (RFOs), speculative accesses and hardware prefetches from L1 and L2. It does not include all misses to the L3.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL057", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x2E", "UMask": "0x4F", "EventName": "LONGEST_LAT_CACHE.REFERENCE", "BriefDescription": "Core-originated cacheable demand requests that refer to L3", "PublicDescription": "Counts core-originated cacheable requests to the  L3 cache (Longest Latency cache). Requests include data and code reads, Reads-for-Ownership (RFOs), speculative accesses and hardware prefetches from L1 and L2.  It does not include all accesses to the L3.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL057", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x32", "UMask": "0x01", "EventName": "SW_PREFETCH_ACCESS.NTA", "BriefDescription": "Number of PREFETCHNTA instructions executed.", "PublicDescription": "Number of PREFETCHNTA instructions executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x32", "UMask": "0x02", "EventName": "SW_PREFETCH_ACCESS.T0", "BriefDescription": "Number of PREFETCHT0 instructions executed.", "PublicDescription": "Number of PREFETCHT0 instructions executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x32", "UMask": "0x04", "EventName": "SW_PREFETCH_ACCESS.T1_T2", "BriefDescription": "Number of PREFETCHT1 or PREFETCHT2 instructions executed.", "PublicDescription": "Number of PREFETCHT1 or PREFETCHT2 instructions executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x32", "UMask": "0x08", "EventName": "SW_PREFETCH_ACCESS.PREFETCHW", "BriefDescription": "Number of PREFETCHW instructions executed.", "PublicDescription": "Number of PREFETCHW instructions executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x00", "EventName": "CPU_CLK_UNHALTED.THREAD_P", "BriefDescription": "Thread cycles when thread is not in halt state", "PublicDescription": "This is an architectural event that counts the number of thread cycles while the thread is not in a halt state. The thread enters the halt state when it is running the HLT instruction. The core frequency may change from time to time due to power or thermal throttling. For this reason, this event may have a changing ratio with regards to wall clock time.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x00", "EventName": "CPU_CLK_UNHALTED.THREAD_P_ANY", "BriefDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "PublicDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x00", "EventName": "CPU_CLK_UNHALTED.RING0_TRANS", "BriefDescription": "Counts when there is a transition from ring 1, 2 or 3 to ring 0.", "PublicDescription": "Counts when the Current Privilege Level (CPL) transitions from ring 1, 2 or 3 to ring 0 (Kernel).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_THREAD_UNHALTED.REF_XCLK", "BriefDescription": "Core crystal clock cycles when the thread is unhalted.", "PublicDescription": "Core crystal clock cycles when the thread is unhalted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2503", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_THREAD_UNHALTED.REF_XCLK_ANY", "BriefDescription": "Core crystal clock cycles when at least one thread on the physical core is unhalted.", "PublicDescription": "Core crystal clock cycles when at least one thread on the physical core is unhalted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2503", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_UNHALTED.REF_XCLK_ANY", "BriefDescription": "Core crystal clock cycles when at least one thread on the physical core is unhalted.", "PublicDescription": "Core crystal clock cycles when at least one thread on the physical core is unhalted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2503", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_UNHALTED.REF_XCLK", "BriefDescription": "Core crystal clock cycles when the thread is unhalted.", "PublicDescription": "Core crystal clock cycles when the thread is unhalted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2503", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x02", "EventName": "CPU_CLK_THREAD_UNHALTED.ONE_THREAD_ACTIVE", "BriefDescription": "Core crystal clock cycles when this thread is unhalted and the other thread is halted.", "PublicDescription": "Core crystal clock cycles when this thread is unhalted and the other thread is halted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x3C", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.ONE_THREAD_ACTIVE", "BriefDescription": "Core crystal clock cycles when this thread is unhalted and the other thread is halted.", "PublicDescription": "Core crystal clock cycles when this thread is unhalted and the other thread is halted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2503", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING_CYCLES", "BriefDescription": "Cycles with L1D load Misses outstanding.", "PublicDescription": "Counts duration of L1D miss outstanding in cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING", "BriefDescription": "L1D miss outstandings duration in cycles", "PublicDescription": "Counts duration of L1D miss outstanding, that is each cycle number of Fill Buffers (FB) outstanding required by Demand Reads. FB either is held by demand loads, or it is held by non-demand loads and gets hit at least once by demand. The valid outstanding interval is defined until the FB deallocation by one of the following ways: from FB allocation, if FB is allocated by demand from the demand Hit FB, if it is allocated by hardware or software prefetch.Note: In the L1D, a Demand Read contains cacheable or noncacheable demand loads, including ones causing cache-line splits and reads due to page walks resulted from any request type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING_CYCLES_ANY", "BriefDescription": "Cycles with L1D load Misses outstanding from any thread on physical core.", "PublicDescription": "Cycles with L1D load Misses outstanding from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x48", "UMask": "0x02", "EventName": "L1D_PEND_MISS.FB_FULL", "BriefDescription": "Number of times a request needed a FB entry but there was no entry available for it. That is the FB unavailability was dominant reason for blocking the request. A request includes cacheable/uncacheable demands that is load, store or SW prefetch.", "PublicDescription": "Number of times a request needed a FB (Fill Buffer) entry but there was no entry available for it. A request includes cacheable/uncacheable demands that are load, store or SW prefetch instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x01", "EventName": "DTLB_STORE_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Store misses in all DTLB levels that cause page walks", "PublicDescription": "Counts demand data stores that caused a page walk of any page size (4K/2M/4M/1G). This implies it missed in all TLB levels, but the walk need not have completed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x02", "EventName": "DTLB_STORE_MISSES.WALK_COMPLETED_4K", "BriefDescription": "Page walk completed due to a demand data store to a 4K page", "PublicDescription": "Counts page walks completed due to demand data stores whose address translations missed in the TLB and were mapped to 4K pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x04", "EventName": "DTLB_STORE_MISSES.WALK_COMPLETED_2M_4M", "BriefDescription": "Page walk completed due to a demand data store to a 2M/4M page", "PublicDescription": "Counts page walks completed due to demand data stores whose address translations missed in the TLB and were mapped to 2M/4M pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x08", "EventName": "DTLB_STORE_MISSES.WALK_COMPLETED_1G", "BriefDescription": "Page walk completed due to a demand data store to a 1G page", "PublicDescription": "Counts page walks completed due to demand data stores whose address translations missed in the TLB and were mapped to 1G pages.  The page walks can end with or without a page fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x0E", "EventName": "DTLB_STORE_MISSES.WALK_COMPLETED", "BriefDescription": "Store misses in all TLB levels causes a page walk that completes. (All page sizes)", "PublicDescription": "Counts demand data stores that caused a completed page walk of any page size (4K/2M/4M/1G). This implies it missed in all TLB levels. The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x10", "EventName": "DTLB_STORE_MISSES.WALK_PENDING", "BriefDescription": "Counts 1 per cycle for each PMH that is busy with a page walk for a store. EPT page walk duration are excluded in Skylake. ", "PublicDescription": "Counts 1 per cycle for each PMH that is busy with a page walk for a store. EPT page walk duration are excluded in Skylake microarchitecture. ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x10", "EventName": "DTLB_STORE_MISSES.WALK_ACTIVE", "BriefDescription": "Cycles when at least one PMH is busy with a page walk for a store. EPT page walk duration are excluded in Skylake. ", "PublicDescription": "Counts cycles when at least one PMH (<PERSON>) is busy with a page walk for a store.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x49", "UMask": "0x20", "EventName": "DTLB_STORE_MISSES.STLB_HIT", "BriefDescription": "Stores that miss the DTLB and hit the STLB.", "PublicDescription": "Stores that miss the DTLB (Data TLB) and hit the STLB (2nd Level TLB).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x4C", "UMask": "0x01", "EventName": "LOAD_HIT_PRE.SW_PF", "BriefDescription": "Demand load dispatches that hit L1D fill buffer (FB) allocated for software prefetch.", "PublicDescription": "Counts all not software-prefetch load dispatches that hit the fill buffer (FB) allocated for the software prefetch. It can also be incremented by some lock instructions. So it should only be used with profiling so that the locks can be excluded by ASM (Assembly File) inspection of the nearby instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x4F", "UMask": "0x10", "EventName": "EPT.WALK_PENDING", "BriefDescription": "Counts 1 per cycle for each PMH that is busy with a EPT (Extended Page Table) walk for any request type.", "PublicDescription": "Counts cycles for each PMH (Page Miss Handler) that is busy with an EPT (Extended Page Table) walk for any request type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x51", "UMask": "0x01", "EventName": "L1D.REPLACEMENT", "BriefDescription": "L1D data line replacements", "PublicDescription": "Counts L1D data line replacements including opportunistic replacements, and replacements that require stall-for-replace or block-for-replace.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x01", "EventName": "TX_MEM.ABORT_CONFLICT", "BriefDescription": "Number of times a transactional abort was signaled due to a data conflict on a transactionally accessed address", "PublicDescription": "Number of times a TSX line had a cache conflict.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x02", "EventName": "TX_MEM.ABORT_CAPACITY", "BriefDescription": "Number of times a transactional abort was signaled due to a data capacity limitation for transactional reads or writes.", "PublicDescription": "Number of times a transactional abort was signaled due to a data capacity limitation for transactional reads or writes.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x04", "EventName": "TX_MEM.ABORT_HLE_STORE_TO_ELIDED_LOCK", "BriefDescription": "Number of times a HLE transactional region aborted due to a non XRELEASE prefixed instruction writing to an elided lock in the elision buffer", "PublicDescription": "Number of times a TSX Abort was triggered due to a non-release/commit store to lock.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x08", "EventName": "TX_MEM.ABORT_HLE_ELISION_BUFFER_NOT_EMPTY", "BriefDescription": "Number of times an HLE transactional execution aborted due to NoAllocatedElisionBuffer being non-zero.", "PublicDescription": "Number of times a TSX Abort was triggered due to commit but Lock Buffer not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x10", "EventName": "TX_MEM.ABORT_HLE_ELISION_BUFFER_MISMATCH", "BriefDescription": "Number of times an HLE transactional execution aborted due to XRELEASE lock not satisfying the address and value requirements in the elision buffer", "PublicDescription": "Number of times a TSX Abort was triggered due to release/commit but data and address mismatch.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x20", "EventName": "TX_MEM.ABORT_HLE_ELISION_BUFFER_UNSUPPORTED_ALIGNMENT", "BriefDescription": "Number of times an HLE transactional execution aborted due to an unsupported read alignment from the elision buffer.", "PublicDescription": "Number of times a TSX Abort was triggered due to attempting an unsupported alignment from Lock Buffer.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x54", "UMask": "0x40", "EventName": "TX_MEM.HLE_ELISION_BUFFER_FULL", "BriefDescription": "Number of times HLE lock could not be elided due to ElisionBufferAvailable being zero.", "PublicDescription": "Number of times we could not allocate <PERSON> Buffer.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x59", "UMask": "0x01", "EventName": "PARTIAL_RAT_STALLS.SCOREBOARD", "BriefDescription": "Cycles where the pipeline is stalled due to serializing operations.", "PublicDescription": "This event counts cycles during which the microcode scoreboard stalls happen.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5d", "UMask": "0x01", "EventName": "TX_EXEC.MISC1", "BriefDescription": "Counts the number of times a class of instructions that may cause a transactional abort was executed. Since this is the count of execution, it may not always cause a transactional abort.", "PublicDescription": "Counts the number of times a class of instructions that may cause a transactional abort was executed. Since this is the count of execution, it may not always cause a transactional abort.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5d", "UMask": "0x02", "EventName": "TX_EXEC.MISC2", "BriefDescription": "Counts the number of times a class of instructions (e.g., vzeroupper) that may cause a transactional abort was executed inside a transactional region", "PublicDescription": "Unfriendly TSX abort triggered by a vzeroupper instruction.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5d", "UMask": "0x04", "EventName": "TX_EXEC.MISC3", "BriefDescription": "Counts the number of times an instruction execution caused the transactional nest count supported to be exceeded", "PublicDescription": "Unfriendly TSX abort triggered by a nest count that is too deep.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5d", "UMask": "0x08", "EventName": "TX_EXEC.MISC4", "BriefDescription": "Counts the number of times a XBEGIN instruction was executed inside an HLE transactional region.", "PublicDescription": "RTM region detected inside HLE.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5d", "UMask": "0x10", "EventName": "TX_EXEC.MISC5", "BriefDescription": "Counts the number of times an HLE XACQUIRE instruction was executed inside an RTM transactional region", "PublicDescription": "Counts the number of times an HLE XACQUIRE instruction was executed inside an RTM transactional region.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5E", "UMask": "0x01", "EventName": "RS_EVENTS.EMPTY_END", "BriefDescription": "Counts end of periods where the Reservation Station (RS) was empty. Could be useful to precisely locate Frontend Latency Bound issues.", "PublicDescription": "Counts end of periods where the Reservation Station (RS) was empty. Could be useful to precisely locate front-end Latency Bound issues.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x5E", "UMask": "0x01", "EventName": "RS_EVENTS.EMPTY_CYCLES", "BriefDescription": "Cycles when Reservation Station (RS) is empty for the thread", "PublicDescription": "Counts cycles during which the reservation station (RS) is empty for the thread.; Note: In ST-mode, not active thread should drive 0. This is usually caused by severely costly branch mispredictions, or allocator/FE issues.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_DATA_RD", "BriefDescription": "Cycles when offcore outstanding Demand Data Read transactions are present in SuperQueue (SQ), queue to uncore", "PublicDescription": "Counts cycles when offcore outstanding Demand Data Read transactions are present in the super queue (SQ). A transaction is considered to be in the Offcore outstanding state between L2 miss and transaction completion sent to requestor (SQ de-allocation).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_DATA_RD", "BriefDescription": "Offcore outstanding Demand Data Read transactions in uncore queue.", "PublicDescription": "Counts the number of offcore outstanding Demand Data Read transactions in the super queue (SQ) every cycle. A transaction is considered to be in the Offcore outstanding state between L2 miss and transaction completion sent to requestor. See the corresponding Umask under OFFCORE_REQUESTS.Note: A prefetch promoted to Demand is counted from the promotion point.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_DATA_RD_GE_6", "BriefDescription": "Cycles with at least 6 offcore outstanding Demand Data Read transactions in uncore queue.", "PublicDescription": "Cycles with at least 6 offcore outstanding Demand Data Read transactions in uncore queue.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_CODE_RD", "BriefDescription": "Offcore outstanding Code Reads transactions in the SuperQueue (SQ), queue to uncore, every cycle. ", "PublicDescription": "Counts the number of offcore outstanding Code Reads transactions in the super queue every cycle. The 'Offcore outstanding' state of the transaction lasts from the L2 miss until the sending transaction completion to requestor (SQ deallocation). See the corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_CODE_RD", "BriefDescription": "Cycles with offcore outstanding Code Reads transactions in the SuperQueue (SQ), queue to uncore.", "PublicDescription": "Counts the number of offcore outstanding Code Reads transactions in the super queue every cycle. The 'Offcore outstanding' state of the transaction lasts from the L2 miss until the sending transaction completion to requestor (SQ deallocation). See the corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_RFO", "BriefDescription": "Offcore outstanding demand rfo reads transactions in SuperQueue (SQ), queue to uncore, every cycle", "PublicDescription": "Counts the number of offcore outstanding RFO (store) transactions in the super queue (SQ) every cycle. A transaction is considered to be in the Offcore outstanding state between L2 miss and transaction completion sent to requestor (SQ de-allocation). See corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_RFO", "BriefDescription": "Cycles with offcore outstanding demand rfo reads transactions in SuperQueue (SQ), queue to uncore.", "PublicDescription": "Counts the number of offcore outstanding demand rfo Reads transactions in the super queue every cycle. The 'Offcore outstanding' state of the transaction lasts from the L2 miss until the sending transaction completion to requestor (SQ deallocation). See the corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DATA_RD", "BriefDescription": "Cycles when offcore outstanding cacheable Core Data Read transactions are present in SuperQueue (SQ), queue to uncore.", "PublicDescription": "Counts cycles when offcore outstanding cacheable Core Data Read transactions are present in the super queue. A transaction is considered to be in the Offcore outstanding state between L2 miss and transaction completion sent to requestor (SQ de-allocation). See corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.ALL_DATA_RD", "BriefDescription": "Offcore outstanding cacheable Core Data Read transactions in SuperQueue (SQ), queue to uncore", "PublicDescription": "Counts the number of offcore outstanding cacheable Core Data Read transactions in the super queue every cycle. A transaction is considered to be in the Offcore outstanding state between L2 miss and transaction completion sent to requestor (SQ de-allocation). See corresponding Umask under OFFCORE_REQUESTS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x10", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.L3_MISS_DEMAND_DATA_RD", "BriefDescription": "Counts number of Offcore outstanding Demand Data Read requests that miss L3 cache in the superQ every cycle.", "PublicDescription": "Counts number of Offcore outstanding Demand Data Read requests that miss L3 cache in the superQ every cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x10", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.L3_MISS_DEMAND_DATA_RD_GE_6", "BriefDescription": "Cycles with at least 6 Demand Data Read requests that miss L3 cache in the superQ.", "PublicDescription": "Cycles with at least 6 Demand Data Read requests that miss L3 cache in the superQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x60", "UMask": "0x10", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_L3_MISS_DEMAND_DATA_RD", "BriefDescription": "Cycles with at least 1 Demand Data Read requests who miss L3 cache in the superQ.", "PublicDescription": "Cycles with at least 1 Demand Data Read requests who miss L3 cache in the superQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x04", "EventName": "IDQ.MITE_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from MITE path", "PublicDescription": "Counts cycles during which uops are being delivered to Instruction Decode Queue (IDQ) from the MITE path. Counting includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x04", "EventName": "IDQ.MITE_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) from MITE path", "PublicDescription": "Counts the number of uops delivered to Instruction Decode Queue (IDQ) from the MITE path. Counting includes uops that may 'bypass' the IDQ. This also means that uops are not being delivered from the Decode Stream Buffer (DSB).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x08", "EventName": "IDQ.DSB_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from Decode Stream Buffer (DSB) path", "PublicDescription": "Counts cycles during which uops are being delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path. Counting includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x08", "EventName": "IDQ.DSB_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path", "PublicDescription": "Counts the number of uops delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path. Counting includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x10", "EventName": "IDQ.MS_DSB_CYCLES", "BriefDescription": "Cycles when uops initiated by Decode Stream Buffer (DSB) are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Counts cycles during which uops initiated by Decode Stream Buffer (DSB) are being delivered to Instruction Decode Queue (IDQ) while the Microcode Sequencer (MS) is busy. Counting includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x18", "EventName": "IDQ.ALL_DSB_CYCLES_ANY_UOPS", "BriefDescription": "Cycles Decode Stream Buffer (DSB) is delivering any Uop", "PublicDescription": "Counts the number of cycles uops were delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path. Count includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x18", "EventName": "IDQ.ALL_DSB_CYCLES_4_UOPS", "BriefDescription": "Cycles Decode Stream Buffer (DSB) is delivering 4 Uops", "PublicDescription": "Counts the number of cycles 4 uops were delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path. Count includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x20", "EventName": "IDQ.MS_MITE_UOPS", "BriefDescription": "Uops initiated by MITE and delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Counts the number of uops initiated by MITE and delivered to Instruction Decode Queue (IDQ) while the Microcode Sequencer (MS) is busy. Counting includes uops that may 'bypass' the IDQ.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x24", "EventName": "IDQ.ALL_MITE_CYCLES_ANY_UOPS", "BriefDescription": "Cycles MITE is delivering any Uop", "PublicDescription": "Counts the number of cycles uops were delivered to the Instruction Decode Queue (IDQ) from the MITE (legacy decode pipeline) path. Counting includes uops that may 'bypass' the IDQ. During these cycles uops are not being delivered from the Decode Stream Buffer (DSB).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x24", "EventName": "IDQ.ALL_MITE_CYCLES_4_UOPS", "BriefDescription": "Cycles MITE is delivering 4 Uops", "PublicDescription": "Counts the number of cycles 4 uops were delivered to the Instruction Decode Queue (IDQ) from the MITE (legacy decode pipeline) path. Counting includes uops that may 'bypass' the IDQ. During these cycles uops are not being delivered from the Decode Stream Buffer (DSB).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Counts cycles during which uops are being delivered to Instruction Decode Queue (IDQ) while the Microcode Sequencer (MS) is busy. Counting includes uops that may 'bypass' the IDQ. Uops maybe initiated by Decode Stream Buffer (DSB) or MITE.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Counts the total number of uops delivered by the Microcode Sequencer (MS). Any instruction over 4 uops will be delivered by the MS. Some instructions such as transcendentals may additionally generate uops from the MS.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_SWITCHES", "BriefDescription": "Number of switches from DSB (Decode Stream Buffer) or MITE (legacy decode pipeline) to the Microcode Sequencer", "PublicDescription": "Number of switches from DSB (Decode Stream Buffer) or MITE (legacy decode pipeline) to the Microcode Sequencer.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x80", "UMask": "0x04", "EventName": "ICACHE_16B.IFDATA_STALL", "BriefDescription": "Cycles where a code fetch is stalled due to L1 instruction cache miss.", "PublicDescription": "Cycles where a code line fetch is stalled due to an L1 instruction cache miss. The legacy decode pipeline works at a 16 Byte granularity.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x83", "UMask": "0x01", "EventName": "ICACHE_64B.IFTAG_HIT", "BriefDescription": "Instruction fetch tag lookups that hit in the instruction cache (L1I). Counts at 64-byte cache-line granularity.", "PublicDescription": "Instruction fetch tag lookups that hit in the instruction cache (L1I). Counts at 64-byte cache-line granularity.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x83", "UMask": "0x02", "EventName": "ICACHE_64B.IFTAG_MISS", "BriefDescription": "Instruction fetch tag lookups that miss in the instruction cache (L1I). Counts at 64-byte cache-line granularity.", "PublicDescription": "Instruction fetch tag lookups that miss in the instruction cache (L1I). Counts at 64-byte cache-line granularity.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x83", "UMask": "0x04", "EventName": "ICACHE_64B.IFTAG_STALL", "BriefDescription": "Cycles where a code fetch is stalled due to L1 instruction cache tag miss.", "PublicDescription": "Cycles where a code fetch is stalled due to L1 instruction cache tag miss.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x01", "EventName": "ITLB_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Misses at all ITLB levels that cause page walks", "PublicDescription": "Counts page walks of any page size (4K/2M/4M/1G) caused by a code fetch. This implies it missed in the ITLB and further levels of TLB, but the walk need not have completed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x02", "EventName": "ITLB_MISSES.WALK_COMPLETED_4K", "BriefDescription": "Code miss in all TLB levels causes a page walk that completes. (4K)", "PublicDescription": "Counts completed page walks (4K page size) caused by a code fetch. This implies it missed in the ITLB and further levels of TLB. The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x04", "EventName": "ITLB_MISSES.WALK_COMPLETED_2M_4M", "BriefDescription": "Code miss in all TLB levels causes a page walk that completes. (2M/4M)", "PublicDescription": "Counts code misses in all ITLB levels that caused a completed page walk (2M and 4M page sizes). The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x08", "EventName": "ITLB_MISSES.WALK_COMPLETED_1G", "BriefDescription": "Code miss in all TLB levels causes a page walk that completes. (1G)", "PublicDescription": "Counts store misses in all DTLB levels that cause a completed page walk (1G page size). The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x0E", "EventName": "ITLB_MISSES.WALK_COMPLETED", "BriefDescription": "Code miss in all TLB levels causes a page walk that completes. (All page sizes)", "PublicDescription": "Counts completed page walks (2M and 4M page sizes) caused by a code fetch. This implies it missed in the ITLB and further levels of TLB. The page walk can end with or without a fault.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x10", "EventName": "ITLB_MISSES.WALK_PENDING", "BriefDescription": "Counts 1 per cycle for each PMH that is busy with a page walk for an instruction fetch request. EPT page walk duration are excluded in Skylake. ", "PublicDescription": "Counts 1 per cycle for each PMH (Page Miss <PERSON>) that is busy with a page walk for an instruction fetch request. EPT page walk duration are excluded in Skylake michroarchitecture. ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x10", "EventName": "ITLB_MISSES.WALK_ACTIVE", "BriefDescription": "Cycles when at least one PMH is busy with a page walk for code (instruction fetch) request. EPT page walk duration are excluded in Skylake.", "PublicDescription": "Cycles when at least one PMH is busy with a page walk for code (instruction fetch) request. EPT page walk duration are excluded in Skylake microarchitecture.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x85", "UMask": "0x20", "EventName": "ITLB_MISSES.STLB_HIT", "BriefDescription": "Instruction fetch requests that miss the ITLB and hit the STLB.", "PublicDescription": "Instruction fetch requests that miss the ITLB and hit the STLB.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x87", "UMask": "0x01", "EventName": "ILD_STALL.LCP", "BriefDescription": "Stalls caused by changing prefix length of the instruction.", "PublicDescription": "Counts cycles that the Instruction Length decoder (ILD) stalls occurred due to dynamically changing prefix length of the decoded instruction (by operand size prefix instruction 0x66, address size prefix instruction 0x67 or REX.W for Intel64). Count is proportional to the number of prefixes in a 16B-line. This may result in a three-cycle penalty for each LCP (Length changing prefix) in a 16-byte chunk.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_FE_WAS_OK", "BriefDescription": "Counts cycles FE delivered 4 uops or Resource Allocation Table (RAT) was stalling FE.", "PublicDescription": "Counts cycles FE delivered 4 uops or Resource Allocation Table (RAT) was stalling FE.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_3_UOP_DELIV.CORE", "BriefDescription": "Cycles with less than 3 uops delivered by the front end.", "PublicDescription": "Cycles with less than 3 uops delivered by the front-end.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_2_UOP_DELIV.CORE", "BriefDescription": "Cycles with less than 2 uops delivered by the front end.", "PublicDescription": "Cycles with less than 2 uops delivered by the front-end.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_1_UOP_DELIV.CORE", "BriefDescription": "Cycles per thread when 3 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled", "PublicDescription": "Counts, on the per-thread basis, cycles when less than 1 uop is delivered to Resource Allocation Table (RAT). IDQ_Uops_Not_Delivered.core >= 3.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_0_UOPS_DELIV.CORE", "BriefDescription": "Cycles per thread when 4 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled", "PublicDescription": "Counts, on the per-thread basis, cycles when no uops are delivered to Resource Allocation Table (RAT). IDQ_Uops_Not_Delivered.core =4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CORE", "BriefDescription": "Uops not delivered to Resource Allocation Table (RAT) per thread when backend of the machine is not stalled", "PublicDescription": "Counts the number of uops not delivered to Resource Allocation Table (RAT) per thread adding “4 – x” when Resource Allocation Table (RAT) is not stalled and Instruction Decode Queue (IDQ) delivers x uops to Resource Allocation Table (RAT) (where x belongs to {0,1,2,3}). Counting does not cover cases when: a. IDQ-Resource Allocation Table (RAT) pipe serves the other thread. b. Resource Allocation Table (RAT) is stalled for the thread (including uop drops and clear BE conditions).  c. Instruction Decode Queue (IDQ) delivers four uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x01", "EventName": "UOPS_DISPATCHED_PORT.PORT_0", "BriefDescription": "Cycles per thread when uops are executed in port 0", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x02", "EventName": "UOPS_DISPATCHED_PORT.PORT_1", "BriefDescription": "Cycles per thread when uops are executed in port 1", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x04", "EventName": "UOPS_DISPATCHED_PORT.PORT_2", "BriefDescription": "Cycles per thread when uops are executed in port 2", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x08", "EventName": "UOPS_DISPATCHED_PORT.PORT_3", "BriefDescription": "Cycles per thread when uops are executed in port 3", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 3.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x10", "EventName": "UOPS_DISPATCHED_PORT.PORT_4", "BriefDescription": "Cycles per thread when uops are executed in port 4", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x20", "EventName": "UOPS_DISPATCHED_PORT.PORT_5", "BriefDescription": "Cycles per thread when uops are executed in port 5", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 5.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x40", "EventName": "UOPS_DISPATCHED_PORT.PORT_6", "BriefDescription": "Cycles per thread when uops are executed in port 6", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 6.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA1", "UMask": "0x80", "EventName": "UOPS_DISPATCHED_PORT.PORT_7", "BriefDescription": "Cycles per thread when uops are executed in port 7", "PublicDescription": "Counts, on the per-thread basis, cycles during which at least one uop is dispatched from the Reservation Station (RS) to port 7.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xa2", "UMask": "0x01", "EventName": "RESOURCE_STALLS.ANY", "BriefDescription": "Resource-related stall cycles", "PublicDescription": "Counts resource-related stall cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA2", "UMask": "0x08", "EventName": "RESOURCE_STALLS.SB", "BriefDescription": "Cycles stalled due to no store buffers available. (not including draining form sync).", "PublicDescription": "Counts allocation stall cycles caused by the store buffer (SB) being full. This counts cycles that the pipeline back-end blocked uop delivery from the front-end.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x01", "EventName": "CYCLE_ACTIVITY.CYCLES_L2_MISS", "BriefDescription": "Cycles while L2 cache miss demand load is outstanding.", "PublicDescription": "Cycles while L2 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x02", "EventName": "CYCLE_ACTIVITY.CYCLES_L3_MISS", "BriefDescription": "Cycles while L3 cache miss demand load is outstanding.", "PublicDescription": "Cycles while L3 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x04", "EventName": "CYCLE_ACTIVITY.STALLS_TOTAL", "BriefDescription": "Total execution stalls.", "PublicDescription": "Total execution stalls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x05", "EventName": "CYCLE_ACTIVITY.STALLS_L2_MISS", "BriefDescription": "Execution stalls while L2 cache miss demand load is outstanding.", "PublicDescription": "Execution stalls while L2 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "5", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x06", "EventName": "CYCLE_ACTIVITY.STALLS_L3_MISS", "BriefDescription": "Execution stalls while L3 cache miss demand load is outstanding.", "PublicDescription": "Execution stalls while L3 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x08", "EventName": "CYCLE_ACTIVITY.CYCLES_L1D_MISS", "BriefDescription": "Cycles while L1 cache miss demand load is outstanding.", "PublicDescription": "Cycles while L1 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "8", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x0C", "EventName": "CYCLE_ACTIVITY.STALLS_L1D_MISS", "BriefDescription": "Execution stalls while L1 cache miss demand load is outstanding.", "PublicDescription": "Execution stalls while L1 cache miss demand load is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "12", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x10", "EventName": "CYCLE_ACTIVITY.CYCLES_MEM_ANY", "BriefDescription": "Cycles while memory subsystem has an outstanding load.", "PublicDescription": "Cycles while memory subsystem has an outstanding load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "16", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA3", "UMask": "0x14", "EventName": "CYCLE_ACTIVITY.STALLS_MEM_ANY", "BriefDescription": "Execution stalls while memory subsystem has an outstanding load.", "PublicDescription": "Execution stalls while memory subsystem has an outstanding load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "20", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x01", "EventName": "EXE_ACTIVITY.EXE_BOUND_0_PORTS", "BriefDescription": "Cycles where no uops were executed, the Reservation Station was not empty, the Store Buffer was full and there was no outstanding load.", "PublicDescription": "Counts cycles during which no uops were executed on all ports and Reservation Station (RS) was not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x02", "EventName": "EXE_ACTIVITY.1_PORTS_UTIL", "BriefDescription": "Cycles total of 1 uop is executed on all ports and Reservation Station was not empty.", "PublicDescription": "Counts cycles during which a total of 1 uop was executed on all ports and Reservation Station (RS) was not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x04", "EventName": "EXE_ACTIVITY.2_PORTS_UTIL", "BriefDescription": "Cycles total of 2 uops are executed on all ports and Reservation Station was not empty.", "PublicDescription": "Counts cycles during which a total of 2 uops were executed on all ports and Reservation Station (RS) was not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x08", "EventName": "EXE_ACTIVITY.3_PORTS_UTIL", "BriefDescription": "Cycles total of 3 uops are executed on all ports and Reservation Station was not empty.", "PublicDescription": "Cycles total of 3 uops are executed on all ports and Reservation Station (RS) was not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x10", "EventName": "EXE_ACTIVITY.4_PORTS_UTIL", "BriefDescription": "Cycles total of 4 uops are executed on all ports and Reservation Station was not empty.", "PublicDescription": "Cycles total of 4 uops are executed on all ports and Reservation Station (RS) was not empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA6", "UMask": "0x40", "EventName": "EXE_ACTIVITY.BOUND_ON_STORES", "BriefDescription": "Cycles where the Store Buffer was full and no outstanding load.", "PublicDescription": "Cycles where the Store Buffer was full and no outstanding load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.UOPS", "BriefDescription": "Number of Uops delivered by the LSD.", "PublicDescription": "Number of uops delivered to the back-end by the LSD(Loop Stream Detector).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.CYCLES_4_UOPS", "BriefDescription": "Cycles 4 Uops delivered by the LSD, but didn't come from the decoder.", "PublicDescription": "Counts the cycles when 4 uops are delivered by the LSD (Loop-stream detector).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.CYCLES_ACTIVE", "BriefDescription": "Cycles Uops delivered by the LSD, but didn't come from the decoder.", "PublicDescription": "Counts the cycles when at least one uop is delivered by the LSD (Loop-stream detector).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xAB", "UMask": "0x02", "EventName": "DSB2MITE_SWITCHES.PENALTY_CYCLES", "BriefDescription": "Decode Stream Buffer (DSB)-to-MITE switch true penalty cycles.", "PublicDescription": "Counts Decode Stream Buffer (DSB)-to-MITE switch true penalty cycles. These cycles do not include uops routed through because of the switch itself, for example, when Instruction Decode Queue (IDQ) pre-allocation is unavailable, or Instruction Decode Queue (IDQ) is full. SBD-to-MITE switch true penalty cycles happen after the merge mux (MM) receives Decode Stream Buffer (DSB) Sync-indication until receiving the first MITE uop. MM is placed before Instruction Decode Queue (IDQ) to merge uops being fed from the MITE and Decode Stream Buffer (DSB) paths. Decode Stream Buffer (DSB) inserts the Sync-indication whenever a Decode Stream Buffer (DSB)-to-MITE switch occurs.Penalty: A Decode Stream Buffer (DSB) hit followed by a Decode Stream Buffer (DSB) miss can cost up to six cycles in which no uops are delivered to the IDQ. Most often, such switches from the Decode Stream Buffer (DSB) to the legacy pipeline cost 0–2 cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xAE", "UMask": "0x01", "EventName": "ITLB.ITLB_FLUSH", "BriefDescription": "Flushing of the Instruction TLB (ITLB) pages, includes 4k/2M/4M pages.", "PublicDescription": "Counts the number of flushes of the big or small ITLB pages. Counting include both TLB Flush (covering all sets) and TLB Set Clear (set-specific).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS.DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests sent to uncore", "PublicDescription": "Counts the Demand Data Read requests sent to uncore. Use it in conjunction with OFFCORE_REQUESTS_OUTSTANDING to determine average latency in the uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS.DEMAND_CODE_RD", "BriefDescription": "Cacheable and noncachaeble code read requests", "PublicDescription": "Counts both cacheable and non-cacheable code read requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS.DEMAND_RFO", "BriefDescription": "Demand RFO requests including regular RFOs, locks, ItoM", "PublicDescription": "Counts the demand RFO (read for ownership) requests including regular RFOs, locks, ItoM.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS.ALL_DATA_RD", "BriefDescription": "Demand and prefetch data reads", "PublicDescription": "Counts the demand and prefetch data reads. All Core Data Reads include cacheable 'Demands' and L2 prefetchers (not L3 prefetchers). Counting also covers reads due to page walks resulted from any request type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x10", "EventName": "OFFCORE_REQUESTS.L3_MISS_DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests who miss L3 cache", "PublicDescription": "Demand Data Read requests who miss L3 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB0", "UMask": "0x80", "EventName": "OFFCORE_REQUESTS.ALL_REQUESTS", "BriefDescription": "Any memory transaction that reached the SQ.", "PublicDescription": "Counts memory transactions reached the super queue including requests initiated by the core, all L3 prefetches, page walks, etc..", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_4_UOPS_EXEC", "BriefDescription": "Cycles where at least 4 uops were executed per-thread", "PublicDescription": "Cycles where at least 4 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_3_UOPS_EXEC", "BriefDescription": "Cycles where at least 3 uops were executed per-thread", "PublicDescription": "Cycles where at least 3 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_2_UOPS_EXEC", "BriefDescription": "Cycles where at least 2 uops were executed per-thread", "PublicDescription": "Cycles where at least 2 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_1_UOP_EXEC", "BriefDescription": "Cycles where at least 1 uop was executed per-thread", "PublicDescription": "Cycles where at least 1 uop was executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.STALL_CYCLES", "BriefDescription": "Counts number of cycles no uops were dispatched to be executed on this thread.", "PublicDescription": "Counts cycles during which no uops were dispatched from the Reservation Station (RS) per thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.THREAD", "BriefDescription": "Counts the number of uops to be executed per-thread each cycle.", "PublicDescription": "Number of uops to be executed per-thread each cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE", "BriefDescription": "Number of uops executed on the core.", "PublicDescription": "Number of uops executed from any thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_NONE", "BriefDescription": "Cycles with no micro-ops executed from any thread on physical core.", "PublicDescription": "Cycles with no micro-ops executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_4", "BriefDescription": "Cycles at least 4 micro-op is executed from any thread on physical core.", "PublicDescription": "Cycles at least 4 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_3", "BriefDescription": "Cycles at least 3 micro-op is executed from any thread on physical core.", "PublicDescription": "Cycles at least 3 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_2", "BriefDescription": "Cycles at least 2 micro-op is executed from any thread on physical core.", "PublicDescription": "Cycles at least 2 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_1", "BriefDescription": "Cycles at least 1 micro-op is executed from any thread on physical core.", "PublicDescription": "Cycles at least 1 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB1", "UMask": "0x10", "EventName": "UOPS_EXECUTED.X87", "BriefDescription": "Counts the number of x87 uops dispatched.", "PublicDescription": "Counts the number of x87 uops executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB2", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_BUFFER.SQ_FULL", "BriefDescription": "Offcore requests buffer cannot take more entries for this thread core.", "PublicDescription": "Counts the number of cases when the offcore requests buffer cannot take more entries for the core. This can happen when the superqueue does not contain eligible entries, or when L1D writeback pending FIFO requests is full.Note: Writeback pending FIFO has six entries.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE", "BriefDescription": "Offcore response can be programmed only with a specific pair of event select and counter MSR, and with specific event codes and predefine mask bit value in a dedicated MSR to specify attributes of the offcore transaction", "PublicDescription": "Offcore response can be programmed only with a specific pair of event select and counter MSR, and with specific event codes and predefine mask bit value in a dedicated MSR to specify attributes of the offcore transaction.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xBD", "UMask": "0x01", "EventName": "TLB_FLUSH.DTLB_THREAD", "BriefDescription": "DTLB flush attempts of the thread-specific entries", "PublicDescription": "Counts the number of DTLB flush attempts of the thread-specific entries.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xBD", "UMask": "0x20", "EventName": "TLB_FLUSH.STLB_ANY", "BriefDescription": "STLB flush attempts", "PublicDescription": "Counts the number of any STLB flush attempts (such as entire, VPID, PCID, InvPage, CR3 write, etc.).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC0", "UMask": "0x00", "EventName": "INST_RETIRED.ANY_P", "BriefDescription": "Number of instructions retired. General Counter - architectural event", "PublicDescription": "Counts the number of instructions (EOMs) retired. Counting covers macro-fused instructions individually (that is, increments by two).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091, SKL044", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC0", "UMask": "0x01", "EventName": "INST_RETIRED.PREC_DIST", "BriefDescription": "Precise instruction retired event with HW to reduce effect of PEBS shadow in IP distribution", "PublicDescription": "A version of INST_RETIRED that allows for a more unbiased distribution of samples across instructions retired. It utilizes the Precise Distribution of Instructions Retired (PDIR) feature to mitigate some bias in how retired instructions get sampled.", "Counter": "1", "CounterHTOff": "1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091, SKL044", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC0", "UMask": "0x01", "EventName": "INST_RETIRED.TOTAL_CYCLES_PS", "BriefDescription": "Number of cycles using always true condition applied to  PEBS instructions retired event.", "PublicDescription": "Number of cycles using an always true condition applied to  PEBS instructions retired event. (inst_ret< 16)", "Counter": "0,2,3", "CounterHTOff": "0,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "10", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091, SKL044", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC1", "UMask": "0x3F", "EventName": "OTHER_ASSISTS.ANY", "BriefDescription": "Number of times a microcode assist is invoked by HW other than FP-assist. Examples include AD (page Access Dirty) and AVX* related assists.", "PublicDescription": "Number of times a microcode assist is invoked by HW other than FP-assist. Examples include AD (page Access Dirty) and AVX* related assists.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC2", "UMask": "0x02", "EventName": "UOPS_RETIRED.TOTAL_CYCLES", "BriefDescription": "Cycles with less than 10 actually retired uops.", "PublicDescription": "Number of cycles using always true condition (uops_ret < 16) applied to non PEBS uops retired event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "10", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC2", "UMask": "0x02", "EventName": "UOPS_RETIRED.STALL_CYCLES", "BriefDescription": "Cycles without actually retired uops.", "PublicDescription": "This event counts cycles without actually retired uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC2", "UMask": "0x02", "EventName": "UOPS_RETIRED.RETIRE_SLOTS", "BriefDescription": "Retirement slots used.", "PublicDescription": "Counts the retirement slots used.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC3", "UMask": "0x01", "EventName": "MACHINE_CLEARS.COUNT", "BriefDescription": "Number of machine clears (nukes) of any type. ", "PublicDescription": "Number of machine clears (nukes) of any type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC3", "UMask": "0x02", "EventName": "MACHINE_CLEARS.MEMORY_ORDERING", "BriefDescription": "Counts the number of machine clears due to memory order conflicts.", "PublicDescription": "Counts the number of memory ordering Machine Clears detected. Memory Ordering Machine Clears can result from one of the following:a. memory disambiguation,b. external snoop, orc. cross SMT-HW-thread snoop (stores) hitting load buffer.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL089", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC3", "UMask": "0x04", "EventName": "MACHINE_CLEARS.SMC", "BriefDescription": "Self-modifying code (SMC) detected.", "PublicDescription": "Counts self-modifying code (SMC) detected, which causes a machine clear.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x00", "EventName": "BR_INST_RETIRED.ALL_BRANCHES", "BriefDescription": "All (macro) branch instructions retired.", "PublicDescription": "Counts all (macro) branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x01", "EventName": "BR_INST_RETIRED.CONDITIONAL", "BriefDescription": "Conditional branch instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts conditional branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x02", "EventName": "BR_INST_RETIRED.NEAR_CALL", "BriefDescription": "Direct and indirect near call instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts both direct and indirect near call instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x04", "EventName": "BR_INST_RETIRED.ALL_BRANCHES_PEBS", "BriefDescription": "All (macro) branch instructions retired. ", "PublicDescription": "This is a precise version of BR_INST_RETIRED.ALL_BRANCHES that counts all (macro) branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x08", "EventName": "BR_INST_RETIRED.NEAR_RETURN", "BriefDescription": "Return instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts return instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x10", "EventName": "BR_INST_RETIRED.NOT_TAKEN", "BriefDescription": "Counts all not taken macro branch instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts not taken branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x20", "EventName": "BR_INST_RETIRED.NEAR_TAKEN", "BriefDescription": "Taken branch instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts taken branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC4", "UMask": "0x40", "EventName": "BR_INST_RETIRED.FAR_BRANCH", "BriefDescription": "Counts the number of far branch instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts far branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "SKL091", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC5", "UMask": "0x00", "EventName": "BR_MISP_RETIRED.ALL_BRANCHES", "BriefDescription": "All mispredicted macro branch instructions retired.", "PublicDescription": "Counts all the retired branch instructions that were mispredicted by the processor. A branch misprediction occurs when the processor incorrectly predicts the destination of the branch.  When the misprediction is discovered at execution, all the instructions executed in the wrong (speculative) path must be discarded, and the processor must start fetching from the correct path.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC5", "UMask": "0x01", "EventName": "BR_MISP_RETIRED.CONDITIONAL", "BriefDescription": "Mispredicted conditional branch instructions retired.", "PublicDescription": "This is a precise version (that is, uses PEBS) of the event that counts mispredicted conditional branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC5", "UMask": "0x02", "EventName": "BR_MISP_RETIRED.NEAR_CALL", "BriefDescription": "Mispredicted direct and indirect near call instructions retired.", "PublicDescription": "This event counts both taken and not taken retired mispredicted direct and indirect near calls, including both register and memory indirect.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC5", "UMask": "0x04", "EventName": "BR_MISP_RETIRED.ALL_BRANCHES_PEBS", "BriefDescription": "Mispredicted macro branch instructions retired. ", "PublicDescription": "This is a precise version of BR_MISP_RETIRED.ALL_BRANCHES that counts all mispredicted macro branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC5", "UMask": "0x20", "EventName": "BR_MISP_RETIRED.NEAR_TAKEN", "BriefDescription": "Number of near branch instructions retired that were mispredicted and taken. ", "PublicDescription": "Number of near branch instructions retired that were mispredicted and taken.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_4", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 4 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 4 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x400406", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_2_BUBBLES_GE_2", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end had at least 2 bubble-slots for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end had at least 2 bubble-slots for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x200206", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_2", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x400206", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.STLB_MISS", "BriefDescription": "Retired Instructions who experienced STLB (2nd level TLB) true miss. Precise Event.", "PublicDescription": "Counts retired Instructions that experienced STLB (2nd level TLB) true miss.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x15", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.ITLB_MISS", "BriefDescription": "Retired Instructions who experienced iTLB true miss. Precise Event.", "PublicDescription": "Counts retired Instructions that experienced iTLB (Instruction TLB) true miss.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x14", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.L2_MISS", "BriefDescription": "Retired Instructions who experienced Instruction L2 Cache true miss. Precise Event.", "PublicDescription": "Retired Instructions who experienced Instruction L2 Cache true miss. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x13", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.L1I_MISS", "BriefDescription": "Retired Instructions who experienced Instruction L1 Cache true miss. Precise Event.", "PublicDescription": "Retired Instructions who experienced Instruction L1 Cache true miss. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x12", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.DSB_MISS", "BriefDescription": "Retired Instructions who experienced decode stream buffer (DSB - the decoded instruction-cache) miss. Precise Event.", "PublicDescription": "Counts retired Instructions that experienced DSB (Decode stream buffer i.e. the decoded instruction-cache) miss. \r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x11", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_2_BUBBLES_GE_3", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end had at least 3 bubble-slots for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end had at least 3 bubble-slots for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x300206", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_2_BUBBLES_GE_1", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end had at least 1 bubble-slot for a period of 2 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Counts retired instructions that are delivered to the back-end after the front-end had at least 1 bubble-slot for a period of 2 cycles. A bubble-slot is an empty issue-pipeline slot while there was no RAT stall.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x100206", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_512", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 512 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 512 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x420006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_256", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 256 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 256 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x410006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_128", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 128 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 128 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x408006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_64", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 64 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 64 cycles which was not interrupted by a back-end stall. Precise Event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x404006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_32", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 32 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Counts retired instructions that are delivered to the back-end  after a front-end stall of at least 32 cycles. During this period the front-end delivered no uops.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x402006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_16", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 16 cycles which was not interrupted by a back-end stall. Precise Event.", "PublicDescription": "Counts retired instructions that are delivered to the back-end after a front-end stall of at least 16 cycles. During this period the front-end delivered no uops.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x401006", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC6", "UMask": "0x01", "EventName": "FRONTEND_RETIRED.LATENCY_GE_8", "BriefDescription": "Retired instructions that are fetched after an interval where the front-end delivered no uops for a period of 8 cycles which was not interrupted by a back-end stall.", "PublicDescription": "Counts retired instructions that are delivered to the back-end after a front-end stall of at least 8 cycles. During this period the front-end delivered no uops. \r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F7", "MSRValue": "0x400806", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x01", "EventName": "FP_ARITH_INST_RETIRED.SCALAR_DOUBLE", "BriefDescription": "Number of SSE/AVX computational scalar double precision floating-point instructions retired.  Each count represents 1 computation. Applies to SSE* and AVX* scalar double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT FM(N)ADD/SUB.  FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational scalar double precision floating-point instructions retired.  Each count represents 1 computation. Applies to SSE* and AVX* scalar double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT FM(N)ADD/SUB.  FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x02", "EventName": "FP_ARITH_INST_RETIRED.SCALAR_SINGLE", "BriefDescription": "Number of SSE/AVX computational scalar single precision floating-point instructions retired.  Each count represents 1 computation. Applies to SSE* and AVX* scalar single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT FM(N)ADD/SUB.  FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational scalar single precision floating-point instructions retired.  Each count represents 1 computation. Applies to SSE* and AVX* scalar single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT FM(N)ADD/SUB.  FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x04", "EventName": "FP_ARITH_INST_RETIRED.128B_PACKED_DOUBLE", "BriefDescription": "Number of SSE/AVX computational 128-bit packed double precision floating-point instructions retired.  Each count represents 2 computations. Applies to SSE* and AVX* packed double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational 128-bit packed double precision floating-point instructions retired.  Each count represents 2 computations. Applies to SSE* and AVX* packed double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x08", "EventName": "FP_ARITH_INST_RETIRED.128B_PACKED_SINGLE", "BriefDescription": "Number of SSE/AVX computational 128-bit packed single precision floating-point instructions retired.  Each count represents 4 computations. Applies to SSE* and AVX* packed single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational 128-bit packed single precision floating-point instructions retired.  Each count represents 4 computations. Applies to SSE* and AVX* packed single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x10", "EventName": "FP_ARITH_INST_RETIRED.256B_PACKED_DOUBLE", "BriefDescription": "Number of SSE/AVX computational 256-bit packed double precision floating-point instructions retired.  Each count represents 4 computations. Applies to SSE* and AVX* packed double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational 256-bit packed double precision floating-point instructions retired.  Each count represents 4 computations. Applies to SSE* and AVX* packed double precision floating-point instructions: ADD SUB MUL DIV MIN MAX SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x20", "EventName": "FP_ARITH_INST_RETIRED.256B_PACKED_SINGLE", "BriefDescription": "Number of SSE/AVX computational 256-bit packed single precision floating-point instructions retired.  Each count represents 8 computations. Applies to SSE* and AVX* packed single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "PublicDescription": "Number of SSE/AVX computational 256-bit packed single precision floating-point instructions retired.  Each count represents 8 computations. Applies to SSE* and AVX* packed single precision floating-point instructions: ADD SUB MUL DIV MIN MAX RCP RSQRT SQRT DPP FM(N)ADD/SUB.  DPP and FM(N)ADD/SUB instructions count twice as they perform multiple calculations per element.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x40", "EventName": "FP_ARITH_INST_RETIRED.512B_PACKED_DOUBLE", "BriefDescription": "Number of Packed Double-Precision FP arithmetic instructions (Use operation multiplier of 8)", "PublicDescription": "Number of Packed Double-Precision FP arithmetic instructions (Use operation multiplier of 8)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC7", "UMask": "0x80", "EventName": "FP_ARITH_INST_RETIRED.512B_PACKED_SINGLE", "BriefDescription": "Number of Packed Single-Precision FP arithmetic instructions (Use operation multiplier of 16)", "PublicDescription": "Number of Packed Single-Precision FP arithmetic instructions (Use operation multiplier of 16)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x01", "EventName": "HLE_RETIRED.START", "BriefDescription": "Number of times an HLE execution started.", "PublicDescription": "Number of times we entered an HLE region. Does not count nested transactions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x02", "EventName": "HLE_RETIRED.COMMIT", "BriefDescription": "Number of times an HLE execution successfully committed", "PublicDescription": "Number of times HLE commit succeeded.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x04", "EventName": "HLE_RETIRED.ABORTED", "BriefDescription": "Number of times an HLE execution aborted due to any reasons (multiple categories may count as one). ", "PublicDescription": "Number of times HLE abort was triggered. (PEBS)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x08", "EventName": "HLE_RETIRED.ABORTED_MEM", "BriefDescription": "Number of times an HLE execution aborted due to various memory events (e.g., read/write capacity and conflicts).", "PublicDescription": "Number of times an HLE execution aborted due to various memory events (e.g., read/write capacity and conflicts).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x10", "EventName": "HLE_RETIRED.ABORTED_TIMER", "BriefDescription": "Number of times an HLE execution aborted due to hardware timer expiration.", "PublicDescription": "Number of times an HLE execution aborted due to hardware timer expiration.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x20", "EventName": "HLE_RETIRED.ABORTED_UNFRIENDLY", "BriefDescription": "Number of times an HLE execution aborted due to HLE-unfriendly instructions and certain unfriendly events (such as AD assists etc.). ", "PublicDescription": "Number of times an HLE execution aborted due to HLE-unfriendly instructions and certain unfriendly events (such as AD assists etc.).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x40", "EventName": "HLE_RETIRED.ABORTED_MEMTYPE", "BriefDescription": "Number of times an HLE execution aborted due to incompatible memory type", "PublicDescription": "Number of times an HLE execution aborted due to incompatible memory type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC8", "UMask": "0x80", "EventName": "HLE_RETIRED.ABORTED_EVENTS", "BriefDescription": "Number of times an HLE execution aborted due to unfriendly events (such as interrupts).", "PublicDescription": "Number of times an HLE execution aborted due to unfriendly events (such as interrupts).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x01", "EventName": "RTM_RETIRED.START", "BriefDescription": "Number of times an RTM execution started.", "PublicDescription": "Number of times we entered an RTM region. Does not count nested transactions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x02", "EventName": "RTM_RETIRED.COMMIT", "BriefDescription": "Number of times an RTM execution successfully committed", "PublicDescription": "Number of times RTM commit succeeded.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x04", "EventName": "RTM_RETIRED.ABORTED", "BriefDescription": "Number of times an RTM execution aborted due to any reasons (multiple categories may count as one). ", "PublicDescription": "Number of times RTM abort was triggered. (PEBS)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x08", "EventName": "RTM_RETIRED.ABORTED_MEM", "BriefDescription": "Number of times an RTM execution aborted due to various memory events (e.g. read/write capacity and conflicts)", "PublicDescription": "Number of times an RTM execution aborted due to various memory events (e.g. read/write capacity and conflicts).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x10", "EventName": "RTM_RETIRED.ABORTED_TIMER", "BriefDescription": "Number of times an RTM execution aborted due to uncommon conditions.", "PublicDescription": "Number of times an RTM execution aborted due to uncommon conditions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x20", "EventName": "RTM_RETIRED.ABORTED_UNFRIENDLY", "BriefDescription": "Number of times an RTM execution aborted due to HLE-unfriendly instructions", "PublicDescription": "Number of times an RTM execution aborted due to HLE-unfriendly instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x40", "EventName": "RTM_RETIRED.ABORTED_MEMTYPE", "BriefDescription": "Number of times an RTM execution aborted due to incompatible memory type", "PublicDescription": "Number of times an RTM execution aborted due to incompatible memory type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xC9", "UMask": "0x80", "EventName": "RTM_RETIRED.ABORTED_EVENTS", "BriefDescription": "Number of times an RTM execution aborted due to none of the previous 4 categories (e.g. interrupt)", "PublicDescription": "Number of times an RTM execution aborted due to none of the previous 4 categories (e.g. interrupt).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCA", "UMask": "0x1E", "EventName": "FP_ASSIST.ANY", "BriefDescription": "Cycles with any input/output SSE or FP assist", "PublicDescription": "Counts cycles with any input and output SSE or x87 FP assist. If an input and output assist are detected on the same cycle the event increments by 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCB", "UMask": "0x01", "EventName": "HW_INTERRUPTS.RECEIVED", "BriefDescription": "Number of hardware interrupts received by the processor.", "PublicDescription": "Counts the number of hardware interruptions received by the processor.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "203", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCC", "UMask": "0x20", "EventName": "ROB_MISC_EVENTS.LBR_INSERTS", "BriefDescription": "Increments whenever there is an update to the LBR array.", "PublicDescription": "Increments when an entry is added to the Last Branch Record (LBR) array (or removed from the array in case of RETURNs in call stack mode). The event requires LBR enable via IA32_DEBUGCTL MSR and branch type selection via MSR_LBR_SELECT.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCC", "UMask": "0x40", "EventName": "ROB_MISC_EVENTS.PAUSE_INST", "BriefDescription": "Number of retired PAUSE instructions (that do not end up with a VMExit to the VMM; TSX aborted Instructions may be counted). This event is not supported on first SKL and KBL products.", "PublicDescription": "Number of retired PAUSE instructions (that do not end up with a VMExit to the VMM; TSX aborted Instructions may be counted). This event is not supported on first SKL and KBL products.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_512", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 512 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 512 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "101", "MSRIndex": "0x3F6", "MSRValue": "0x200", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_256", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 256 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 256 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "503", "MSRIndex": "0x3F6", "MSRValue": "0x100", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_128", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 128 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 128 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "1009", "MSRIndex": "0x3F6", "MSRValue": "0x80", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_64", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 64 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 64 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2003", "MSRIndex": "0x3F6", "MSRValue": "0x40", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_32", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 32 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 32 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0x3F6", "MSRValue": "0x20", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_16", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 16 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 16 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0x3F6", "MSRValue": "0x10", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_8", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 8 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 8 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "50021", "MSRIndex": "0x3F6", "MSRValue": "0x8", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_4", "BriefDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 4 cycles.", "PublicDescription": "Counts randomly selected loads when the latency from first dispatch to completion is greater than 4 cycles.  Reported latency may be longer than just the memory latency.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x3F6", "MSRValue": "0x4", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x11", "EventName": "MEM_INST_RETIRED.STLB_MISS_LOADS", "BriefDescription": "Retired load instructions that miss the STLB. (Precise Event)", "PublicDescription": "Retired load instructions that miss the STLB.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x12", "EventName": "MEM_INST_RETIRED.STLB_MISS_STORES", "BriefDescription": "Retired store instructions that miss the STLB. (Precise Event)", "PublicDescription": "Retired store instructions that miss the STLB.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "1", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x21", "EventName": "MEM_INST_RETIRED.LOCK_LOADS", "BriefDescription": "Retired load instructions with locked access. (Precise Event)", "PublicDescription": "Retired load instructions with locked access. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x41", "EventName": "MEM_INST_RETIRED.SPLIT_LOADS", "BriefDescription": "Retired load instructions that split across a cacheline boundary. (Precise Event)", "PublicDescription": "Retired load instructions that split across a cacheline boundary. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x42", "EventName": "MEM_INST_RETIRED.SPLIT_STORES", "BriefDescription": "Retired store instructions that split across a cacheline boundary. (Precise Event)", "PublicDescription": "Retired store instructions that split across a cacheline boundary. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "1", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x81", "EventName": "MEM_INST_RETIRED.ALL_LOADS", "BriefDescription": "All retired load instructions. (Precise Event)", "PublicDescription": "All retired load instructions. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD0", "UMask": "0x82", "EventName": "MEM_INST_RETIRED.ALL_STORES", "BriefDescription": "All retired store instructions. (Precise Event)", "PublicDescription": "All retired store instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "1", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x01", "EventName": "MEM_LOAD_RETIRED.L1_HIT", "BriefDescription": "Retired load instructions with L1 cache hits as data sources", "PublicDescription": "Counts retired load instructions with at least one uop that hit in the L1 data cache. This event includes all SW prefetches and lock instructions regardless of the data source.\r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x02", "EventName": "MEM_LOAD_RETIRED.L2_HIT", "BriefDescription": "Retired load instructions with L2 cache hits as data sources", "PublicDescription": "Retired load instructions with L2 cache hits as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x04", "EventName": "MEM_LOAD_RETIRED.L3_HIT", "BriefDescription": "Retired load instructions with L3 cache hits as data sources", "PublicDescription": "Retired load instructions with L3 cache hits as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "50021", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x08", "EventName": "MEM_LOAD_RETIRED.L1_MISS", "BriefDescription": "Retired load instructions missed L1 cache as data sources", "PublicDescription": "Counts retired load instructions with at least one uop that missed in the L1 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x10", "EventName": "MEM_LOAD_RETIRED.L2_MISS", "BriefDescription": "Retired load instructions missed L2 cache as data sources", "PublicDescription": "Retired load instructions missed L2 cache as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "50021", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x20", "EventName": "MEM_LOAD_RETIRED.L3_MISS", "BriefDescription": "Retired load instructions missed L3 cache as data sources", "PublicDescription": "Retired load instructions missed L3 cache as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD1", "UMask": "0x40", "EventName": "MEM_LOAD_RETIRED.FB_HIT", "BriefDescription": "Retired load instructions which data sources were load missed L1 but hit FB due to preceding miss to the same cache line with data not ready", "PublicDescription": "Counts retired load instructions with at least one uop was load missed in L1 but hit FB (Fill Buffers) due to preceding miss to the same cache line with data not ready. \r\n", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD2", "UMask": "0x01", "EventName": "MEM_LOAD_L3_HIT_RETIRED.XSNP_MISS", "BriefDescription": "Retired load instructions which data sources were L3 hit and cross-core snoop missed in on-pkg core cache.", "PublicDescription": "Retired load instructions which data sources were L3 hit and cross-core snoop missed in on-pkg core cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD2", "UMask": "0x02", "EventName": "MEM_LOAD_L3_HIT_RETIRED.XSNP_HIT", "BriefDescription": "Retired load instructions which data sources were L3 and cross-core snoop hits in on-pkg core cache", "PublicDescription": "Retired load instructions which data sources were L3 and cross-core snoop hits in on-pkg core cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD2", "UMask": "0x04", "EventName": "MEM_LOAD_L3_HIT_RETIRED.XSNP_HITM", "BriefDescription": "Retired load instructions which data sources were HitM responses from shared L3", "PublicDescription": "Retired load instructions which data sources were HitM responses from shared L3.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD2", "UMask": "0x08", "EventName": "MEM_LOAD_L3_HIT_RETIRED.XSNP_NONE", "BriefDescription": "Retired load instructions which data sources were hits in L3 without snoops required", "PublicDescription": "Retired load instructions which data sources were hits in L3 without snoops required.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD3", "UMask": "0x01", "EventName": "MEM_LOAD_L3_MISS_RETIRED.LOCAL_DRAM", "BriefDescription": "Retired load instructions which data sources missed L3 but serviced from local dram", "PublicDescription": "Retired load instructions which data sources missed L3 but serviced from local dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD3", "UMask": "0x02", "EventName": "MEM_LOAD_L3_MISS_RETIRED.REMOTE_DRAM", "BriefDescription": "Retired load instructions which data sources missed L3 but serviced from remote dram", "PublicDescription": "Retired load instructions which data sources missed L3 but serviced from remote dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD3", "UMask": "0x04", "EventName": "MEM_LOAD_L3_MISS_RETIRED.REMOTE_HITM", "BriefDescription": "Retired load instructions whose data sources was remote HITM", "PublicDescription": "Retired load instructions whose data sources was remote HITM", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD3", "UMask": "0x08", "EventName": "MEM_LOAD_L3_MISS_RETIRED.REMOTE_FWD", "BriefDescription": "Retired load instructions whose data sources was forwarded from a remote cache", "PublicDescription": "Retired load instructions whose data sources was forwarded from a remote cache", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xD4", "UMask": "0x04", "EventName": "MEM_LOAD_MISC_RETIRED.UC", "BriefDescription": "Retired instructions with at least 1 uncacheable load or lock.", "PublicDescription": "Retired instructions with at least 1 uncacheable load or lock.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Data_LA": "1", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xE6", "UMask": "0x01", "EventName": "BACLEARS.ANY", "BriefDescription": "Counts the total number when the front end is resteered, mainly when the BPU cannot provide a correct prediction and this is corrected by other branch handling mechanisms at the front end.", "PublicDescription": "Counts the number of times the front-end is resteered when it finds a branch instruction in a fetch line. This occurs for the first time a branch instruction is fetched or when the branch is not tracked by the BPU (Branch Prediction Unit) anymore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x01", "EventName": "CORE_SNOOP_RESPONSE.RSP_IHITI", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x02", "EventName": "CORE_SNOOP_RESPONSE.RSP_IHITFSE", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x04", "EventName": "CORE_SNOOP_RESPONSE.RSP_SHITFSE", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x08", "EventName": "CORE_SNOOP_RESPONSE.RSP_SFWDM", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x10", "EventName": "CORE_SNOOP_RESPONSE.RSP_IFWDM", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x20", "EventName": "CORE_SNOOP_RESPONSE.RSP_IFWDFE", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xEF", "UMask": "0x40", "EventName": "CORE_SNOOP_RESPONSE.RSP_SFWDFE", "BriefDescription": "tbd", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF0", "UMask": "0x40", "EventName": "L2_TRANS.L2_WB", "BriefDescription": "L2 writebacks that access L2 cache", "PublicDescription": "Counts L2 writebacks that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF1", "UMask": "0x1F", "EventName": "L2_LINES_IN.ALL", "BriefDescription": "L2 cache lines filling L2", "PublicDescription": "Counts the number of L2 cache lines filling the L2. Counting does not cover rejects.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF2", "UMask": "0x01", "EventName": "L2_LINES_OUT.SILENT", "BriefDescription": "Counts the number of lines that are silently dropped by L2 cache when triggered by an L2 cache fill. These lines are typically in Shared state. A non-threaded event.", "PublicDescription": "Counts the number of lines that are silently dropped by L2 cache when triggered by an L2 cache fill. These lines are typically in Shared or Exclusive state. A non-threaded event.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF2", "UMask": "0x02", "EventName": "L2_LINES_OUT.NON_SILENT", "BriefDescription": "Counts the number of lines that are evicted by L2 cache when triggered by an L2 cache fill. Those lines can be either in modified state or clean state. Modified lines may either be written back to L3 or directly written to memory and not allocated in L3.  Clean lines may either be allocated in L3 or dropped", "PublicDescription": "Counts the number of lines that are evicted by L2 cache when triggered by an L2 cache fill. Those lines are in Modified state. Modified lines are written back to L3", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF2", "UMask": "0x04", "EventName": "L2_LINES_OUT.USELESS_PREF", "BriefDescription": "\nThis event is deprecated. Refer to new event L2_LINES_OUT.USELESS_HWPF", "PublicDescription": "This event is deprecated. Refer to new event L2_LINES_OUT.USELESS_HWPF", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "1"}, {"EventCode": "0xF2", "UMask": "0x04", "EventName": "L2_LINES_OUT.USELESS_HWPF", "BriefDescription": "Counts the number of lines that have been hardware prefetched but not used and now evicted by L2 cache", "PublicDescription": "Counts the number of lines that have been hardware prefetched but not used and now evicted by L2 cache", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xF4", "UMask": "0x10", "EventName": "SQ_MISC.SPLIT_LOCK", "BriefDescription": "Number of cache line split locks sent to uncore.", "PublicDescription": "Counts the number of cache line split locks sent to the uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xFE", "UMask": "0x02", "EventName": "IDI_MISC.WB_UPGRADE", "BriefDescription": "Counts number of cache lines that are allocated and written back to L3 with the intention that they are more likely to be reused shortly", "PublicDescription": "Counts number of cache lines that are allocated and written back to L3 with the intention that they are more likely to be reused shortly.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xFE", "UMask": "0x04", "EventName": "IDI_MISC.WB_DOWNGRADE", "BriefDescription": "Counts number of cache lines that are dropped and not written back to L3 as they are deemed to be less likely to be reused shortly", "PublicDescription": "Counts number of cache lines that are dropped and not written back to L3 as they are deemed to be less likely to be reused shortly.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "0", "ELLC": "0", "Offcore": "0", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.ANY_RESPONSE", "BriefDescription": "Counts demand data reads have any response type. ", "PublicDescription": "Counts demand data reads have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand data reads TBD TBD ", "PublicDescription": "Counts demand data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts demand data reads TBD TBD ", "PublicDescription": "Counts demand data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts demand data reads TBD TBD ", "PublicDescription": "Counts demand data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts demand data reads TBD TBD ", "PublicDescription": "Counts demand data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts demand data reads TBD TBD ", "PublicDescription": "Counts demand data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts demand data reads TBD ", "PublicDescription": "Counts demand data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts demand data reads TBD ", "PublicDescription": "Counts demand data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand data reads TBD ", "PublicDescription": "Counts demand data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand data reads TBD ", "PublicDescription": "Counts demand data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand data reads TBD ", "PublicDescription": "Counts demand data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.ANY_RESPONSE", "BriefDescription": "Counts all demand data writes (RFOs) have any response type. ", "PublicDescription": "Counts all demand data writes (RFOs) have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts all demand data writes (RFOs) TBD TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts all demand data writes (RFOs) TBD TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts all demand data writes (RFOs) TBD TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts all demand data writes (RFOs) TBD TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts all demand data writes (RFOs) TBD TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts all demand data writes (RFOs) TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts all demand data writes (RFOs) TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all demand data writes (RFOs) TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all demand data writes (RFOs) TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all demand data writes (RFOs) TBD ", "PublicDescription": "Counts all demand data writes (RFOs) TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.ANY_RESPONSE", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that have any response type. ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.ANY_RESPONSE", "BriefDescription": "Counts prefetch (that bring data to L2) data reads have any response type. ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads TBD ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.ANY_RESPONSE", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs have any response type. ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.ANY_RESPONSE", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads have any response type. ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.ANY_RESPONSE", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs have any response type. ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.ANY_RESPONSE", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests have any response type. ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_HIT.ANY_SNOOP", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS.ANY_SNOOP", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS.REMOTE_HITM", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.ANY_RESPONSE", "BriefDescription": "TBD have any response type. ", "PublicDescription": "TBD have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.ANY_RESPONSE", "BriefDescription": "TBD have any response type. ", "PublicDescription": "TBD have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_HIT.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS.REMOTE_HITM", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.ANY_RESPONSE", "BriefDescription": "TBD have any response type. ", "PublicDescription": "TBD have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_HIT.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS.REMOTE_HITM", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.ANY_RESPONSE", "BriefDescription": "TBD have any response type. ", "PublicDescription": "TBD have any response type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0000010122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_HIT.NO_SNOOP_NEEDED", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x01003C0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x04003C0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_HIT.HITM_OTHER_CORE", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x10003C0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_HIT.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3F803C0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS.ANY_SNOOP", "BriefDescription": "TBD TBD TBD ", "PublicDescription": "TBD TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x3FBC000122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS.REMOTE_HIT_FORWARD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x083FC00122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS.REMOTE_HITM", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x103FC00122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063FC00122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS_REMOTE_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x063B800122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_MISS_LOCAL_DRAM.SNOOP_MISS_OR_NO_FWD", "BriefDescription": "TBD TBD ", "PublicDescription": "TBD TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6, 0x1a7", "MSRValue": "0x0604000122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts demand data reads ", "PublicDescription": "Counts demand data reads", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts all demand data writes (RFOs) ", "PublicDescription": "Counts all demand data writes (RFOs)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that ", "PublicDescription": "Counts demand instruction fetches and L1 instruction cache prefetches that", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_DATA_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts prefetch (that bring data to L2) data reads ", "PublicDescription": "Counts prefetch (that bring data to L2) data reads", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0010", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L2_RFO.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts all prefetch (that bring data to L2) RFOs ", "PublicDescription": "Counts all prefetch (that bring data to L2) RFOs", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0020", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_DATA_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) data reads ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) data reads", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0080", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L3_RFO.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts all prefetch (that bring data to LLC only) RFOs ", "PublicDescription": "Counts all prefetch (that bring data to LLC only) RFOs", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0100", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.PF_L1D_AND_SW.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests ", "PublicDescription": "Counts L1 data cache hardware prefetch requests and software prefetch requests", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_DATA_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "TBD ", "PublicDescription": "TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0490", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_PF_RFO.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "TBD ", "PublicDescription": "TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0120", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "TBD ", "PublicDescription": "TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0491", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.L3_HIT.SNOOP_HIT_WITH_FWD", "BriefDescription": "TBD ", "PublicDescription": "TBD", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0x08007C0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Data_LA": "0", "L1_Hit_Indication": "0", "Errata": "null", "ELLC": "0", "Offcore": "1", "Deprecated": "0"}]