# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.40"
name = "futures-lite"
version = "1.13.0"
authors = [
    "Stjepan Glavina <<EMAIL>>",
    "Contributors to futures-rs",
]
exclude = ["/.*"]
description = "Futures, streams, and async I/O combinators"
homepage = "https://github.com/smol-rs/futures-lite"
documentation = "https://docs.rs/futures-lite"
readme = "README.md"
keywords = [
    "asynchronous",
    "futures",
    "async",
]
categories = [
    "asynchronous",
    "concurrency",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/smol-rs/futures-lite"

[dependencies.fastrand]
version = "1.3.4"
optional = true

[dependencies.futures-core]
version = "0.3.5"
default-features = false

[dependencies.futures-io]
version = "0.3.5"
optional = true

[dependencies.memchr]
version = "2.3.3"
optional = true

[dependencies.parking]
version = "2.0.0"
optional = true

[dependencies.pin-project-lite]
version = "0.2.0"

[dependencies.waker-fn]
version = "1.0.0"
optional = true

[dev-dependencies.spin_on]
version = "0.1.0"

[features]
alloc = []
default = ["std"]
std = [
    "alloc",
    "fastrand",
    "futures-io",
    "parking",
    "memchr",
    "waker-fn",
]
