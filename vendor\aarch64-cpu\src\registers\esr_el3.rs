// SPDX-License-Identifier: Apache-2.0 OR MIT
//
// Copyright (c) 2018-2023 by the author(s)
//
// Author(s):
//   - <PERSON> <<EMAIL>>
//   - <PERSON><PERSON><PERSON> <<EMAIL>>
//   - <PERSON> <<EMAIL>>
//   - rmsyn <<EMAIL>>

//! Exception Syndrome Register - EL3
//!
//! Holds syndrome information for an exception taken to EL3.

use tock_registers::{interfaces::Readable, register_bitfields};

register_bitfields! {u64,
    pub ESR_EL3 [

        /// Reserved
        RES0 OFFSET(37) NUMBITS(27) [],

        /// Instruction Specific Syndrome 2
        ///
        /// When FEAT_LS64 is implemented:
        ///
        /// When FEAT_LS64_V is implemented, if a memory access generated by an ST64BV instruction
        /// generates a Data Abort exception for a Translation fault, Access flag fault, or Permission fault, then
        /// this field holds register specifier, Xs.
        ///
        /// When FEAT_LS64_ACCDATA is implemented, if a memory access generated by an ST64BV0
        /// instruction generates a Data Abort exception for a Translation fault, Access flag fault, or Permission
        /// fault, then this field holds register specifier, Xs.
        ///
        /// Otherwise, this field is RES 0.
        ISS2 OFFSET(32) NUMBITS(5) [],

        /// Exception Class. Indicates the reason for the exception that this register holds
        /// information about.
        ///
        /// For each EC value, the table references a subsection that gives information about:
        ///   - The cause of the exception, for example the configuration required to enable the
        ///     trap.
        ///   - The encoding of the associated ISS.
        ///
        /// All other EC values are reserved by Arm, and:
        ///   - Unused values in the range 0b000000 - 0b101100 (0x00 - 0x2C) are reserved for future use for
        ///   synchronous exceptions.
        ///
        ///   - Unused values in the range 0b101101 - 0b111111 (0x2D - 0x3F) are reserved for future use, and
        ///   might be used for synchronous or asynchronous exceptions.
        ///
        /// The effect of programming this field to a reserved value is that behavior is CONSTRAINED UNPREDICTABLE.
        ///
        /// The reset behavior of this field is:
        ///   - On a Warm reset, this field resets to an architecturally UNKNOWN value.
        EC  OFFSET(26) NUMBITS(6) [
            Unknown               = 0b00_0000,
            TrappedWFIorWFE       = 0b00_0001,
            TrappedMCRorMRC       = 0b00_0011, // A32
            TrappedMCRRorMRRC     = 0b00_0100, // A32
            TrappedMCRorMRC2      = 0b00_0101, // A32
            TrappedLDCorSTC       = 0b00_0110, // A32
            TrappedFP             = 0b00_0111,
            TrappedPointerAuth    = 0b00_1001,
            LD64BorST64B          = 0b00_1010,
            TrappedMRRC           = 0b00_1100, // A32
            BranchTarget          = 0b00_1101,
            IllegalExecutionState = 0b00_1110,
            SMC32                 = 0b01_0011, // A32
            SVC64                 = 0b01_0101,
            HVC64                 = 0b01_0110,
            SMC64                 = 0b01_0111,
            TrappedMsrMrs         = 0b01_1000,
            TrappedSve            = 0b01_1001,
            TSTART                = 0b01_1011,
            PointerAuth           = 0b01_1100,
            TrappedSME            = 0b01_1101,
            GranuleProtection     = 0b01_1110,
            ImplDefined           = 0b01_1111,
            InstrAbortLowerEL     = 0b10_0000,
            InstrAbortCurrentEL   = 0b10_0001,
            PCAlignmentFault      = 0b10_0010,
            DataAbortLowerEL      = 0b10_0100,
            DataAbortCurrentEL    = 0b10_0101,
            SPAlignmentFault      = 0b10_0110,
            MemoryOperation       = 0b10_0111,
            TrappedFP64           = 0b10_1100,
            SError                = 0b10_1111,
            Brk64                 = 0b11_1100  // A64 BRK instruction
        ],

        /// Instruction Length for synchronous exceptions. Possible values of this bit are:
        ///     0b0 16-bit instruction trapped.
        ///
        ///     0b1 32-bit instruction trapped. This value is also used when the exception is one of the
        ///     following:
        ///         - An SError interrupt.
        ///         - An Instruction Abort exception.
        ///         - A PC alignment fault exception.
        ///         - An SP alignment fault exception.
        ///         - A Data Abort exception for which the value of the ISV bit is 0.
        ///         - An Illegal Execution state exception.
        ///         - Any debug exception except for Breakpoint instruction exceptions.
        ///         - An exception reported using EC value 0b000000.
        ///
        /// The reset behavior of this field is:
        ///     - On a Warm reset, this field resets to an architecturally UNKNOWN value.
        IL  OFFSET(25) NUMBITS(1) [
            Trapped16 = 0,
            Trapped32 = 1
        ],

        /// Instruction Specific Syndrome. Architecturally, this field can be defined independently
        /// for each defined Exception class. However, in practice, some ISS encodings are used for
        /// more than one Exception class.
        ISS OFFSET(0)  NUMBITS(25) []
    ]
}

pub struct Reg;

impl Readable for Reg {
    type T = u64;
    type R = ESR_EL3::Register;

    sys_coproc_read_raw!(u64, "ESR_EL3", "x");
}

pub const ESR_EL3: Reg = Reg {};
