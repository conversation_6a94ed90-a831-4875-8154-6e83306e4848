[{"EventCode": "0x2", "UMask": "0x83", "EventName": "STORE_FORWARDS.ANY", "BriefDescription": "All store forwards", "PublicDescription": "All store forwards", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2", "UMask": "0x81", "EventName": "STORE_FORWARDS.GOOD", "BriefDescription": "Good store forwards", "PublicDescription": "Good store forwards", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3", "UMask": "0x7F", "EventName": "REISSUE.ANY", "BriefDescription": "Micro-op reissues for any cause", "PublicDescription": "Micro-op reissues for any cause", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3", "UMask": "0xFF", "EventName": "REISSUE.ANY.AR", "BriefDescription": "Micro-op reissues for any cause (At Retirement)", "PublicDescription": "Micro-op reissues for any cause (At Retirement)", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0xF", "EventName": "MISALIGN_MEM_REF.SPLIT", "BriefDescription": "Memory references that cross an 8-byte boundary.", "PublicDescription": "Memory references that cross an 8-byte boundary.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x9", "EventName": "MISALIGN_MEM_REF.LD_SPLIT", "BriefDescription": "Load splits", "PublicDescription": "Load splits", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0xA", "EventName": "MISALIGN_MEM_REF.ST_SPLIT", "BriefDescription": "Store splits", "PublicDescription": "Store splits", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x8F", "EventName": "MISALIGN_MEM_REF.SPLIT.AR", "BriefDescription": "Memory references that cross an 8-byte boundary (At Retirement)", "PublicDescription": "Memory references that cross an 8-byte boundary (At Retirement)", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x89", "EventName": "MISALIGN_MEM_REF.LD_SPLIT.AR", "BriefDescription": "Load splits (At Retirement)", "PublicDescription": "Load splits (At Retirement)", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x8A", "EventName": "MISALIGN_MEM_REF.ST_SPLIT.AR", "BriefDescription": "Store splits (Ar Retirement)", "PublicDescription": "Store splits (Ar Retirement)", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x8C", "EventName": "MISALIGN_MEM_REF.RMW_SPLIT", "BriefDescription": "ld-op-st splits", "PublicDescription": "ld-op-st splits", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x97", "EventName": "MISALIGN_MEM_REF.BUBBLE", "BriefDescription": "Nonzero segbase 1 bubble", "PublicDescription": "Nonzero segbase 1 bubble", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x91", "EventName": "MISALIGN_MEM_REF.LD_BUBBLE", "BriefDescription": "Nonzero segbase load 1 bubble", "PublicDescription": "Nonzero segbase load 1 bubble", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x92", "EventName": "MISALIGN_MEM_REF.ST_BUBBLE", "BriefDescription": "Nonzero segbase store 1 bubble", "PublicDescription": "Nonzero segbase store 1 bubble", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x5", "UMask": "0x94", "EventName": "MISALIGN_MEM_REF.RMW_BUBBLE", "BriefDescription": "Nonzero segbase ld-op-st 1 bubble", "PublicDescription": "Nonzero segbase ld-op-st 1 bubble", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6", "UMask": "0x80", "EventName": "SEGMENT_REG_LOADS.ANY", "BriefDescription": "Number of segment register loads.", "PublicDescription": "Number of segment register loads.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x81", "EventName": "PREFETCH.PREFETCHT0", "BriefDescription": "Streaming SIMD Extensions (SSE) PrefetchT0 instructions executed.", "PublicDescription": "Streaming SIMD Extensions (SSE) PrefetchT0 instructions executed.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x82", "EventName": "PREFETCH.PREFETCHT1", "BriefDescription": "Streaming SIMD Extensions (SSE) PrefetchT1 instructions executed.", "PublicDescription": "Streaming SIMD Extensions (SSE) PrefetchT1 instructions executed.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x84", "EventName": "PREFETCH.PREFETCHT2", "BriefDescription": "Streaming SIMD Extensions (SSE) PrefetchT2 instructions executed.", "PublicDescription": "Streaming SIMD Extensions (SSE) PrefetchT2 instructions executed.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x86", "EventName": "PREFETCH.SW_L2", "BriefDescription": "Streaming SIMD Extensions (SSE) PrefetchT1 and PrefetchT2 instructions executed", "PublicDescription": "Streaming SIMD Extensions (SSE) PrefetchT1 and PrefetchT2 instructions executed", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x88", "EventName": "PREFETCH.PREFETCHNTA", "BriefDescription": "Streaming SIMD Extensions (SSE) Prefetch NTA instructions executed", "PublicDescription": "Streaming SIMD Extensions (SSE) Prefetch NTA instructions executed", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x10", "EventName": "PREFETCH.HW_PREFETCH", "BriefDescription": "L1 hardware prefetch request", "PublicDescription": "L1 hardware prefetch request", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0xF", "EventName": "PREFETCH.SOFTWARE_PREFETCH", "BriefDescription": "Any Software prefetch", "PublicDescription": "Any Software prefetch", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7", "UMask": "0x8F", "EventName": "PREFETCH.SOFTWARE_PREFETCH.AR", "BriefDescription": "Any Software prefetch", "PublicDescription": "Any Software prefetch", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x8", "UMask": "0x7", "EventName": "DATA_TLB_MISSES.DTLB_MISS", "BriefDescription": "Memory accesses that missed the DTLB.", "PublicDescription": "Memory accesses that missed the DTLB.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x8", "UMask": "0x5", "EventName": "DATA_TLB_MISSES.DTLB_MISS_LD", "BriefDescription": "DTLB misses due to load operations.", "PublicDescription": "DTLB misses due to load operations.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x8", "UMask": "0x9", "EventName": "DATA_TLB_MISSES.L0_DTLB_MISS_LD", "BriefDescription": "L0 DTLB misses due to load operations.", "PublicDescription": "L0 DTLB misses due to load operations.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x8", "UMask": "0x6", "EventName": "DATA_TLB_MISSES.DTLB_MISS_ST", "BriefDescription": "DTLB misses due to store operations.", "PublicDescription": "DTLB misses due to store operations.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x8", "UMask": "0xA", "EventName": "DATA_TLB_MISSES.L0_DTLB_MISS_ST", "BriefDescription": "L0 DTLB misses due to store operations", "PublicDescription": "L0 DTLB misses due to store operations", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x9", "UMask": "0x20", "EventName": "DISPATCH_BLOCKED.ANY", "BriefDescription": "Memory cluster signals to block micro-op dispatch for any reason", "PublicDescription": "Memory cluster signals to block micro-op dispatch for any reason", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x3", "EventName": "PAGE_WALKS.WALKS", "BriefDescription": "Number of page-walks executed.", "PublicDescription": "Number of page-walks executed.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x3", "EventName": "PAGE_WALKS.CYCLES", "BriefDescription": "Duration of page-walks in core cycles", "PublicDescription": "Duration of page-walks in core cycles", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x1", "EventName": "PAGE_WALKS.D_SIDE_WALKS", "BriefDescription": "Number of D-side only page walks", "PublicDescription": "Number of D-side only page walks", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x1", "EventName": "PAGE_WALKS.D_SIDE_CYCLES", "BriefDescription": "Duration of D-side only page walks", "PublicDescription": "Duration of D-side only page walks", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x2", "EventName": "PAGE_WALKS.I_SIDE_WALKS", "BriefDescription": "Number of I-Side page walks", "PublicDescription": "Number of I-Side page walks", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC", "UMask": "0x2", "EventName": "PAGE_WALKS.I_SIDE_CYCLES", "BriefDescription": "Duration of I-Side page walks", "PublicDescription": "Duration of I-Side page walks", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x1", "EventName": "X87_COMP_OPS_EXE.ANY.S", "BriefDescription": "Floating point computational micro-ops executed.", "PublicDescription": "Floating point computational micro-ops executed.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x81", "EventName": "X87_COMP_OPS_EXE.ANY.AR", "BriefDescription": "Floating point computational micro-ops retired.", "PublicDescription": "Floating point computational micro-ops retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x2", "EventName": "X87_COMP_OPS_EXE.FXCH.S", "BriefDescription": "FXCH uops executed.", "PublicDescription": "FXCH uops executed.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x82", "EventName": "X87_COMP_OPS_EXE.FXCH.AR", "BriefDescription": "<PERSON><PERSON> retired.", "PublicDescription": "<PERSON><PERSON> retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x11", "UMask": "0x1", "EventName": "FP_ASSIST.S", "BriefDescription": "Floating point assists.", "PublicDescription": "Floating point assists.", "Counter": "0,1", "SampleAfterValue": "10000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x11", "UMask": "0x81", "EventName": "FP_ASSIST.AR", "BriefDescription": "Floating point assists for retired operations.", "PublicDescription": "Floating point assists for retired operations.", "Counter": "0,1", "SampleAfterValue": "10000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x12", "UMask": "0x1", "EventName": "MUL.S", "BriefDescription": "Multiply operations executed.", "PublicDescription": "Multiply operations executed.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x12", "UMask": "0x81", "EventName": "MUL.AR", "BriefDescription": "Multiply operations retired", "PublicDescription": "Multiply operations retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x13", "UMask": "0x1", "EventName": "DIV.S", "BriefDescription": "Divide operations executed.", "PublicDescription": "Divide operations executed.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x13", "UMask": "0x81", "EventName": "DIV.AR", "BriefDescription": "Divide operations retired", "PublicDescription": "Divide operations retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x14", "UMask": "0x1", "EventName": "CYCLES_DIV_BUSY", "BriefDescription": "Cycles the divider is busy.", "PublicDescription": "Cycles the divider is busy.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x21", "UMask": "0x40", "EventName": "L2_ADS.SELF", "BriefDescription": "Cycles L2 address bus is in use.", "PublicDescription": "Cycles L2 address bus is in use.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x22", "UMask": "0x40", "EventName": "L2_DBUS_BUSY.SELF", "BriefDescription": "Cycles the L2 cache data bus is busy.", "PublicDescription": "Cycles the L2 cache data bus is busy.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x23", "UMask": "0x40", "EventName": "L2_DBUS_BUSY_RD.SELF", "BriefDescription": "Cycles the L2 transfers data to the core.", "PublicDescription": "Cycles the L2 transfers data to the core.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x70", "EventName": "L2_LINES_IN.SELF.ANY", "BriefDescription": "L2 cache misses.", "PublicDescription": "L2 cache misses.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x40", "EventName": "L2_LINES_IN.SELF.DEMAND", "BriefDescription": "L2 cache misses.", "PublicDescription": "L2 cache misses.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x50", "EventName": "L2_LINES_IN.SELF.PREFETCH", "BriefDescription": "L2 cache misses.", "PublicDescription": "L2 cache misses.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x25", "UMask": "0x40", "EventName": "L2_M_LINES_IN.SELF", "BriefDescription": "L2 cache line modifications.", "PublicDescription": "L2 cache line modifications.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x26", "UMask": "0x70", "EventName": "L2_LINES_OUT.SELF.ANY", "BriefDescription": "L2 cache lines evicted.", "PublicDescription": "L2 cache lines evicted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x26", "UMask": "0x40", "EventName": "L2_LINES_OUT.SELF.DEMAND", "BriefDescription": "L2 cache lines evicted.", "PublicDescription": "L2 cache lines evicted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x26", "UMask": "0x50", "EventName": "L2_LINES_OUT.SELF.PREFETCH", "BriefDescription": "L2 cache lines evicted.", "PublicDescription": "L2 cache lines evicted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x70", "EventName": "L2_M_LINES_OUT.SELF.ANY", "BriefDescription": "Modified lines evicted from the L2 cache", "PublicDescription": "Modified lines evicted from the L2 cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x40", "EventName": "L2_M_LINES_OUT.SELF.DEMAND", "BriefDescription": "Modified lines evicted from the L2 cache", "PublicDescription": "Modified lines evicted from the L2 cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x50", "EventName": "L2_M_LINES_OUT.SELF.PREFETCH", "BriefDescription": "Modified lines evicted from the L2 cache", "PublicDescription": "Modified lines evicted from the L2 cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x44", "EventName": "L2_IFETCH.SELF.E_STATE", "BriefDescription": "L2 cacheable instruction fetch requests", "PublicDescription": "L2 cacheable instruction fetch requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x41", "EventName": "L2_IFETCH.SELF.I_STATE", "BriefDescription": "L2 cacheable instruction fetch requests", "PublicDescription": "L2 cacheable instruction fetch requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x48", "EventName": "L2_IFETCH.SELF.M_STATE", "BriefDescription": "L2 cacheable instruction fetch requests", "PublicDescription": "L2 cacheable instruction fetch requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x42", "EventName": "L2_IFETCH.SELF.S_STATE", "BriefDescription": "L2 cacheable instruction fetch requests", "PublicDescription": "L2 cacheable instruction fetch requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x4F", "EventName": "L2_IFETCH.SELF.MESI", "BriefDescription": "L2 cacheable instruction fetch requests", "PublicDescription": "L2 cacheable instruction fetch requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x74", "EventName": "L2_LD.SELF.ANY.E_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x71", "EventName": "L2_LD.SELF.ANY.I_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x78", "EventName": "L2_LD.SELF.ANY.M_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x72", "EventName": "L2_LD.SELF.ANY.S_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x7F", "EventName": "L2_LD.SELF.ANY.MESI", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x44", "EventName": "L2_LD.SELF.DEMAND.E_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x41", "EventName": "L2_LD.SELF.DEMAND.I_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x48", "EventName": "L2_LD.SELF.DEMAND.M_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x42", "EventName": "L2_LD.SELF.DEMAND.S_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x4F", "EventName": "L2_LD.SELF.DEMAND.MESI", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x54", "EventName": "L2_LD.SELF.PREFETCH.E_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x51", "EventName": "L2_LD.SELF.PREFETCH.I_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x58", "EventName": "L2_LD.SELF.PREFETCH.M_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x52", "EventName": "L2_LD.SELF.PREFETCH.S_STATE", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x29", "UMask": "0x5F", "EventName": "L2_LD.SELF.PREFETCH.MESI", "BriefDescription": "L2 cache reads", "PublicDescription": "L2 cache reads", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2A", "UMask": "0x44", "EventName": "L2_ST.SELF.E_STATE", "BriefDescription": "L2 store requests", "PublicDescription": "L2 store requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2A", "UMask": "0x41", "EventName": "L2_ST.SELF.I_STATE", "BriefDescription": "L2 store requests", "PublicDescription": "L2 store requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2A", "UMask": "0x48", "EventName": "L2_ST.SELF.M_STATE", "BriefDescription": "L2 store requests", "PublicDescription": "L2 store requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2A", "UMask": "0x42", "EventName": "L2_ST.SELF.S_STATE", "BriefDescription": "L2 store requests", "PublicDescription": "L2 store requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2A", "UMask": "0x4F", "EventName": "L2_ST.SELF.MESI", "BriefDescription": "L2 store requests", "PublicDescription": "L2 store requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2B", "UMask": "0x44", "EventName": "L2_LOCK.SELF.E_STATE", "BriefDescription": "L2 locked accesses", "PublicDescription": "L2 locked accesses", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2B", "UMask": "0x41", "EventName": "L2_LOCK.SELF.I_STATE", "BriefDescription": "L2 locked accesses", "PublicDescription": "L2 locked accesses", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2B", "UMask": "0x48", "EventName": "L2_LOCK.SELF.M_STATE", "BriefDescription": "L2 locked accesses", "PublicDescription": "L2 locked accesses", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2B", "UMask": "0x42", "EventName": "L2_LOCK.SELF.S_STATE", "BriefDescription": "L2 locked accesses", "PublicDescription": "L2 locked accesses", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2B", "UMask": "0x4F", "EventName": "L2_LOCK.SELF.MESI", "BriefDescription": "L2 locked accesses", "PublicDescription": "L2 locked accesses", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2C", "UMask": "0x44", "EventName": "L2_DATA_RQSTS.SELF.E_STATE", "BriefDescription": "All data requests from the L1 data cache", "PublicDescription": "All data requests from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2C", "UMask": "0x41", "EventName": "L2_DATA_RQSTS.SELF.I_STATE", "BriefDescription": "All data requests from the L1 data cache", "PublicDescription": "All data requests from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2C", "UMask": "0x48", "EventName": "L2_DATA_RQSTS.SELF.M_STATE", "BriefDescription": "All data requests from the L1 data cache", "PublicDescription": "All data requests from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2C", "UMask": "0x42", "EventName": "L2_DATA_RQSTS.SELF.S_STATE", "BriefDescription": "All data requests from the L1 data cache", "PublicDescription": "All data requests from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2C", "UMask": "0x4F", "EventName": "L2_DATA_RQSTS.SELF.MESI", "BriefDescription": "All data requests from the L1 data cache", "PublicDescription": "All data requests from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2D", "UMask": "0x44", "EventName": "L2_LD_IFETCH.SELF.E_STATE", "BriefDescription": "All read requests from L1 instruction and data caches", "PublicDescription": "All read requests from L1 instruction and data caches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2D", "UMask": "0x41", "EventName": "L2_LD_IFETCH.SELF.I_STATE", "BriefDescription": "All read requests from L1 instruction and data caches", "PublicDescription": "All read requests from L1 instruction and data caches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2D", "UMask": "0x48", "EventName": "L2_LD_IFETCH.SELF.M_STATE", "BriefDescription": "All read requests from L1 instruction and data caches", "PublicDescription": "All read requests from L1 instruction and data caches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2D", "UMask": "0x42", "EventName": "L2_LD_IFETCH.SELF.S_STATE", "BriefDescription": "All read requests from L1 instruction and data caches", "PublicDescription": "All read requests from L1 instruction and data caches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2D", "UMask": "0x4F", "EventName": "L2_LD_IFETCH.SELF.MESI", "BriefDescription": "All read requests from L1 instruction and data caches", "PublicDescription": "All read requests from L1 instruction and data caches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x74", "EventName": "L2_RQSTS.SELF.ANY.E_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x71", "EventName": "L2_RQSTS.SELF.ANY.I_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x78", "EventName": "L2_RQSTS.SELF.ANY.M_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x72", "EventName": "L2_RQSTS.SELF.ANY.S_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x7F", "EventName": "L2_RQSTS.SELF.ANY.MESI", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x44", "EventName": "L2_RQSTS.SELF.DEMAND.E_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x48", "EventName": "L2_RQSTS.SELF.DEMAND.M_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x42", "EventName": "L2_RQSTS.SELF.DEMAND.S_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x54", "EventName": "L2_RQSTS.SELF.PREFETCH.E_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x51", "EventName": "L2_RQSTS.SELF.PREFETCH.I_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x58", "EventName": "L2_RQSTS.SELF.PREFETCH.M_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x52", "EventName": "L2_RQSTS.SELF.PREFETCH.S_STATE", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x5F", "EventName": "L2_RQSTS.SELF.PREFETCH.MESI", "BriefDescription": "L2 cache requests", "PublicDescription": "L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x41", "EventName": "L2_RQSTS.SELF.DEMAND.I_STATE", "BriefDescription": "L2 cache demand requests from this core that missed the L2", "PublicDescription": "L2 cache demand requests from this core that missed the L2", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x4F", "EventName": "L2_RQSTS.SELF.DEMAND.MESI", "BriefDescription": "L2 cache demand requests from this core", "PublicDescription": "L2 cache demand requests from this core", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x74", "EventName": "L2_REJECT_BUSQ.SELF.ANY.E_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x71", "EventName": "L2_REJECT_BUSQ.SELF.ANY.I_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x78", "EventName": "L2_REJECT_BUSQ.SELF.ANY.M_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x72", "EventName": "L2_REJECT_BUSQ.SELF.ANY.S_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x7F", "EventName": "L2_REJECT_BUSQ.SELF.ANY.MESI", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x44", "EventName": "L2_REJECT_BUSQ.SELF.DEMAND.E_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x41", "EventName": "L2_REJECT_BUSQ.SELF.DEMAND.I_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x48", "EventName": "L2_REJECT_BUSQ.SELF.DEMAND.M_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x42", "EventName": "L2_REJECT_BUSQ.SELF.DEMAND.S_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x4F", "EventName": "L2_REJECT_BUSQ.SELF.DEMAND.MESI", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x54", "EventName": "L2_REJECT_BUSQ.SELF.PREFETCH.E_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x51", "EventName": "L2_REJECT_BUSQ.SELF.PREFETCH.I_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x58", "EventName": "L2_REJECT_BUSQ.SELF.PREFETCH.M_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x52", "EventName": "L2_REJECT_BUSQ.SELF.PREFETCH.S_STATE", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x30", "UMask": "0x5F", "EventName": "L2_REJECT_BUSQ.SELF.PREFETCH.MESI", "BriefDescription": "Rejected L2 cache requests", "PublicDescription": "Rejected L2 cache requests", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x32", "UMask": "0x40", "EventName": "L2_NO_REQ.SELF", "BriefDescription": "Cycles no L2 cache requests are pending", "PublicDescription": "Cycles no L2 cache requests are pending", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3A", "UMask": "0x0", "EventName": "EIST_TRANS", "BriefDescription": "Number of Enhanced Intel SpeedStep(R) Technology (EIST) transitions", "PublicDescription": "Number of Enhanced Intel SpeedStep(R) Technology (EIST) transitions", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3B", "UMask": "0xC0", "EventName": "THERMAL_TRIP", "BriefDescription": "Number of thermal trips", "PublicDescription": "Number of thermal trips", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x0", "EventName": "CPU_CLK_UNHALTED.CORE_P", "BriefDescription": "Core cycles when core is not halted", "PublicDescription": "Core cycles when core is not halted", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x1", "EventName": "CPU_CLK_UNHALTED.BUS", "BriefDescription": "Bus cycles when core is not halted", "PublicDescription": "Bus cycles when core is not halted", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xA", "UMask": "0x0", "EventName": "CPU_CLK_UNHALTED.CORE", "BriefDescription": "Core cycles when core is not halted", "PublicDescription": "Core cycles when core is not halted", "Counter": "Fixed counter 2", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xA", "UMask": "0x0", "EventName": "CPU_CLK_UNHALTED.REF", "BriefDescription": "Reference cycles when core is not halted.", "PublicDescription": "Reference cycles when core is not halted.", "Counter": "Fixed counter 3", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0xA1", "EventName": "L1D_CACHE.LD", "BriefDescription": "L1 Cacheable Data Reads", "PublicDescription": "L1 Cacheable Data Reads", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0xA2", "EventName": "L1D_CACHE.ST", "BriefDescription": "L1 Cacheable Data Writes", "PublicDescription": "L1 Cacheable Data Writes", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0x83", "EventName": "L1D_CACHE.ALL_REF", "BriefDescription": "L1 Data reads and writes", "PublicDescription": "L1 Data reads and writes", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0xA3", "EventName": "L1D_CACHE.ALL_CACHE_REF", "BriefDescription": "L1 Data Cacheable reads and writes", "PublicDescription": "L1 Data Cacheable reads and writes", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0x8", "EventName": "L1D_CACHE.REPL", "BriefDescription": "L1 Data line replacements", "PublicDescription": "L1 Data line replacements", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0x48", "EventName": "L1D_CACHE.REPLM", "BriefDescription": "Modified cache lines allocated in the L1 data cache", "PublicDescription": "Modified cache lines allocated in the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x40", "UMask": "0x10", "EventName": "L1D_CACHE.EVICT", "BriefDescription": "Modified cache lines evicted from the L1 data cache", "PublicDescription": "Modified cache lines evicted from the L1 data cache", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0xE0", "EventName": "BUS_REQUEST_OUTSTANDING.ALL_AGENTS", "BriefDescription": "Outstanding cacheable data read bus requests duration.", "PublicDescription": "Outstanding cacheable data read bus requests duration.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x40", "EventName": "BUS_REQUEST_OUTSTANDING.SELF", "BriefDescription": "Outstanding cacheable data read bus requests duration.", "PublicDescription": "Outstanding cacheable data read bus requests duration.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x61", "UMask": "0x20", "EventName": "BUS_BNR_DRV.ALL_AGENTS", "BriefDescription": "Number of Bus Not Ready signals asserted.", "PublicDescription": "Number of Bus Not Ready signals asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x61", "UMask": "0x0", "EventName": "BUS_BNR_DRV.THIS_AGENT", "BriefDescription": "Number of Bus Not Ready signals asserted.", "PublicDescription": "Number of Bus Not Ready signals asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x62", "UMask": "0x20", "EventName": "BUS_DRDY_CLOCKS.ALL_AGENTS", "BriefDescription": "Bus cycles when data is sent on the bus.", "PublicDescription": "Bus cycles when data is sent on the bus.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x62", "UMask": "0x0", "EventName": "BUS_DRDY_CLOCKS.THIS_AGENT", "BriefDescription": "Bus cycles when data is sent on the bus.", "PublicDescription": "Bus cycles when data is sent on the bus.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x63", "UMask": "0xE0", "EventName": "BUS_LOCK_CLOCKS.ALL_AGENTS", "BriefDescription": "Bus cycles when a LOCK signal is asserted.", "PublicDescription": "Bus cycles when a LOCK signal is asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x63", "UMask": "0x40", "EventName": "BUS_LOCK_CLOCKS.SELF", "BriefDescription": "Bus cycles when a LOCK signal is asserted.", "PublicDescription": "Bus cycles when a LOCK signal is asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x64", "UMask": "0x40", "EventName": "BUS_DATA_RCV.SELF", "BriefDescription": "Bus cycles while processor receives data.", "PublicDescription": "Bus cycles while processor receives data.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x65", "UMask": "0xE0", "EventName": "BUS_TRANS_BRD.ALL_AGENTS", "BriefDescription": "<PERSON><PERSON><PERSON> read bus transactions.", "PublicDescription": "<PERSON><PERSON><PERSON> read bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x65", "UMask": "0x40", "EventName": "BUS_TRANS_BRD.SELF", "BriefDescription": "<PERSON><PERSON><PERSON> read bus transactions.", "PublicDescription": "<PERSON><PERSON><PERSON> read bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x66", "UMask": "0xE0", "EventName": "BUS_TRANS_RFO.ALL_AGENTS", "BriefDescription": "RFO bus transactions.", "PublicDescription": "RFO bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x66", "UMask": "0x40", "EventName": "BUS_TRANS_RFO.SELF", "BriefDescription": "RFO bus transactions.", "PublicDescription": "RFO bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x67", "UMask": "0xE0", "EventName": "BUS_TRANS_WB.ALL_AGENTS", "BriefDescription": "Explicit writeback bus transactions.", "PublicDescription": "Explicit writeback bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x67", "UMask": "0x40", "EventName": "BUS_TRANS_WB.SELF", "BriefDescription": "Explicit writeback bus transactions.", "PublicDescription": "Explicit writeback bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x68", "UMask": "0xE0", "EventName": "BUS_TRANS_IFETCH.ALL_AGENTS", "BriefDescription": "Instruction-fetch bus transactions.", "PublicDescription": "Instruction-fetch bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x68", "UMask": "0x40", "EventName": "BUS_TRANS_IFETCH.SELF", "BriefDescription": "Instruction-fetch bus transactions.", "PublicDescription": "Instruction-fetch bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x69", "UMask": "0xE0", "EventName": "BUS_TRANS_INVAL.ALL_AGENTS", "BriefDescription": "Invalidate bus transactions.", "PublicDescription": "Invalidate bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x69", "UMask": "0x40", "EventName": "BUS_TRANS_INVAL.SELF", "BriefDescription": "Invalidate bus transactions.", "PublicDescription": "Invalidate bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6A", "UMask": "0xE0", "EventName": "BUS_TRANS_PWR.ALL_AGENTS", "BriefDescription": "Partial write bus transaction.", "PublicDescription": "Partial write bus transaction.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6A", "UMask": "0x40", "EventName": "BUS_TRANS_PWR.SELF", "BriefDescription": "Partial write bus transaction.", "PublicDescription": "Partial write bus transaction.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6B", "UMask": "0xE0", "EventName": "BUS_TRANS_P.ALL_AGENTS", "BriefDescription": "Partial bus transactions.", "PublicDescription": "Partial bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6B", "UMask": "0x40", "EventName": "BUS_TRANS_P.SELF", "BriefDescription": "Partial bus transactions.", "PublicDescription": "Partial bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6C", "UMask": "0xE0", "EventName": "BUS_TRANS_IO.ALL_AGENTS", "BriefDescription": "IO bus transactions.", "PublicDescription": "IO bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6C", "UMask": "0x40", "EventName": "BUS_TRANS_IO.SELF", "BriefDescription": "IO bus transactions.", "PublicDescription": "IO bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6D", "UMask": "0xE0", "EventName": "BUS_TRANS_DEF.ALL_AGENTS", "BriefDescription": "Deferred bus transactions.", "PublicDescription": "Deferred bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6D", "UMask": "0x40", "EventName": "BUS_TRANS_DEF.SELF", "BriefDescription": "Deferred bus transactions.", "PublicDescription": "Deferred bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6E", "UMask": "0xE0", "EventName": "BUS_TRANS_BURST.ALL_AGENTS", "BriefDescription": "Burst (full cache-line) bus transactions.", "PublicDescription": "Burst (full cache-line) bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6E", "UMask": "0x40", "EventName": "BUS_TRANS_BURST.SELF", "BriefDescription": "Burst (full cache-line) bus transactions.", "PublicDescription": "Burst (full cache-line) bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6F", "UMask": "0xE0", "EventName": "BUS_TRANS_MEM.ALL_AGENTS", "BriefDescription": "Memory bus transactions.", "PublicDescription": "Memory bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x6F", "UMask": "0x40", "EventName": "BUS_TRANS_MEM.SELF", "BriefDescription": "Memory bus transactions.", "PublicDescription": "Memory bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x70", "UMask": "0xE0", "EventName": "BUS_TRANS_ANY.ALL_AGENTS", "BriefDescription": "All bus transactions.", "PublicDescription": "All bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x70", "UMask": "0x40", "EventName": "BUS_TRANS_ANY.SELF", "BriefDescription": "All bus transactions.", "PublicDescription": "All bus transactions.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0xB", "EventName": "EXT_SNOOP.THIS_AGENT.ANY", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x1", "EventName": "EXT_SNOOP.THIS_AGENT.CLEAN", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x2", "EventName": "EXT_SNOOP.THIS_AGENT.HIT", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x8", "EventName": "EXT_SNOOP.THIS_AGENT.HITM", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x2B", "EventName": "EXT_SNOOP.ALL_AGENTS.ANY", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x21", "EventName": "EXT_SNOOP.ALL_AGENTS.CLEAN", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x22", "EventName": "EXT_SNOOP.ALL_AGENTS.HIT", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x77", "UMask": "0x28", "EventName": "EXT_SNOOP.ALL_AGENTS.HITM", "BriefDescription": "External snoops.", "PublicDescription": "External snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7A", "UMask": "0x20", "EventName": "BUS_HIT_DRV.ALL_AGENTS", "BriefDescription": "HIT signal asserted.", "PublicDescription": "HIT signal asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7A", "UMask": "0x0", "EventName": "BUS_HIT_DRV.THIS_AGENT", "BriefDescription": "HIT signal asserted.", "PublicDescription": "HIT signal asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7B", "UMask": "0x20", "EventName": "BUS_HITM_DRV.ALL_AGENTS", "BriefDescription": "HITM signal asserted.", "PublicDescription": "HITM signal asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7B", "UMask": "0x0", "EventName": "BUS_HITM_DRV.THIS_AGENT", "BriefDescription": "HITM signal asserted.", "PublicDescription": "HITM signal asserted.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7D", "UMask": "0x40", "EventName": "BUSQ_EMPTY.SELF", "BriefDescription": "Bus queue is empty.", "PublicDescription": "Bus queue is empty.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7E", "UMask": "0xE0", "EventName": "SNOOP_STALL_DRV.ALL_AGENTS", "BriefDescription": "Bus stalled for snoops.", "PublicDescription": "Bus stalled for snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7E", "UMask": "0x40", "EventName": "SNOOP_STALL_DRV.SELF", "BriefDescription": "Bus stalled for snoops.", "PublicDescription": "Bus stalled for snoops.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x7F", "UMask": "0x40", "EventName": "BUS_IO_WAIT.SELF", "BriefDescription": "IO requests waiting in the bus queue.", "PublicDescription": "IO requests waiting in the bus queue.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x3", "EventName": "ICACHE.ACCESSES", "BriefDescription": "Instruction fetches.", "PublicDescription": "Instruction fetches.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x1", "EventName": "ICACHE.HIT", "BriefDescription": "Icache hit", "PublicDescription": "Icache hit", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x2", "EventName": "ICACHE.MISSES", "BriefDescription": "Icache miss", "PublicDescription": "Icache miss", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x82", "UMask": "0x1", "EventName": "ITLB.HIT", "BriefDescription": "ITLB hits.", "PublicDescription": "ITLB hits.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x82", "UMask": "0x4", "EventName": "ITLB.FLUSH", "BriefDescription": "ITLB flushes.", "PublicDescription": "ITLB flushes.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x82", "UMask": "0x2", "EventName": "ITLB.MISSES", "BriefDescription": "ITLB misses.", "PublicDescription": "ITLB misses.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x86", "UMask": "0x1", "EventName": "CYCLES_ICACHE_MEM_STALLED.ICACHE_MEM_STALLED", "BriefDescription": "Cycles during which instruction fetches are  stalled.", "PublicDescription": "Cycles during which instruction fetches are  stalled.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x87", "UMask": "0x1", "EventName": "DECODE_STALL.PFB_EMPTY", "BriefDescription": "Decode stall due to PFB empty", "PublicDescription": "Decode stall due to PFB empty", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x87", "UMask": "0x2", "EventName": "DECODE_STALL.IQ_FULL", "BriefDescription": "Decode stall due to IQ full", "PublicDescription": "Decode stall due to IQ full", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x1", "EventName": "BR_INST_TYPE_RETIRED.COND", "BriefDescription": "All macro conditional branch instructions.", "PublicDescription": "All macro conditional branch instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x2", "EventName": "BR_INST_TYPE_RETIRED.UNCOND", "BriefDescription": "All macro unconditional branch instructions, excluding calls and indirects", "PublicDescription": "All macro unconditional branch instructions, excluding calls and indirects", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x4", "EventName": "BR_INST_TYPE_RETIRED.IND", "BriefDescription": "All indirect branches that are not calls.", "PublicDescription": "All indirect branches that are not calls.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x8", "EventName": "BR_INST_TYPE_RETIRED.RET", "BriefDescription": "All indirect branches that have a return mnemonic", "PublicDescription": "All indirect branches that have a return mnemonic", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x10", "EventName": "BR_INST_TYPE_RETIRED.DIR_CALL", "BriefDescription": "All non-indirect calls", "PublicDescription": "All non-indirect calls", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x20", "EventName": "BR_INST_TYPE_RETIRED.IND_CALL", "BriefDescription": "All indirect calls, including both register and memory indirect.", "PublicDescription": "All indirect calls, including both register and memory indirect.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x41", "EventName": "BR_INST_TYPE_RETIRED.COND_TAKEN", "BriefDescription": "Only taken macro conditional branch instructions", "PublicDescription": "Only taken macro conditional branch instructions", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x1", "EventName": "BR_MISSP_TYPE_RETIRED.COND", "BriefDescription": "Mispredicted cond branch instructions retired", "PublicDescription": "Mispredicted cond branch instructions retired", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x2", "EventName": "BR_MISSP_TYPE_RETIRED.IND", "BriefDescription": "Mispredicted ind branches that are not calls", "PublicDescription": "Mispredicted ind branches that are not calls", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x4", "EventName": "BR_MISSP_TYPE_RETIRED.RETURN", "BriefDescription": "Mispredicted return branches", "PublicDescription": "Mispredicted return branches", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x8", "EventName": "BR_MISSP_TYPE_RETIRED.IND_CALL", "BriefDescription": "Mispredicted indirect calls, including both register and memory indirect. ", "PublicDescription": "Mispredicted indirect calls, including both register and memory indirect. ", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x11", "EventName": "BR_MISSP_TYPE_RETIRED.COND_TAKEN", "BriefDescription": "Mispredicted and taken cond branch instructions retired", "PublicDescription": "Mispredicted and taken cond branch instructions retired", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xAA", "UMask": "0x1", "EventName": "MACRO_INSTS.NON_CISC_DECODED", "BriefDescription": "Non-CISC nacro instructions decoded", "PublicDescription": "Non-CISC nacro instructions decoded", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xAA", "UMask": "0x2", "EventName": "MACRO_INSTS.CISC_DECODED", "BriefDescription": "CISC macro instructions decoded", "PublicDescription": "CISC macro instructions decoded", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xAA", "UMask": "0x3", "EventName": "MACRO_INSTS.ALL_DECODED", "BriefDescription": "All Instructions decoded", "PublicDescription": "All Instructions decoded", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x0", "EventName": "SIMD_UOPS_EXEC.S", "BriefDescription": "SIMD micro-ops executed (excluding stores).", "PublicDescription": "SIMD micro-ops executed (excluding stores).", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x80", "EventName": "SIMD_UOPS_EXEC.AR", "BriefDescription": "SIMD micro-ops retired (excluding stores).", "PublicDescription": "SIMD micro-ops retired (excluding stores).", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x0", "EventName": "SIMD_SAT_UOP_EXEC.S", "BriefDescription": "SIMD saturated arithmetic micro-ops executed.", "PublicDescription": "SIMD saturated arithmetic micro-ops executed.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x80", "EventName": "SIMD_SAT_UOP_EXEC.AR", "BriefDescription": "SIMD saturated arithmetic micro-ops retired.", "PublicDescription": "SIMD saturated arithmetic micro-ops retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x1", "EventName": "SIMD_UOP_TYPE_EXEC.MUL.S", "BriefDescription": "SIMD packed multiply micro-ops executed", "PublicDescription": "SIMD packed multiply micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x81", "EventName": "SIMD_UOP_TYPE_EXEC.MUL.AR", "BriefDescription": "SIMD packed multiply micro-ops retired", "PublicDescription": "SIMD packed multiply micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x2", "EventName": "SIMD_UOP_TYPE_EXEC.SHIFT.S", "BriefDescription": "SIMD packed shift micro-ops executed", "PublicDescription": "SIMD packed shift micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x82", "EventName": "SIMD_UOP_TYPE_EXEC.SHIFT.AR", "BriefDescription": "SIMD packed shift micro-ops retired", "PublicDescription": "SIMD packed shift micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x4", "EventName": "SIMD_UOP_TYPE_EXEC.PACK.S", "BriefDescription": "SIMD packed micro-ops executed", "PublicDescription": "SIMD packed micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x84", "EventName": "SIMD_UOP_TYPE_EXEC.PACK.AR", "BriefDescription": "SIMD packed micro-ops retired", "PublicDescription": "SIMD packed micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x8", "EventName": "SIMD_UOP_TYPE_EXEC.UNPACK.S", "BriefDescription": "SIMD unpacked micro-ops executed", "PublicDescription": "SIMD unpacked micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x88", "EventName": "SIMD_UOP_TYPE_EXEC.UNPACK.AR", "BriefDescription": "SIMD unpacked micro-ops retired", "PublicDescription": "SIMD unpacked micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x10", "EventName": "SIMD_UOP_TYPE_EXEC.LOGICAL.S", "BriefDescription": "SIMD packed logical micro-ops executed", "PublicDescription": "SIMD packed logical micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x90", "EventName": "SIMD_UOP_TYPE_EXEC.LOGICAL.AR", "BriefDescription": "SIMD packed logical micro-ops retired", "PublicDescription": "SIMD packed logical micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0x20", "EventName": "SIMD_UOP_TYPE_EXEC.ARITHMETIC.S", "BriefDescription": "SIMD packed arithmetic micro-ops executed", "PublicDescription": "SIMD packed arithmetic micro-ops executed", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xB3", "UMask": "0xA0", "EventName": "SIMD_UOP_TYPE_EXEC.ARITHMETIC.AR", "BriefDescription": "SIMD packed arithmetic micro-ops retired", "PublicDescription": "SIMD packed arithmetic micro-ops retired", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC0", "UMask": "0x0", "EventName": "INST_RETIRED.ANY_P", "BriefDescription": "Instructions retired (precise event).", "PublicDescription": "Instructions retired (precise event).", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xA", "UMask": "0x0", "EventName": "INST_RETIRED.ANY", "BriefDescription": "Instructions retired.", "PublicDescription": "Instructions retired.", "Counter": "Fixed counter 1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x10", "EventName": "UOPS_RETIRED.ANY", "BriefDescription": "Micro-ops retired.", "PublicDescription": "Micro-ops retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x10", "EventName": "UOPS_RETIRED.STALLED_CYCLES", "BriefDescription": "Cycles no micro-ops retired.", "PublicDescription": "Cycles no micro-ops retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x10", "EventName": "UOPS_RETIRED.STALLS", "BriefDescription": "Periods no micro-ops retired.", "PublicDescription": "Periods no micro-ops retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xA9", "UMask": "0x1", "EventName": "UOPS.MS_CYCLES", "BriefDescription": "This event counts the cycles where 1 or more uops are issued by the micro-sequencer (MS), including microcode assists and inserted flows, and written to the IQ. ", "PublicDescription": "This event counts the cycles where 1 or more uops are issued by the micro-sequencer (MS), including microcode assists and inserted flows, and written to the IQ. ", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC3", "UMask": "0x1", "EventName": "MACHINE_CLEARS.SMC", "BriefDescription": "Self-Modifying Code detected.", "PublicDescription": "Self-Modifying Code detected.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x0", "EventName": "BR_INST_RETIRED.ANY", "BriefDescription": "Retired branch instructions.", "PublicDescription": "Retired branch instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x1", "EventName": "BR_INST_RETIRED.PRED_NOT_TAKEN", "BriefDescription": "Retired branch instructions that were predicted not-taken.", "PublicDescription": "Retired branch instructions that were predicted not-taken.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x2", "EventName": "BR_INST_RETIRED.MISPRED_NOT_TAKEN", "BriefDescription": "Retired branch instructions that were mispredicted not-taken.", "PublicDescription": "Retired branch instructions that were mispredicted not-taken.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x4", "EventName": "BR_INST_RETIRED.PRED_TAKEN", "BriefDescription": "Retired branch instructions that were predicted taken.", "PublicDescription": "Retired branch instructions that were predicted taken.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x8", "EventName": "BR_INST_RETIRED.MISPRED_TAKEN", "BriefDescription": "Retired branch instructions that were mispredicted taken.", "PublicDescription": "Retired branch instructions that were mispredicted taken.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0xC", "EventName": "BR_INST_RETIRED.TAKEN", "BriefDescription": "Retired taken branch instructions.", "PublicDescription": "Retired taken branch instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0xF", "EventName": "BR_INST_RETIRED.ANY1", "BriefDescription": "Retired branch instructions.", "PublicDescription": "Retired branch instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC5", "UMask": "0x0", "EventName": "BR_INST_RETIRED.MISPRED", "BriefDescription": "Retired mispredicted branch instructions (precise event).", "PublicDescription": "Retired mispredicted branch instructions (precise event).", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC6", "UMask": "0x1", "EventName": "CYCLES_INT_MASKED.CYCLES_INT_MASKED", "BriefDescription": "Cycles during which interrupts are disabled.", "PublicDescription": "Cycles during which interrupts are disabled.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC6", "UMask": "0x2", "EventName": "CYCLES_INT_MASKED.CYCLES_INT_PENDING_AND_MASKED", "BriefDescription": "Cycles during which interrupts are pending and disabled.", "PublicDescription": "Cycles during which interrupts are pending and disabled.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC7", "UMask": "0x1", "EventName": "SIMD_INST_RETIRED.PACKED_SINGLE", "BriefDescription": "Retired Streaming SIMD Extensions (SSE) packed-single instructions.", "PublicDescription": "Retired Streaming SIMD Extensions (SSE) packed-single instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC7", "UMask": "0x2", "EventName": "SIMD_INST_RETIRED.SCALAR_SINGLE", "BriefDescription": "Retired Streaming SIMD Extensions (SSE) scalar-single instructions.", "PublicDescription": "Retired Streaming SIMD Extensions (SSE) scalar-single instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC7", "UMask": "0x8", "EventName": "SIMD_INST_RETIRED.SCALAR_DOUBLE", "BriefDescription": "Retired Streaming SIMD Extensions 2 (SSE2) scalar-double instructions.", "PublicDescription": "Retired Streaming SIMD Extensions 2 (SSE2) scalar-double instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC7", "UMask": "0x10", "EventName": "SIMD_INST_RETIRED.VECTOR", "BriefDescription": "Retired Streaming SIMD Extensions 2 (SSE2) vector instructions.", "PublicDescription": "Retired Streaming SIMD Extensions 2 (SSE2) vector instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xC8", "UMask": "0x0", "EventName": "HW_INT_RCV", "BriefDescription": "Hardware interrupts received.", "PublicDescription": "Hardware interrupts received.", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x1", "EventName": "SIMD_COMP_INST_RETIRED.PACKED_SINGLE", "BriefDescription": "Retired computational Streaming SIMD Extensions (SSE) packed-single instructions.", "PublicDescription": "Retired computational Streaming SIMD Extensions (SSE) packed-single instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x2", "EventName": "SIMD_COMP_INST_RETIRED.SCALAR_SINGLE", "BriefDescription": "Retired computational Streaming SIMD Extensions (SSE) scalar-single instructions.", "PublicDescription": "Retired computational Streaming SIMD Extensions (SSE) scalar-single instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x8", "EventName": "SIMD_COMP_INST_RETIRED.SCALAR_DOUBLE", "BriefDescription": "Retired computational Streaming SIMD Extensions 2 (SSE2) scalar-double instructions.", "PublicDescription": "Retired computational Streaming SIMD Extensions 2 (SSE2) scalar-double instructions.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCB", "UMask": "0x1", "EventName": "MEM_LOAD_RETIRED.L2_HIT", "BriefDescription": "Retired loads that hit the L2 cache (precise event).", "PublicDescription": "Retired loads that hit the L2 cache (precise event).", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCB", "UMask": "0x2", "EventName": "MEM_LOAD_RETIRED.L2_MISS", "BriefDescription": "Retired loads that miss the L2 cache", "PublicDescription": "Retired loads that miss the L2 cache", "Counter": "0,1", "SampleAfterValue": "10000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCB", "UMask": "0x4", "EventName": "MEM_LOAD_RETIRED.DTLB_MISS", "BriefDescription": "Retired loads that miss the DTLB (precise event).", "PublicDescription": "Retired loads that miss the DTLB (precise event).", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x0", "EventName": "SIMD_ASSIST", "BriefDescription": "SIMD assists invoked.", "PublicDescription": "SIMD assists invoked.", "Counter": "0,1", "SampleAfterValue": "100000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCE", "UMask": "0x0", "EventName": "SIMD_INSTR_RETIRED", "BriefDescription": "SIMD Instructions retired.", "PublicDescription": "SIMD Instructions retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xCF", "UMask": "0x0", "EventName": "SIMD_SAT_INSTR_RETIRED", "BriefDescription": "Saturated arithmetic instructions retired.", "PublicDescription": "Saturated arithmetic instructions retired.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xDC", "UMask": "0x2", "EventName": "RESOURCE_STALLS.DIV_BUSY", "BriefDescription": "Cycles issue is stalled due to div busy.", "PublicDescription": "Cycles issue is stalled due to div busy.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xE0", "UMask": "0x1", "EventName": "BR_INST_DECODED", "BriefDescription": "Branch instructions decoded", "PublicDescription": "Branch instructions decoded", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xE4", "UMask": "0x1", "EventName": "BOGUS_BR", "BriefDescription": "Bogus branches", "PublicDescription": "Bogus branches", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0xE6", "UMask": "0x1", "EventName": "BACLEARS.ANY", "BriefDescription": "BACLEARS asserted.", "PublicDescription": "BACLEARS asserted.", "Counter": "0,1", "SampleAfterValue": "2000000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3", "UMask": "0x1", "EventName": "REISSUE.OVERLAP_STORE", "BriefDescription": "Micro-op reissues on a store-load collision", "PublicDescription": "Micro-op reissues on a store-load collision", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}, {"EventCode": "0x3", "UMask": "0x81", "EventName": "REISSUE.OVERLAP_STORE.AR", "BriefDescription": "Micro-op reissues on a store-load collision (At Retirement)", "PublicDescription": "Micro-op reissues on a store-load collision (At Retirement)", "Counter": "0,1", "SampleAfterValue": "200000", "MSRIndex": "0", "MSRValue": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "Errata": "null", "Offcore": "0"}]