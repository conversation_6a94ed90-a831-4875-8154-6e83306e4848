[package]
name = "fs"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
log = "0.4"
bitflags = "2.0.2"
devfs = { workspace = true }
procfs = { workspace = true }
ramfs = { workspace = true }
vfscore = { workspace = true }
syscalls = { workspace = true }
sync = { workspace = true }
devices = { workspace = true }

[target.'cfg(root_fs = "ext4_rs")'.dependencies]
ext4_rs = "1.3.1"

[target.'cfg(root_fs = "ext4")'.dependencies]
lwext4_rust = { git = "https://github.com/Azure-stars/lwext4_rust.git", default-features = false, rev = "ee5131c" }

[target.'cfg(root_fs = "fat32")'.dependencies.fatfs]
git = "https://github.com/byte-os/rust-fatfs.git"
# rev = "a3a834e"
default-features = false
features = ["alloc", "lfn", "unicode"]
