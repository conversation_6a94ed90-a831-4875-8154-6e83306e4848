use super::UserTask;
use crate::tasks::initproc::{get_dyn_path, get_libc_path}; // 添加get_glibc_path导入
use crate::{
    consts::USER_DYN_ADDR,
    tasks::{
        elf::{init_task_stack, ElfExtra},
        MapTrack, MemArea, MemType,
    },
};
use alloc::{
    boxed::Box,
    string::{String, ToString},
    sync::Arc,
    vec::Vec,
};
use async_recursion::async_recursion;
use core::ops::{Add, Mul};
use devices::{frame_alloc_much, FrameTracker, PAGE_SIZE};
use fs::{file::File, pathbuf::PathBuf, OpenFlags};
use polyhal::MappingFlags;
use sync::Mutex;
use syscalls::Errno;
use xmas_elf::program::{SegmentData, Type};

// 添加libc类型枚举
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
enum LibcType {
    Musl,
    Glibc,
    Unknown,
}

// 添加检测libc类型的函数
fn detect_libc_type(elf: &xmas_elf::ElfFile) -> LibcType {
    if let Some(header) = elf
        .program_iter()
        .find(|ph| ph.get_type() == Ok(Type::Interp))
    {
        if let Ok(SegmentData::Undefined(data)) = header.get_data(&elf) {
            if let Ok(interp_path) = core::str::from_utf8(data) {
                let interp_path = interp_path.trim_end_matches('\0');
                if interp_path.contains("musl") {
                    return LibcType::Musl;
                } else if interp_path.contains("glibc") || interp_path.contains("ld-linux") {
                    return LibcType::Glibc;
                }
            }
        }
    }
    LibcType::Unknown
}

pub struct TaskCacheTemplate {
    name: PathBuf,
    entry: usize,
    maps: Vec<MemArea>,
    base: usize,
    heap_bottom: usize,
    ph_count: usize,
    ph_entry_size: usize,
    ph_addr: usize,
}

pub static TASK_CACHES: Mutex<Vec<TaskCacheTemplate>> = Mutex::new(Vec::new());

pub fn cache_task_template(path: PathBuf) -> Result<(), Errno> {
    let file = File::open(path.clone(), OpenFlags::O_RDONLY)?;
    let file_size = file.file_size()?;
    let frame_paddr = frame_alloc_much(file_size.div_ceil(PAGE_SIZE));
    let buffer = frame_paddr.as_ref().unwrap()[0].slice_mut_with_len(file_size);
    let rsize = file.readat(0, buffer)?;
    assert_eq!(rsize, file_size);

    if let Ok(elf) = xmas_elf::ElfFile::new(&buffer) {
        let elf_header = elf.header;
        let entry_point = elf.header.pt2.entry_point() as usize;

        assert_eq!(
            elf_header.pt1.magic,
            [0x7f, 0x45, 0x4c, 0x46],
            "invalid elf!"
        );

        let header = elf
            .program_iter()
            .find(|ph| ph.get_type() == Ok(Type::Interp));

        if let Some(_header) = header {
            unimplemented!("can't cache dynamic file.");
        }

        let heap_bottom = elf
            .program_iter()
            .map(|x| (x.virtual_addr() + x.mem_size()) as usize)
            .max()
            .unwrap()
            .div_ceil(PAGE_SIZE)
            .mul(PAGE_SIZE);

        let base = elf.relocate(USER_DYN_ADDR).unwrap_or(0);
        let mut maps = Vec::new();

        elf.program_iter()
            .filter(|x| x.get_type().unwrap() == xmas_elf::program::Type::Load)
            .for_each(|ph| {
                let file_size = ph.file_size() as usize;
                let mem_size = ph.mem_size() as usize;
                let offset = ph.offset() as usize;
                let virt_addr = base + ph.virtual_addr() as usize;
                let vpn = virt_addr / PAGE_SIZE;

                let page_count = (virt_addr + mem_size).div_ceil(PAGE_SIZE) - vpn;
                let pages: Vec<Arc<FrameTracker>> = frame_alloc_much(page_count)
                    .expect("can't alloc in cache task template")
                    .into_iter()
                    .map(|x| Arc::new(x))
                    .collect();
                let ppn_space = pages[0]
                    .add(virt_addr % PAGE_SIZE)
                    .slice_mut_with_len(file_size);

                ppn_space.copy_from_slice(&buffer[offset..offset + file_size]);

                maps.push(MemArea {
                    mtype: MemType::Mmap,
                    mtrackers: pages
                        .into_iter()
                        .enumerate()
                        .map(|(i, x)| MapTrack {
                            vaddr: va!((vpn + i) * PAGE_SIZE),
                            tracker: x,
                            rwx: 0,
                        })
                        .collect(),
                    file: None,
                    offset: 0,
                    start: vpn * PAGE_SIZE,
                    len: page_count * PAGE_SIZE,
                })
            });
        TASK_CACHES.lock().push(TaskCacheTemplate {
            name: path,
            entry: entry_point,
            maps,
            base,
            heap_bottom,
            ph_count: elf_header.pt2.ph_count() as _,
            ph_entry_size: elf_header.pt2.ph_entry_size() as _,
            ph_addr: elf.get_ph_addr().unwrap_or(0) as _,
        });
    }
    Ok(())
}

#[async_recursion(Sync)]
pub async fn exec_with_process(
    task: Arc<UserTask>,
    curr_dir: PathBuf,
    path: String,
    args: Vec<String>,
    envp: Vec<String>,
) -> Result<Arc<UserTask>, Errno> {
    let path = curr_dir.join(&path);

    let user_task = task.clone();
    
    // 修复：在清空内存集合之前先恢复到干净的页表状态
    // 这避免了页表指向已经清空的内存区域，防止riscv架构下的内核页错误
    
    // 第一步：获取并保存当前页表的干净状态
    let clean_page_table = user_task.page_table.clone();
    
    // 第二步：同步确保所有之前的内存访问都完成
    core::sync::atomic::fence(core::sync::atomic::Ordering::SeqCst);
    
    // 第三步：清理内存集合，但保持页表完整性
    {
        let mut pcb = user_task.pcb.lock();
        // 在清空之前，确保每个内存区域都正确取消映射
        for area in pcb.memset.iter() {
            // 逐页取消映射以避免突然的映射失效
            for tracker in area.mtrackers.iter() {
                user_task.page_table.unmap_page(tracker.vaddr);
            }
        }
        pcb.memset.clear();
    }
    
    // 第四步：恢复页表并应用更改
    user_task.page_table.restore();
    user_task.page_table.change();

    let caches = TASK_CACHES.lock();
    if let Some(cache_task) = caches.iter().find(|x| x.name == path) {
        init_task_stack(
            user_task.clone(),
            args,
            cache_task.base,
            &path.path(),
            cache_task.entry,
            cache_task.ph_count,
            cache_task.ph_entry_size,
            cache_task.ph_addr,
            cache_task.heap_bottom,
        );

        for area in &cache_task.maps {
            user_task.inner_map(|pcb| {
                pcb.memset
                    .sub_area(area.start, area.start + area.len, &user_task.page_table);
                pcb.memset.push(area.clone());
            });
            for mtracker in area.mtrackers.iter() {
                user_task.map(mtracker.tracker.0, mtracker.vaddr, MappingFlags::URX);
            }
        }
        Ok(user_task)
    } else {
        drop(caches);

        let file = File::open(path.clone(), OpenFlags::O_RDONLY)
            .map(Arc::new)?
            .clone();
        let file_size = file.file_size()?;
        let frame_ppn = frame_alloc_much(file_size.div_ceil(PAGE_SIZE));
        let buffer = frame_ppn.as_ref().unwrap()[0].slice_mut_with_len(file_size);
        let rsize = file.readat(0, buffer)?;
        assert_eq!(rsize, file_size);

        let elf = if let Ok(elf) = xmas_elf::ElfFile::new(&buffer) {
            elf
        } else {
            let mut new_args = vec!["busybox".to_string(), "sh".to_string()];
            args.iter().for_each(|x| new_args.push(x.clone()));
            return exec_with_process(task, curr_dir, String::from("busybox"), new_args, envp)
                .await;
        };
        let elf_header = elf.header;

        let entry_point = elf.header.pt2.entry_point() as usize;
        assert_eq!(
            elf_header.pt1.magic,
            [0x7f, 0x45, 0x4c, 0x46],
            "invalid elf!"
        );

        let user_task = task.clone();

        // 检查是否需要动态链接器，并根据libc类型选择相应的链接器
        let header = elf
            .program_iter()
            .find(|ph| ph.get_type() == Ok(Type::Interp));
        if let Some(header) = header {
            if let Ok(SegmentData::Undefined(_data)) = header.get_data(&elf) {
                drop(frame_ppn);

                // 根据检测到的libc类型选择相应的动态链接器
                let libc_path = match detect_libc_type(&elf) {
                    LibcType::Musl => get_libc_path(),
                    LibcType::Glibc => get_dyn_path(),
                    LibcType::Unknown => get_dyn_path(), // 默认使用musl
                };

                let mut new_args = vec![libc_path];
                // 第二个参数应该是要执行的程序的绝对路径
                new_args.push(path.path().to_string());
                // 然后添加原始的程序参数（跳过第一个参数，因为那是程序名）
                if args.len() > 1 {
                    new_args.extend(args[1..].iter().cloned());
                }
                warn!("EXEC: Dynamic linker args: {:?}", new_args);
                return exec_with_process(task, curr_dir, new_args[0].clone(), new_args, envp)
                    .await;
            }
        }

        let heap_bottom = elf
            .program_iter()
            .map(|x| (x.virtual_addr() + x.mem_size()) as usize)
            .max()
            .unwrap()
            .div_ceil(PAGE_SIZE)
            .mul(PAGE_SIZE);

        let base = elf.relocate(USER_DYN_ADDR).unwrap_or(0);
        init_task_stack(
            user_task.clone(),
            args,
            base,
            &path.path(),
            entry_point,
            elf_header.pt2.ph_count() as usize,
            elf_header.pt2.ph_entry_size() as usize,
            elf.get_ph_addr().unwrap_or(0) as usize,
            heap_bottom,
        );

        elf.program_iter()
            .filter(|x| x.get_type().unwrap() == xmas_elf::program::Type::Load)
            .for_each(|ph| {
                let file_size = ph.file_size() as usize;
                let mem_size = ph.mem_size() as usize;
                let offset = ph.offset() as usize;
                let virt_addr = base + ph.virtual_addr() as usize;
                let vpn = virt_addr / PAGE_SIZE;

                let page_count = (virt_addr + mem_size).div_ceil(PAGE_SIZE) - vpn;
                let ppn_start =
                    user_task.frame_alloc(va!(virt_addr).floor(), MemType::Mmap, page_count);
                let page_space = va!(virt_addr).slice_mut_with_len(file_size);
                let ppn_space = ppn_start
                    .expect("not have enough memory")
                    .add(virt_addr % PAGE_SIZE)
                    .slice_mut_with_len(file_size);

                page_space.copy_from_slice(&buffer[offset..offset + file_size]);
                assert_eq!(ppn_space, page_space);
                assert_eq!(&buffer[offset..offset + file_size], ppn_space);
                assert_eq!(&buffer[offset..offset + file_size], page_space);
            });
        Ok(user_task)
    }
}
