{"rust-analyzer.check.allTargets": false, "rust-analyzer.check.extraArgs": [], "rust-analyzer.procMacro.enable": true, "rust-analyzer.cargo.features": "all", "rust-analyzer.cargo.target": "loongarch64-unknown-linux-musl", "rust-analyzer.cargo.extraEnv": {"CARGO_BUILD_TARGET": "loongarch64-unknown-linux-musl", "RUSTFLAGS": "-Clink-arg=-no-pie --cfg=root_fs=\"ext4\" --cfg=loongarch --cfg=riscv", "BOARD": "qemu", "ROOT_MANIFEST_DIR": "${workspaceFolder}"}, "deno.enable": true}