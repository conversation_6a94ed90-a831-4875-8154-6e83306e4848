[{"EventCode": "0x00", "UMask": "0x01", "EventName": "INST_RETIRED.ANY", "BriefDescription": "Instructions retired from execution.", "PublicDescription": "Instructions retired from execution.", "Counter": "Fixed counter 0", "CounterHTOff": "Fixed counter 0", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x00", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.THREAD", "BriefDescription": "Core cycles when the thread is not in halt state.", "PublicDescription": "Core cycles when the thread is not in halt state.", "Counter": "Fixed counter 1", "CounterHTOff": "Fixed counter 1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x00", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.THREAD_ANY", "BriefDescription": "Core cycles when at least one thread on the physical core is not in halt state", "PublicDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "Counter": "Fixed counter 1", "CounterHTOff": "Fixed counter 1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x00", "UMask": "0x03", "EventName": "CPU_CLK_UNHALTED.REF_TSC", "BriefDescription": "Reference cycles when the core is not in halt state.", "PublicDescription": "Reference cycles when the core is not in halt state.", "Counter": "Fixed counter 2", "CounterHTOff": "Fixed counter 2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x03", "UMask": "0x02", "EventName": "LD_BLOCKS.STORE_FORWARD", "BriefDescription": "Cases when loads get true Block-on-Store blocking code preventing store forwarding", "PublicDescription": "Loads blocked by overlapping with store buffer that cannot be forwarded.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x03", "UMask": "0x08", "EventName": "LD_BLOCKS.NO_SR", "BriefDescription": "This event counts the number of times that split load operations are temporarily blocked because all resources for handling the split accesses are in use.", "PublicDescription": "The number of times that split load operations are temporarily blocked because all resources for handling the split accesses are in use.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x05", "UMask": "0x01", "EventName": "MISALIGN_MEM_REF.LOADS", "BriefDescription": "Speculative cache line split load uops dispatched to L1 cache", "PublicDescription": "Speculative cache-line split load uops dispatched to L1D.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x05", "UMask": "0x02", "EventName": "MISALIGN_MEM_REF.STORES", "BriefDescription": "Speculative cache line split STA uops dispatched to L1 cache", "PublicDescription": "Speculative cache-line split Store-address uops dispatched to L1D.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x07", "UMask": "0x01", "EventName": "LD_BLOCKS_PARTIAL.ADDRESS_ALIAS", "BriefDescription": "False dependencies in MOB due to partial compare on address", "PublicDescription": "False dependencies in MOB due to partial compare on address.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x08", "UMask": "0x81", "EventName": "DTLB_LOAD_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Demand load Miss in all translation lookaside buffer (TLB) levels causes an page walk of any page size.", "PublicDescription": "Misses in all TLB levels that cause a page walk of any page size from demand loads.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x08", "UMask": "0x82", "EventName": "DTLB_LOAD_MISSES.WALK_COMPLETED", "BriefDescription": "Demand load Miss in all translation lookaside buffer (TLB) levels causes a page walk that completes of any page size.", "PublicDescription": "Misses in all TLB levels that caused page walk completed of any size by demand loads.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x08", "UMask": "0x84", "EventName": "DTLB_LOAD_MISSES.WALK_DURATION", "BriefDescription": "Demand load cycles page miss handler (PMH) is busy with this walk.", "PublicDescription": "Cycle PMH is busy with a walk due to demand loads.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x08", "UMask": "0x88", "EventName": "DTLB_LOAD_MISSES.LARGE_PAGE_WALK_COMPLETED", "BriefDescription": "Page walk for a large page completed for Demand load.", "PublicDescription": "Page walk for a large page completed for Demand load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0D", "UMask": "0x03", "EventName": "INT_MISC.RECOVERY_CYCLES", "BriefDescription": "Number of cycles waiting for the checkpoints in Resource Allocation Table (RAT) to be recovered after Nuke due to all other cases except JEClear (e.g. whenever a ucode assist is needed like SSE exception, memory disambiguation, etc.)", "PublicDescription": "Number of cycles waiting for the checkpoints in Resource Allocation Table (RAT) to be recovered after Nuke due to all other cases except JEClear (e.g. whenever a ucode assist is needed like SSE exception, memory disambiguation, etc.)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0D", "UMask": "0x03", "EventName": "INT_MISC.RECOVERY_STALLS_COUNT", "BriefDescription": "Number of occurences waiting for the checkpoints in Resource Allocation Table (RAT) to be recovered after Nuke due to all other cases except JEClear (e.g. whenever a ucode assist is needed like SSE exception, memory disambiguation, etc.)", "PublicDescription": "Number of occurences waiting for the checkpoints in Resource Allocation Table (RAT) to be recovered after Nuke due to all other cases except JEClear (e.g. whenever a ucode assist is needed like SSE exception, memory disambiguation, etc.)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0D", "UMask": "0x03", "EventName": "INT_MISC.RECOVERY_CYCLES_ANY", "BriefDescription": "Core cycles the allocator was stalled due to recovery from earlier clear event for any thread running on the physical core (e.g. misprediction or memory nuke).", "PublicDescription": "Core cycles the allocator was stalled due to recovery from earlier clear event for any thread running on the physical core (e.g. misprediction or memory nuke).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x01", "EventName": "UOPS_ISSUED.ANY", "BriefDescription": "Uops that Resource Allocation Table (RAT) issues to Reservation Station (RS)", "PublicDescription": "Increments each cycle the # of Uops issued by the RAT to RS. Set Cmask = 1, Inv = 1, Any= 1to count stalled cycles of this core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x01", "EventName": "UOPS_ISSUED.STALL_CYCLES", "BriefDescription": "Cycles when Resource Allocation Table (RAT) does not issue Uops to Reservation Station (RS) for the thread", "PublicDescription": "Cycles when Resource Allocation Table (RAT) does not issue Uops to Reservation Station (RS) for the thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x01", "EventName": "UOPS_ISSUED.CORE_STALL_CYCLES", "BriefDescription": "Cycles when Resource Allocation Table (RAT) does not issue Uops to Reservation Station (RS) for all threads", "PublicDescription": "Cycles when Resource Allocation Table (RAT) does not issue Uops to Reservation Station (RS) for all threads.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x10", "EventName": "UOPS_ISSUED.FLAGS_MERGE", "BriefDescription": "Number of flags-merge uops being allocated.", "PublicDescription": "Number of flags-merge uops allocated. Such uops adds delay.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x20", "EventName": "UOPS_ISSUED.SLOW_LEA", "BriefDescription": "Number of slow LEA uops being allocated. A uop is generally considered SlowLea if it has 3 sources (e.g. 2 sources + immediate) regardless if as a result of LEA instruction or not.", "PublicDescription": "Number of slow LEA or similar uops allocated. Such uop has 3 sources (e.g. 2 sources + immediate) regardless if as a result of LEA instruction or not.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x0E", "UMask": "0x40", "EventName": "UOPS_ISSUED.SINGLE_MUL", "BriefDescription": "Number of Multiply packed/scalar single precision uops allocated", "PublicDescription": "Number of multiply packed/scalar single precision uops allocated.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x01", "EventName": "FP_COMP_OPS_EXE.X87", "BriefDescription": "Number of FP Computational Uops Executed this cycle. The number of FADD, FSUB, FCOM, FMULs, integer MULsand IMULs, FDIVs, FPREMs, FSQRTS, integer DIVs, and IDIVs. This event does not distinguish an FADD used in the middle of a transcendental flow from a s", "PublicDescription": "Counts number of X87 uops executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x10", "EventName": "FP_COMP_OPS_EXE.SSE_PACKED_DOUBLE", "BriefDescription": "Number of SSE* or AVX-128 FP Computational packed double-precision uops issued this cycle", "PublicDescription": "Number of SSE* or AVX-128 FP Computational packed double-precision uops issued this cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x20", "EventName": "FP_COMP_OPS_EXE.SSE_SCALAR_SINGLE", "BriefDescription": "Number of SSE* or AVX-128 FP Computational scalar single-precision uops issued this cycle", "PublicDescription": "Number of SSE* or AVX-128 FP Computational scalar single-precision uops issued this cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x40", "EventName": "FP_COMP_OPS_EXE.SSE_PACKED_SINGLE", "BriefDescription": "Number of SSE* or AVX-128 FP Computational packed single-precision uops issued this cycle", "PublicDescription": "Number of SSE* or AVX-128 FP Computational packed single-precision uops issued this cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x10", "UMask": "0x80", "EventName": "FP_COMP_OPS_EXE.SSE_SCALAR_DOUBLE", "BriefDescription": "Number of SSE* or AVX-128 FP Computational scalar double-precision uops issued this cycle", "PublicDescription": "Counts number of SSE* or AVX-128 double precision FP scalar uops executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x11", "UMask": "0x01", "EventName": "SIMD_FP_256.PACKED_SINGLE", "BriefDescription": "number of GSSE-256 Computational FP single precision uops issued this cycle", "PublicDescription": "Counts 256-bit packed single-precision floating-point instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x11", "UMask": "0x02", "EventName": "SIMD_FP_256.PACKED_DOUBLE", "BriefDescription": "number of AVX-256 Computational FP double precision uops issued this cycle", "PublicDescription": "Counts 256-bit packed double-precision floating-point instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x14", "UMask": "0x01", "EventName": "ARITH.FPU_DIV_ACTIVE", "BriefDescription": "Cycles when divider is busy executing divide operations", "PublicDescription": "Cycles that the divider is active, includes INT and FP. Set 'edge =1, cmask=1' to count the number of divides.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x14", "UMask": "0x04", "EventName": "ARITH.FPU_DIV", "BriefDescription": "Divide operations executed", "PublicDescription": "Divide operations executed.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x01", "EventName": "L2_RQSTS.DEMAND_DATA_RD_HIT", "BriefDescription": "Demand Data Read requests that hit L2 cache", "PublicDescription": "Demand Data Read requests that hit L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x03", "EventName": "L2_RQSTS.ALL_DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests", "PublicDescription": "Counts any demand and L1 HW prefetch data load requests to L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x04", "EventName": "L2_RQSTS.RFO_HIT", "BriefDescription": "RFO requests that hit L2 cache", "PublicDescription": "RFO requests that hit L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x08", "EventName": "L2_RQSTS.RFO_MISS", "BriefDescription": "RFO requests that miss L2 cache", "PublicDescription": "Counts the number of store RFO requests that miss the L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x0C", "EventName": "L2_RQSTS.ALL_RFO", "BriefDescription": "RFO requests to L2 cache", "PublicDescription": "Counts all L2 store RFO requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x10", "EventName": "L2_RQSTS.CODE_RD_HIT", "BriefDescription": "L2 cache hits when fetching instructions, code reads.", "PublicDescription": "Number of instruction fetches that hit the L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x20", "EventName": "L2_RQSTS.CODE_RD_MISS", "BriefDescription": "L2 cache misses when fetching instructions", "PublicDescription": "Number of instruction fetches that missed the L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x30", "EventName": "L2_RQSTS.ALL_CODE_RD", "BriefDescription": "L2 code requests", "PublicDescription": "Counts all L2 code requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x40", "EventName": "L2_RQSTS.PF_HIT", "BriefDescription": "Requests from the L2 hardware prefetchers that hit L2 cache", "PublicDescription": "Counts all L2 HW prefetcher requests that hit L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0x80", "EventName": "L2_RQSTS.PF_MISS", "BriefDescription": "Requests from the L2 hardware prefetchers that miss L2 cache", "PublicDescription": "Counts all L2 HW prefetcher requests that missed L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x24", "UMask": "0xC0", "EventName": "L2_RQSTS.ALL_PF", "BriefDescription": "Requests from L2 hardware prefetchers", "PublicDescription": "Counts all L2 HW prefetcher requests.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x01", "EventName": "L2_STORE_LOCK_RQSTS.MISS", "BriefDescription": "RFOs that miss cache lines", "PublicDescription": "RFOs that miss cache lines.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x08", "EventName": "L2_STORE_LOCK_RQSTS.HIT_M", "BriefDescription": "RFOs that hit cache lines in M state", "PublicDescription": "RFOs that hit cache lines in M state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x27", "UMask": "0x0F", "EventName": "L2_STORE_LOCK_RQSTS.ALL", "BriefDescription": "RFOs that access cache lines in any state", "PublicDescription": "RFOs that access cache lines in any state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x01", "EventName": "L2_L1D_WB_RQSTS.MISS", "BriefDescription": "Count the number of modified Lines evicted from L1 and missed L2. (Non-rejected WBs from the DCU.)", "PublicDescription": "Not rejected writebacks that missed LLC.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x04", "EventName": "L2_L1D_WB_RQSTS.HIT_E", "BriefDescription": "Not rejected writebacks from L1D to L2 cache lines in E state", "PublicDescription": "Not rejected writebacks from L1D to L2 cache lines in E state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x08", "EventName": "L2_L1D_WB_RQSTS.HIT_M", "BriefDescription": "Not rejected writebacks from L1D to L2 cache lines in M state", "PublicDescription": "Not rejected writebacks from L1D to L2 cache lines in M state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x28", "UMask": "0x0F", "EventName": "L2_L1D_WB_RQSTS.ALL", "BriefDescription": "Not rejected writebacks from L1D to L2 cache lines in any state.", "PublicDescription": "Not rejected writebacks from L1D to L2 cache lines in any state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x41", "EventName": "LONGEST_LAT_CACHE.MISS", "BriefDescription": "Core-originated cacheable demand requests missed LLC", "PublicDescription": "This event counts each cache miss condition for references to the last level cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x2E", "UMask": "0x4F", "EventName": "LONGEST_LAT_CACHE.REFERENCE", "BriefDescription": "Core-originated cacheable demand requests that refer to LLC", "PublicDescription": "This event counts requests originating from the core that reference a cache line in the last level cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x00", "EventName": "CPU_CLK_UNHALTED.THREAD_P", "BriefDescription": "Thread cycles when thread is not in halt state", "PublicDescription": "Counts the number of thread cycles while the thread is not in a halt state. The thread enters the halt state when it is running the HLT instruction. The core frequency may change from time to time due to power or thermal throttling.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x00", "EventName": "CPU_CLK_UNHALTED.THREAD_P_ANY", "BriefDescription": "Core cycles when at least one thread on the physical core is not in halt state", "PublicDescription": "Core cycles when at least one thread on the physical core is not in halt state.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_THREAD_UNHALTED.REF_XCLK", "BriefDescription": "Reference cycles when the thread is unhalted (counts at 100 MHz rate)", "PublicDescription": "Increments at the frequency of XCLK (100 MHz) when not halted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_THREAD_UNHALTED.REF_XCLK_ANY", "BriefDescription": "Reference cycles when the at least one thread on the physical core is unhalted. (counts at 100 MHz rate)", "PublicDescription": "Reference cycles when the at least one thread on the physical core is unhalted. (counts at 100 MHz rate)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_UNHALTED.REF_XCLK", "BriefDescription": "Reference cycles when the thread is unhalted (counts at 100 MHz rate)", "PublicDescription": "Reference cycles when the thread is unhalted. (counts at 100 MHz rate)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x01", "EventName": "CPU_CLK_UNHALTED.REF_XCLK_ANY", "BriefDescription": "Reference cycles when the at least one thread on the physical core is unhalted. (counts at 100 MHz rate)", "PublicDescription": "Reference cycles when the at least one thread on the physical core is unhalted. (counts at 100 MHz rate)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x02", "EventName": "CPU_CLK_THREAD_UNHALTED.ONE_THREAD_ACTIVE", "BriefDescription": "Count <PERSON><PERSON><PERSON> pulses when this thread is unhalted and the other is halted.", "PublicDescription": "Count <PERSON><PERSON><PERSON> pulses when this thread is unhalted and the other is halted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x3C", "UMask": "0x02", "EventName": "CPU_CLK_UNHALTED.ONE_THREAD_ACTIVE", "BriefDescription": "Count <PERSON><PERSON><PERSON> pulses when this thread is unhalted and the other thread is halted.", "PublicDescription": "Count <PERSON><PERSON><PERSON> pulses when this thread is unhalted and the other thread is halted.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING", "BriefDescription": "L1D miss oustandings duration in cycles", "PublicDescription": "Increments the number of outstanding L1D misses every cycle. Set Cmask = 1 and Edge =1 to count occurrences.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING_CYCLES", "BriefDescription": "Cycles with L1D load Misses outstanding.", "PublicDescription": "Cycles with L1D load Misses outstanding.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x48", "UMask": "0x01", "EventName": "L1D_PEND_MISS.PENDING_CYCLES_ANY", "BriefDescription": "Cycles with L1D load Misses outstanding from any thread on physical core", "PublicDescription": "Cycles with L1D load Misses outstanding from any thread on physical core.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x48", "UMask": "0x02", "EventName": "L1D_PEND_MISS.FB_FULL", "BriefDescription": "Cycles a demand request was blocked due to Fill Buffers inavailability", "PublicDescription": "Cycles a demand request was blocked due to Fill Buffers inavailability.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x49", "UMask": "0x01", "EventName": "DTLB_STORE_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Store misses in all DTLB levels that cause page walks", "PublicDescription": "Miss in all TLB levels causes a page walk of any page size (4K/2M/4M/1G).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x49", "UMask": "0x02", "EventName": "DTLB_STORE_MISSES.WALK_COMPLETED", "BriefDescription": "Store misses in all DTLB levels that cause completed page walks", "PublicDescription": "Miss in all TLB levels causes a page walk that completes of any page size (4K/2M/4M/1G).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x49", "UMask": "0x04", "EventName": "DTLB_STORE_MISSES.WALK_DURATION", "BriefDescription": "Cycles when PMH is busy with page walks", "PublicDescription": "Cycles PMH is busy with this walk.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x49", "UMask": "0x10", "EventName": "DTLB_STORE_MISSES.STLB_HIT", "BriefDescription": "Store operations that miss the first TLB level but hit the second and do not cause page walks", "PublicDescription": "Store operations that miss the first TLB level but hit the second and do not cause page walks.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x4C", "UMask": "0x01", "EventName": "LOAD_HIT_PRE.SW_PF", "BriefDescription": "Not software-prefetch load dispatches that hit FB allocated for software prefetch", "PublicDescription": "Non-SW-prefetch load dispatches that hit fill buffer allocated for S/W prefetch.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x4C", "UMask": "0x02", "EventName": "LOAD_HIT_PRE.HW_PF", "BriefDescription": "Not software-prefetch load dispatches that hit FB allocated for hardware prefetch", "PublicDescription": "Non-SW-prefetch load dispatches that hit fill buffer allocated for H/W prefetch.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x4F", "UMask": "0x10", "EventName": "EPT.WALK_CYCLES", "BriefDescription": "Cycle count for an Extended Page table walk.  The Extended Page Directory cache is used by Virtual Machine operating systems while the guest operating systems use the standard TLB caches.", "PublicDescription": "Cycle count for an Extended Page table walk.  The Extended Page Directory cache is used by Virtual Machine operating systems while the guest operating systems use the standard TLB caches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x51", "UMask": "0x01", "EventName": "L1D.REPLACEMENT", "BriefDescription": "L1D data line replacements", "PublicDescription": "Counts the number of lines brought into the L1 data cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x58", "UMask": "0x01", "EventName": "MOVE_ELIMINATION.INT_ELIMINATED", "BriefDescription": "Number of integer Move Elimination candidate uops that were eliminated.", "PublicDescription": "Number of integer Move Elimination candidate uops that were eliminated.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "1000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x58", "UMask": "0x02", "EventName": "MOVE_ELIMINATION.SIMD_ELIMINATED", "BriefDescription": "Number of SIMD Move Elimination candidate uops that were eliminated.", "PublicDescription": "Number of SIMD Move Elimination candidate uops that were eliminated.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "1000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x58", "UMask": "0x04", "EventName": "MOVE_ELIMINATION.INT_NOT_ELIMINATED", "BriefDescription": "Number of integer Move Elimination candidate uops that were not eliminated.", "PublicDescription": "Number of integer Move Elimination candidate uops that were not eliminated.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "1000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x58", "UMask": "0x08", "EventName": "MOVE_ELIMINATION.SIMD_NOT_ELIMINATED", "BriefDescription": "Number of SIMD Move Elimination candidate uops that were not eliminated.", "PublicDescription": "Number of SIMD Move Elimination candidate uops that were not eliminated.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "1000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5C", "UMask": "0x01", "EventName": "CPL_CYCLES.RING0", "BriefDescription": "Unhalted core cycles when the thread is in ring 0", "PublicDescription": "Unhalted core cycles when the thread is in ring 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5C", "UMask": "0x01", "EventName": "CPL_CYCLES.RING0_TRANS", "BriefDescription": "Number of intervals between processor halts while thread is in ring 0", "PublicDescription": "Number of intervals between processor halts while thread is in ring 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5C", "UMask": "0x02", "EventName": "CPL_CYCLES.RING123", "BriefDescription": "Unhalted core cycles when thread is in rings 1, 2, or 3", "PublicDescription": "Unhalted core cycles when the thread is not in ring 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5E", "UMask": "0x01", "EventName": "RS_EVENTS.EMPTY_CYCLES", "BriefDescription": "Cycles when Reservation Station (RS) is empty for the thread", "PublicDescription": "Cycles the RS is empty for the thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5E", "UMask": "0x01", "EventName": "RS_EVENTS.EMPTY_END", "BriefDescription": "Counts end of periods where the Reservation Station (RS) was empty. Could be useful to precisely locate Frontend Latency Bound issues.", "PublicDescription": "Counts end of periods where the Reservation Station (RS) was empty. Could be useful to precisely locate Frontend Latency Bound issues.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x5F", "UMask": "0x04", "EventName": "DTLB_LOAD_MISSES.STLB_HIT", "BriefDescription": "Load operations that miss the first DTLB level but hit the second and do not cause page walks", "PublicDescription": "Counts load operations that missed 1st level DTLB but hit the 2nd level.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_DATA_RD", "BriefDescription": "Offcore outstanding Demand Data Read transactions in uncore queue.", "PublicDescription": "Offcore outstanding Demand Data Read transactions in SQ to uncore. Set Cmask=1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_DATA_RD", "BriefDescription": "Cycles when offcore outstanding Demand Data Read transactions are present in SuperQueue (SQ), queue to uncore", "PublicDescription": "Cycles when offcore outstanding Demand Data Read transactions are present in SuperQueue (SQ), queue to uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_DATA_RD_GE_6", "BriefDescription": "Cycles with at least 6 offcore outstanding Demand Data Read transactions in uncore queue", "PublicDescription": "Cycles with at least 6 offcore outstanding Demand Data Read transactions in uncore queue.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_CODE_RD", "BriefDescription": "Offcore outstanding code reads transactions in SuperQueue (SQ), queue to uncore, every cycle", "PublicDescription": "Offcore outstanding Demand Code Read transactions in SQ to uncore. Set Cmask=1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_CODE_RD", "BriefDescription": "Offcore outstanding code reads transactions in SuperQueue (SQ), queue to uncore, every cycle", "PublicDescription": "Offcore outstanding code reads transactions in SuperQueue (SQ), queue to uncore, every cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.DEMAND_RFO", "BriefDescription": "Offcore outstanding RFO store transactions in SuperQueue (SQ), queue to uncore", "PublicDescription": "Offcore outstanding RFO store transactions in SQ to uncore. Set Cmask=1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DEMAND_RFO", "BriefDescription": "Offcore outstanding demand rfo reads transactions in SuperQueue (SQ), queue to uncore, every cycle", "PublicDescription": "Offcore outstanding demand rfo reads transactions in SuperQueue (SQ), queue to uncore, every cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.ALL_DATA_RD", "BriefDescription": "Offcore outstanding cacheable Core Data Read transactions in SuperQueue (SQ), queue to uncore", "PublicDescription": "Offcore outstanding cacheable data read transactions in SQ to uncore. Set Cmask=1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x60", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS_OUTSTANDING.CYCLES_WITH_DATA_RD", "BriefDescription": "Cycles when offcore outstanding cacheable Core Data Read transactions are present in SuperQueue (SQ), queue to uncore", "PublicDescription": "Cycles when offcore outstanding cacheable Core Data Read transactions are present in SuperQueue (SQ), queue to uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x63", "UMask": "0x01", "EventName": "LOCK_CYCLES.SPLIT_LOCK_UC_LOCK_DURATION", "BriefDescription": "Cycles when L1 and L2 are locked due to UC or split lock", "PublicDescription": "Cycles in which the L1D and L2 are locked, due to a UC lock or split lock.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x63", "UMask": "0x02", "EventName": "LOCK_CYCLES.CACHE_LOCK_DURATION", "BriefDescription": "Cycles when L1D is locked", "PublicDescription": "Cycles in which the L1D is locked.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x02", "EventName": "IDQ.EMPTY", "BriefDescription": "Instruction Decode Queue (IDQ) empty cycles", "PublicDescription": "Counts cycles the IDQ is empty.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x04", "EventName": "IDQ.MITE_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) from MITE path", "PublicDescription": "Increment each cycle # of uops delivered to IDQ from MITE path. Set Cmask = 1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x04", "EventName": "IDQ.MITE_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from MITE path", "PublicDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from MITE path.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x08", "EventName": "IDQ.DSB_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) from the Decode Stream Buffer (DSB) path", "PublicDescription": "Increment each cycle. # of uops delivered to IDQ from DSB path. Set Cmask = 1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x08", "EventName": "IDQ.DSB_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from Decode Stream Buffer (DSB) path", "PublicDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) from Decode Stream Buffer (DSB) path.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x10", "EventName": "IDQ.MS_DSB_UOPS", "BriefDescription": "Uops initiated by Decode Stream Buffer (DSB) that are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Increment each cycle # of uops delivered to IDQ when MS_busy by DSB. Set Cmask = 1 to count cycles. Add Edge=1 to count # of delivery.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x10", "EventName": "IDQ.MS_DSB_CYCLES", "BriefDescription": "Cycles when uops initiated by Decode Stream Buffer (DSB) are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Cycles when uops initiated by Decode Stream Buffer (DSB) are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x10", "EventName": "IDQ.MS_DSB_OCCUR", "BriefDescription": "Deliveries to Instruction Decode Queue (IDQ) initiated by Decode Stream Buffer (DSB) while Microcode Sequenser (MS) is busy", "PublicDescription": "Deliveries to Instruction Decode Queue (IDQ) initiated by Decode Stream Buffer (DSB) while Microcode Sequenser (MS) is busy.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x18", "EventName": "IDQ.ALL_DSB_CYCLES_4_UOPS", "BriefDescription": "Cycles Decode Stream Buffer (DSB) is delivering 4 Uops", "PublicDescription": "Counts cycles DSB is delivered four uops. Set Cmask = 4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x18", "EventName": "IDQ.ALL_DSB_CYCLES_ANY_UOPS", "BriefDescription": "Cycles Decode Stream Buffer (DSB) is delivering any Uop", "PublicDescription": "Counts cycles DSB is delivered at least one uops. Set Cmask = 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x20", "EventName": "IDQ.MS_MITE_UOPS", "BriefDescription": "Uops initiated by MITE and delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Increment each cycle # of uops delivered to IDQ when MS_busy by MITE. Set Cmask = 1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x24", "EventName": "IDQ.ALL_MITE_CYCLES_4_UOPS", "BriefDescription": "Cycles MITE is delivering 4 Uops", "PublicDescription": "Counts cycles MITE is delivered four uops. Set Cmask = 4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x24", "EventName": "IDQ.ALL_MITE_CYCLES_ANY_UOPS", "BriefDescription": "Cycles MITE is delivering any Uop", "PublicDescription": "Counts cycles MITE is delivered at least one uops. Set Cmask = 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Increment each cycle # of uops delivered to IDQ from MS by either DSB or MITE. Set Cmask = 1 to count cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_CYCLES", "BriefDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy", "PublicDescription": "Cycles when uops are being delivered to Instruction Decode Queue (IDQ) while Microcode Sequenser (MS) is busy.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x30", "EventName": "IDQ.MS_SWITCHES", "BriefDescription": "Number of switches from DSB (Decode Stream Buffer) or MITE (legacy decode pipeline) to the Microcode Sequencer", "PublicDescription": "Number of switches from DSB (Decode Stream Buffer) or MITE (legacy decode pipeline) to the Microcode Sequencer.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x79", "UMask": "0x3C", "EventName": "IDQ.MITE_ALL_UOPS", "BriefDescription": "Uops delivered to Instruction Decode Queue (IDQ) from MITE path", "PublicDescription": "Number of uops delivered to IDQ from any path.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x01", "EventName": "ICACHE.HIT", "BriefDescription": "Number of Instruction Cache, Streaming Buffer and Victim Cache Reads. both cacheable and noncacheable, including UC fetches", "PublicDescription": "Number of Instruction Cache, Streaming Buffer and Victim Cache Reads. both cacheable and noncacheable, including UC fetches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x02", "EventName": "ICACHE.MISSES", "BriefDescription": "Instruction cache, streaming buffer and victim cache misses", "PublicDescription": "Number of Instruction Cache, <PERSON><PERSON> Buffer and <PERSON><PERSON>m <PERSON> Miss<PERSON>. Includes UC accesses.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x80", "UMask": "0x04", "EventName": "ICACHE.IFETCH_STALL", "BriefDescription": "Cycles where a code-fetch stalled due to L1 instruction-cache miss or an iTLB miss", "PublicDescription": "Cycles where a code-fetch stalled due to L1 instruction-cache miss or an iTLB miss.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x85", "UMask": "0x01", "EventName": "ITLB_MISSES.MISS_CAUSES_A_WALK", "BriefDescription": "Misses at all ITLB levels that cause page walks", "PublicDescription": "Misses in all ITLB levels that cause page walks.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x85", "UMask": "0x02", "EventName": "ITLB_MISSES.WALK_COMPLETED", "BriefDescription": "Misses in all ITLB levels that cause completed page walks", "PublicDescription": "Misses in all ITLB levels that cause completed page walks.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x85", "UMask": "0x04", "EventName": "ITLB_MISSES.WALK_DURATION", "BriefDescription": "Cycles when PMH is busy with page walks", "PublicDescription": "Cycle PMH is busy with a walk.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x85", "UMask": "0x10", "EventName": "ITLB_MISSES.STLB_HIT", "BriefDescription": "Operations that miss the first ITLB level but hit the second and do not cause any page walks", "PublicDescription": "Number of cache load STLB hits. No page walk.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x85", "UMask": "0x80", "EventName": "ITLB_MISSES.LARGE_PAGE_WALK_COMPLETED", "BriefDescription": "Completed page walks in ITLB due to STLB load misses for large pages", "PublicDescription": "Completed page walks in ITLB due to STLB load misses for large pages.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x87", "UMask": "0x01", "EventName": "ILD_STALL.LCP", "BriefDescription": "Stalls caused by changing prefix length of the instruction.", "PublicDescription": "Stalls caused by changing prefix length of the instruction.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x87", "UMask": "0x04", "EventName": "ILD_STALL.IQ_FULL", "BriefDescription": "Stall cycles because IQ is full", "PublicDescription": "Stall cycles due to IQ is full.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x41", "EventName": "BR_INST_EXEC.NONTAKEN_CONDITIONAL", "BriefDescription": "Not taken macro-conditional branches", "PublicDescription": "Not taken macro-conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x81", "EventName": "BR_INST_EXEC.TAKEN_CONDITIONAL", "BriefDescription": "Taken speculative and retired macro-conditional branches", "PublicDescription": "Taken speculative and retired macro-conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x82", "EventName": "BR_INST_EXEC.TAKEN_DIRECT_JUMP", "BriefDescription": "Taken speculative and retired macro-conditional branch instructions excluding calls and indirects", "PublicDescription": "Taken speculative and retired macro-conditional branch instructions excluding calls and indirects.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x84", "EventName": "BR_INST_EXEC.TAKEN_INDIRECT_JUMP_NON_CALL_RET", "BriefDescription": "Taken speculative and retired indirect branches excluding calls and returns", "PublicDescription": "Taken speculative and retired indirect branches excluding calls and returns.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x88", "EventName": "BR_INST_EXEC.TAKEN_INDIRECT_NEAR_RETURN", "BriefDescription": "Taken speculative and retired indirect branches with return mnemonic", "PublicDescription": "Taken speculative and retired indirect branches with return mnemonic.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0x90", "EventName": "BR_INST_EXEC.TAKEN_DIRECT_NEAR_CALL", "BriefDescription": "Taken speculative and retired direct near calls", "PublicDescription": "Taken speculative and retired direct near calls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xA0", "EventName": "BR_INST_EXEC.TAKEN_INDIRECT_NEAR_CALL", "BriefDescription": "Taken speculative and retired indirect calls", "PublicDescription": "Taken speculative and retired indirect calls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xC1", "EventName": "BR_INST_EXEC.ALL_CONDITIONAL", "BriefDescription": "Speculative and retired macro-conditional branches", "PublicDescription": "Speculative and retired macro-conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xC2", "EventName": "BR_INST_EXEC.ALL_DIRECT_JMP", "BriefDescription": "Speculative and retired macro-unconditional branches excluding calls and indirects", "PublicDescription": "Speculative and retired macro-unconditional branches excluding calls and indirects.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xC4", "EventName": "BR_INST_EXEC.ALL_INDIRECT_JUMP_NON_CALL_RET", "BriefDescription": "Speculative and retired indirect branches excluding calls and returns", "PublicDescription": "Speculative and retired indirect branches excluding calls and returns.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xC8", "EventName": "BR_INST_EXEC.ALL_INDIRECT_NEAR_RETURN", "BriefDescription": "Speculative and retired indirect return branches.", "PublicDescription": "Speculative and retired indirect return branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xD0", "EventName": "BR_INST_EXEC.ALL_DIRECT_NEAR_CALL", "BriefDescription": "Speculative and retired direct near calls", "PublicDescription": "Speculative and retired direct near calls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x88", "UMask": "0xFF", "EventName": "BR_INST_EXEC.ALL_BRANCHES", "BriefDescription": "Speculative and retired  branches", "PublicDescription": "Counts all near executed branches (not necessarily retired).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x41", "EventName": "BR_MISP_EXEC.NONTAKEN_CONDITIONAL", "BriefDescription": "Not taken speculative and retired mispredicted macro conditional branches", "PublicDescription": "Not taken speculative and retired mispredicted macro conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x81", "EventName": "BR_MISP_EXEC.TAKEN_CONDITIONAL", "BriefDescription": "Taken speculative and retired mispredicted macro conditional branches", "PublicDescription": "Taken speculative and retired mispredicted macro conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x84", "EventName": "BR_MISP_EXEC.TAKEN_INDIRECT_JUMP_NON_CALL_RET", "BriefDescription": "Taken speculative and retired mispredicted indirect branches excluding calls and returns", "PublicDescription": "Taken speculative and retired mispredicted indirect branches excluding calls and returns.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0x88", "EventName": "BR_MISP_EXEC.TAKEN_RETURN_NEAR", "BriefDescription": "Taken speculative and retired mispredicted indirect branches with return mnemonic", "PublicDescription": "Taken speculative and retired mispredicted indirect branches with return mnemonic.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0xA0", "EventName": "BR_MISP_EXEC.TAKEN_INDIRECT_NEAR_CALL", "BriefDescription": "Taken speculative and retired mispredicted indirect calls", "PublicDescription": "Taken speculative and retired mispredicted indirect calls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0xC1", "EventName": "BR_MISP_EXEC.ALL_CONDITIONAL", "BriefDescription": "Speculative and retired mispredicted macro conditional branches", "PublicDescription": "Speculative and retired mispredicted macro conditional branches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0xC4", "EventName": "BR_MISP_EXEC.ALL_INDIRECT_JUMP_NON_CALL_RET", "BriefDescription": "Mispredicted indirect branches excluding calls and returns", "PublicDescription": "Mispredicted indirect branches excluding calls and returns.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x89", "UMask": "0xFF", "EventName": "BR_MISP_EXEC.ALL_BRANCHES", "BriefDescription": "Speculative and retired mispredicted macro conditional branches", "PublicDescription": "Counts all near executed branches (not necessarily retired).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CORE", "BriefDescription": "Uops not delivered to Resource Allocation Table (RAT) per thread when backend of the machine is not stalled", "PublicDescription": "Count issue pipeline slots where no uop was delivered from the front end to the back end when there is no back-end stall.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_0_UOPS_DELIV.CORE", "BriefDescription": "Cycles per thread when 4 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled.", "PublicDescription": "Cycles per thread when 4 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_1_UOP_DELIV.CORE", "BriefDescription": "Cycles per thread when 3 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled.", "PublicDescription": "Cycles per thread when 3 or more uops are not delivered to Resource Allocation Table (RAT) when backend of the machine is not stalled.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_2_UOP_DELIV.CORE", "BriefDescription": "Cycles with less than 2 uops delivered by the front end.", "PublicDescription": "Cycles with less than 2 uops delivered by the front end.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_LE_3_UOP_DELIV.CORE", "BriefDescription": "Cycles with less than 3 uops delivered by the front end.", "PublicDescription": "Cycles with less than 3 uops delivered by the front end.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0x9C", "UMask": "0x01", "EventName": "IDQ_UOPS_NOT_DELIVERED.CYCLES_FE_WAS_OK", "BriefDescription": "Counts cycles FE delivered 4 uops or Resource Allocation Table (RAT) was stalling FE.", "PublicDescription": "Counts cycles FE delivered 4 uops or Resource Allocation Table (RAT) was stalling FE.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x01", "EventName": "UOPS_DISPATCHED_PORT.PORT_0", "BriefDescription": "Cycles per thread when uops are dispatched to port 0", "PublicDescription": "Cycles which a Uop is dispatched on port 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x01", "EventName": "UOPS_DISPATCHED_PORT.PORT_0_CORE", "BriefDescription": "Cycles per core when uops are dispatched to port 0", "PublicDescription": "Cycles per core when uops are dispatched to port 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x02", "EventName": "UOPS_DISPATCHED_PORT.PORT_1", "BriefDescription": "Cycles per thread when uops are dispatched to port 1", "PublicDescription": "Cycles which a Uop is dispatched on port 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x02", "EventName": "UOPS_DISPATCHED_PORT.PORT_1_CORE", "BriefDescription": "Cycles per core when uops are dispatched to port 1", "PublicDescription": "Cycles per core when uops are dispatched to port 1.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x0C", "EventName": "UOPS_DISPATCHED_PORT.PORT_2", "BriefDescription": "Cycles per thread when load or STA uops are dispatched to port 2", "PublicDescription": "Cycles which a Uop is dispatched on port 2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x0C", "EventName": "UOPS_DISPATCHED_PORT.PORT_2_CORE", "BriefDescription": "Uops dispatched to port 2, loads and stores per core (speculative and retired).", "PublicDescription": "Uops dispatched to port 2, loads and stores per core (speculative and retired).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x30", "EventName": "UOPS_DISPATCHED_PORT.PORT_3", "BriefDescription": "Cycles per thread when load or STA uops are dispatched to port 3", "PublicDescription": "Cycles which a Uop is dispatched on port 3.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x30", "EventName": "UOPS_DISPATCHED_PORT.PORT_3_CORE", "BriefDescription": "Cycles per core when load or STA uops are dispatched to port 3", "PublicDescription": "Cycles per core when load or STA uops are dispatched to port 3.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x40", "EventName": "UOPS_DISPATCHED_PORT.PORT_4", "BriefDescription": "Cycles per thread when uops are dispatched to port 4", "PublicDescription": "Cycles which a Uop is dispatched on port 4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x40", "EventName": "UOPS_DISPATCHED_PORT.PORT_4_CORE", "BriefDescription": "Cycles per core when uops are dispatched to port 4", "PublicDescription": "Cycles per core when uops are dispatched to port 4.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x80", "EventName": "UOPS_DISPATCHED_PORT.PORT_5", "BriefDescription": "Cycles per thread when uops are dispatched to port 5", "PublicDescription": "Cycles which a Uop is dispatched on port 5.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA1", "UMask": "0x80", "EventName": "UOPS_DISPATCHED_PORT.PORT_5_CORE", "BriefDescription": "Cycles per core when uops are dispatched to port 5", "PublicDescription": "Cycles per core when uops are dispatched to port 5.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA2", "UMask": "0x01", "EventName": "RESOURCE_STALLS.ANY", "BriefDescription": "Resource-related stall cycles", "PublicDescription": "Cycles Allocation is stalled due to Resource Related reason.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA2", "UMask": "0x04", "EventName": "RESOURCE_STALLS.RS", "BriefDescription": "Cycles stalled due to no eligible RS entry available.", "PublicDescription": "Cycles stalled due to no eligible RS entry available.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA2", "UMask": "0x08", "EventName": "RESOURCE_STALLS.SB", "BriefDescription": "Cycles stalled due to no store buffers available. (not including draining form sync).", "PublicDescription": "Cycles stalled due to no store buffers available (not including draining form sync).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA2", "UMask": "0x10", "EventName": "RESOURCE_STALLS.ROB", "BriefDescription": "Cycles stalled due to re-order buffer full.", "PublicDescription": "Cycles stalled due to re-order buffer full.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x01", "EventName": "CYCLE_ACTIVITY.CYCLES_L2_PENDING", "BriefDescription": "Cycles with pending L2 cache miss loads.", "PublicDescription": "Cycles with pending L2 miss loads. Set AnyThread to count per core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x01", "EventName": "CYCLE_ACTIVITY.CYCLES_L2_MISS", "BriefDescription": "Cycles while L2 cache miss load* is outstanding.", "PublicDescription": "Cycles while L2 cache miss load* is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x02", "EventName": "CYCLE_ACTIVITY.CYCLES_LDM_PENDING", "BriefDescription": "Cycles with pending memory loads.", "PublicDescription": "Cycles with pending memory loads. Set AnyThread to count per core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x02", "EventName": "CYCLE_ACTIVITY.CYCLES_MEM_ANY", "BriefDescription": "Cycles while memory subsystem has an outstanding load.", "PublicDescription": "Cycles while memory subsystem has an outstanding load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x04", "EventName": "CYCLE_ACTIVITY.CYCLES_NO_EXECUTE", "BriefDescription": "This event increments by 1 for every cycle where there was no execute for this thread.", "PublicDescription": "Total execution stalls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x04", "EventName": "CYCLE_ACTIVITY.STALLS_TOTAL", "BriefDescription": "Total execution stalls.", "PublicDescription": "Total execution stalls.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x05", "EventName": "CYCLE_ACTIVITY.STALLS_L2_PENDING", "BriefDescription": "Execution stalls due to L2 cache misses.", "PublicDescription": "Number of loads missed L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "5", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x05", "EventName": "CYCLE_ACTIVITY.STALLS_L2_MISS", "BriefDescription": "Execution stalls while L2 cache miss load* is outstanding.", "PublicDescription": "Execution stalls while L2 cache miss load* is outstanding.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "5", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x06", "EventName": "CYCLE_ACTIVITY.STALLS_LDM_PENDING", "BriefDescription": "Execution stalls due to memory subsystem.", "PublicDescription": "Execution stalls due to memory subsystem.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x06", "EventName": "CYCLE_ACTIVITY.STALLS_MEM_ANY", "BriefDescription": "Execution stalls while memory subsystem has an outstanding load.", "PublicDescription": "Execution stalls while memory subsystem has an outstanding load.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "6", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x08", "EventName": "CYCLE_ACTIVITY.CYCLES_L1D_PENDING", "BriefDescription": "Cycles with pending L1 cache miss loads.", "PublicDescription": "Cycles with pending L1 cache miss loads. Set AnyThread to count per core.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "8", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x08", "EventName": "CYCLE_ACTIVITY.CYCLES_L1D_MISS", "BriefDescription": "Cycles while L1 cache miss demand load is outstanding.", "PublicDescription": "Cycles while L1 cache miss demand load is outstanding.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "8", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x0C", "EventName": "CYCLE_ACTIVITY.STALLS_L1D_PENDING", "BriefDescription": "Execution stalls due to L1 data cache misses", "PublicDescription": "Execution stalls due to L1 data cache miss loads. Set Cmask=0CH.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "12", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA3", "UMask": "0x0C", "EventName": "CYCLE_ACTIVITY.STALLS_L1D_MISS", "BriefDescription": "Execution stalls while L1 cache miss demand load is outstanding.", "PublicDescription": "Execution stalls while L1 cache miss demand load is outstanding.", "Counter": "2", "CounterHTOff": "2", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "12", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.UOPS", "BriefDescription": "Number of Uops delivered by the LSD.", "PublicDescription": "Number of Uops delivered by the LSD.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.CYCLES_ACTIVE", "BriefDescription": "Cycles Uops delivered by the LSD, but didn't come from the decoder", "PublicDescription": "Cycles Uops delivered by the LSD, but didn't come from the decoder.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xA8", "UMask": "0x01", "EventName": "LSD.CYCLES_4_UOPS", "BriefDescription": "Cycles 4 Uops delivered by the LSD, but didn't come from the decoder", "PublicDescription": "Cycles 4 Uops delivered by the LSD, but didn't come from the decoder.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xAB", "UMask": "0x01", "EventName": "DSB2MITE_SWITCHES.COUNT", "BriefDescription": "Decode Stream Buffer (DSB)-to-MITE switches", "PublicDescription": "Number of DSB to MITE switches.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xAB", "UMask": "0x02", "EventName": "DSB2MITE_SWITCHES.PENALTY_CYCLES", "BriefDescription": "Decode Stream Buffer (DSB)-to-MITE switch true penalty cycles", "PublicDescription": "Cycles DSB to MITE switches caused delay.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xAC", "UMask": "0x08", "EventName": "DSB_FILL.EXCEED_DSB_LINES", "BriefDescription": "Cycles when Decode Stream Buffer (DSB) fill encounter more than 3 Decode Stream Buffer (DSB) lines", "PublicDescription": "DSB Fill encountered > 3 DSB lines.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xAE", "UMask": "0x01", "EventName": "ITLB.ITLB_FLUSH", "BriefDescription": "Flushing of the Instruction TLB (ITLB) pages, includes 4k/2M/4M pages.", "PublicDescription": "Counts the number of ITLB flushes, includes 4k/2M/4M pages.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS.DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests sent to uncore", "PublicDescription": "Demand data read requests sent to uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x02", "EventName": "OFFCORE_REQUESTS.DEMAND_CODE_RD", "BriefDescription": "Cacheable and noncachaeble code read requests", "PublicDescription": "Demand code read requests sent to uncore.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x04", "EventName": "OFFCORE_REQUESTS.DEMAND_RFO", "BriefDescription": "Demand RFO requests including regular RFOs, locks, ItoM", "PublicDescription": "Demand RFO read requests sent to uncore, including regular RFOs, locks, ItoM.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB0", "UMask": "0x08", "EventName": "OFFCORE_REQUESTS.ALL_DATA_RD", "BriefDescription": "Demand and prefetch data reads", "PublicDescription": "Data read requests sent to uncore (demand and prefetch).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.THREAD", "BriefDescription": "Counts the number of uops to be executed per-thread each cycle.", "PublicDescription": "Counts total number of uops to be executed per-thread each cycle. Set Cmask = 1, INV =1 to count stall cycles.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.STALL_CYCLES", "BriefDescription": "Counts number of cycles no uops were dispatched to be executed on this thread.", "PublicDescription": "Counts number of cycles no uops were dispatched to be executed on this thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_1_UOP_EXEC", "BriefDescription": "Cycles where at least 1 uop was executed per-thread", "PublicDescription": "Cycles where at least 1 uop was executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_2_UOPS_EXEC", "BriefDescription": "Cycles where at least 2 uops were executed per-thread", "PublicDescription": "Cycles where at least 2 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_3_UOPS_EXEC", "BriefDescription": "Cycles where at least 3 uops were executed per-thread", "PublicDescription": "Cycles where at least 3 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x01", "EventName": "UOPS_EXECUTED.CYCLES_GE_4_UOPS_EXEC", "BriefDescription": "Cycles where at least 4 uops were executed per-thread", "PublicDescription": "Cycles where at least 4 uops were executed per-thread.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE", "BriefDescription": "Number of uops executed on the core.", "PublicDescription": "Counts total number of uops to be executed per-core each cycle.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_1", "BriefDescription": "Cycles at least 1 micro-op is executed from any thread on physical core", "PublicDescription": "Cycles at least 1 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_2", "BriefDescription": "Cycles at least 2 micro-op is executed from any thread on physical core", "PublicDescription": "Cycles at least 2 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "2", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_3", "BriefDescription": "Cycles at least 3 micro-op is executed from any thread on physical core", "PublicDescription": "Cycles at least 3 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "3", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_GE_4", "BriefDescription": "Cycles at least 4 micro-op is executed from any thread on physical core", "PublicDescription": "Cycles at least 4 micro-op is executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "4", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB1", "UMask": "0x02", "EventName": "UOPS_EXECUTED.CORE_CYCLES_NONE", "BriefDescription": "Cycles with no micro-ops executed from any thread on physical core", "PublicDescription": "Cycles with no micro-ops executed from any thread on physical core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0x00", "MSRValue": "0x00", "TakenAlone": "0", "CounterMask": "0", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB2", "UMask": "0x01", "EventName": "OFFCORE_REQUESTS_BUFFER.SQ_FULL", "BriefDescription": "Cases when offcore requests buffer cannot take more entries for core", "PublicDescription": "Cases when offcore requests buffer cannot take more entries for core.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xBD", "UMask": "0x01", "EventName": "TLB_FLUSH.DTLB_THREAD", "BriefDescription": "DTLB flush attempts of the thread-specific entries", "PublicDescription": "DTLB flush attempts of the thread-specific entries.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xBD", "UMask": "0x20", "EventName": "TLB_FLUSH.STLB_ANY", "BriefDescription": "STLB flush attempts", "PublicDescription": "Count number of STLB flush attempts.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xBE", "UMask": "0x01", "EventName": "PAGE_WALKS.LLC_MISS", "BriefDescription": "Number of any page walk that had a miss in LLC.", "PublicDescription": "Number of any page walk that had a miss in LLC.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC0", "UMask": "0x00", "EventName": "INST_RETIRED.ANY_P", "BriefDescription": "Number of instructions retired. General Counter   - architectural event", "PublicDescription": "Number of instructions at retirement.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC0", "UMask": "0x01", "EventName": "INST_RETIRED.PREC_DIST", "BriefDescription": "Precise instruction retired event with HW to reduce effect of PEBS shadow in IP distribution", "PublicDescription": "Precise instruction retired event with HW to reduce effect of PEBS shadow in IP distribution.", "Counter": "1", "CounterHTOff": "1", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC1", "UMask": "0x08", "EventName": "OTHER_ASSISTS.AVX_STORE", "BriefDescription": "Number of GSSE memory assist for stores. GSSE microcode assist is being invoked whenever the hardware is unable to properly handle GSSE-256b operations.", "PublicDescription": "Number of assists associated with 256-bit AVX store operations.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC1", "UMask": "0x10", "EventName": "OTHER_ASSISTS.AVX_TO_SSE", "BriefDescription": "Number of transitions from AVX-256 to legacy SSE when penalty applicable.", "PublicDescription": "Number of transitions from AVX-256 to legacy SSE when penalty applicable.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC1", "UMask": "0x20", "EventName": "OTHER_ASSISTS.SSE_TO_AVX", "BriefDescription": "Number of transitions from SSE to AVX-256 when penalty applicable.", "PublicDescription": "Number of transitions from SSE to AVX-256 when penalty applicable.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC1", "UMask": "0x80", "EventName": "OTHER_ASSISTS.ANY_WB_ASSIST", "BriefDescription": "Number of times any microcode assist is invoked by HW upon uop writeback.", "PublicDescription": "Number of times any microcode assist is invoked by HW upon uop writeback.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x01", "EventName": "UOPS_RETIRED.ALL", "BriefDescription": "Retired uops.", "PublicDescription": "Retired uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x01", "EventName": "UOPS_RETIRED.STALL_CYCLES", "BriefDescription": "Cycles without actually retired uops.", "PublicDescription": "Cycles without actually retired uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x01", "EventName": "UOPS_RETIRED.TOTAL_CYCLES", "BriefDescription": "Cycles with less than 10 actually retired uops.", "PublicDescription": "Cycles with less than 10 actually retired uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "10", "Invert": "1", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x01", "EventName": "UOPS_RETIRED.CORE_STALL_CYCLES", "BriefDescription": "Cycles without actually retired uops.", "PublicDescription": "Cycles without actually retired uops.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "1", "AnyThread": "1", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC2", "UMask": "0x02", "EventName": "UOPS_RETIRED.RETIRE_SLOTS", "BriefDescription": "Retirement slots used.", "PublicDescription": "Retirement slots used.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC3", "UMask": "0x01", "EventName": "MACHINE_CLEARS.COUNT", "BriefDescription": "Number of machine clears (nukes) of any type.", "PublicDescription": "Number of machine clears (nukes) of any type.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "1", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC3", "UMask": "0x02", "EventName": "MACHINE_CLEARS.MEMORY_ORDERING", "BriefDescription": "Counts the number of machine clears due to memory order conflicts.", "PublicDescription": "Counts the number of machine clears due to memory order conflicts.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC3", "UMask": "0x04", "EventName": "MACHINE_CLEARS.SMC", "BriefDescription": "Self-modifying code (SMC) detected.", "PublicDescription": "Number of self-modifying-code machine clears detected.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC3", "UMask": "0x20", "EventName": "MACHINE_CLEARS.MASKMOV", "BriefDescription": "This event counts the number of executed Intel AVX masked load operations that refer to an illegal address range with the mask bits set to 0.", "PublicDescription": "Counts the number of executed AVX masked load operations that refer to an illegal address range with the mask bits set to 0.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x00", "EventName": "BR_INST_RETIRED.ALL_BRANCHES", "BriefDescription": "All (macro) branch instructions retired.", "PublicDescription": "Branch instructions at retirement.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x01", "EventName": "BR_INST_RETIRED.CONDITIONAL", "BriefDescription": "Conditional branch instructions retired.", "PublicDescription": "Conditional branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x02", "EventName": "BR_INST_RETIRED.NEAR_CALL", "BriefDescription": "Direct and indirect near call instructions retired.", "PublicDescription": "Direct and indirect near call instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x02", "EventName": "BR_INST_RETIRED.NEAR_CALL_R3", "BriefDescription": "Direct and indirect macro near call instructions retired (captured in ring 3).", "PublicDescription": "Direct and indirect macro near call instructions retired (captured in ring 3).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x04", "EventName": "BR_INST_RETIRED.ALL_BRANCHES_PEBS", "BriefDescription": "All (macro) branch instructions retired.", "PublicDescription": "All (macro) branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x08", "EventName": "BR_INST_RETIRED.NEAR_RETURN", "BriefDescription": "Return instructions retired.", "PublicDescription": "Return instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x10", "EventName": "BR_INST_RETIRED.NOT_TAKEN", "BriefDescription": "Not taken branch instructions retired.", "PublicDescription": "Counts the number of not taken branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x20", "EventName": "BR_INST_RETIRED.NEAR_TAKEN", "BriefDescription": "Taken branch instructions retired.", "PublicDescription": "Taken branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC4", "UMask": "0x40", "EventName": "BR_INST_RETIRED.FAR_BRANCH", "BriefDescription": "Far branch instructions retired.", "PublicDescription": "Number of far branches retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC5", "UMask": "0x00", "EventName": "BR_MISP_RETIRED.ALL_BRANCHES", "BriefDescription": "All mispredicted macro branch instructions retired.", "PublicDescription": "Mispredicted branch instructions at retirement.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC5", "UMask": "0x01", "EventName": "BR_MISP_RETIRED.CONDITIONAL", "BriefDescription": "Mispredicted conditional branch instructions retired.", "PublicDescription": "Mispredicted conditional branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC5", "UMask": "0x04", "EventName": "BR_MISP_RETIRED.ALL_BRANCHES_PEBS", "BriefDescription": "Mispredicted macro branch instructions retired.", "PublicDescription": "Mispredicted macro branch instructions retired.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xC5", "UMask": "0x20", "EventName": "BR_MISP_RETIRED.NEAR_TAKEN", "BriefDescription": "number of near branch instructions retired that were mispredicted and taken.", "PublicDescription": "number of near branch instructions retired that were mispredicted and taken.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "400009", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x02", "EventName": "FP_ASSIST.X87_OUTPUT", "BriefDescription": "Number of X87 assists due to output value.", "PublicDescription": "Number of X87 FP assists due to output values.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x04", "EventName": "FP_ASSIST.X87_INPUT", "BriefDescription": "Number of X87 assists due to input value.", "PublicDescription": "Number of X87 FP assists due to input values.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x08", "EventName": "FP_ASSIST.SIMD_OUTPUT", "BriefDescription": "Number of SIMD FP assists due to Output values", "PublicDescription": "Number of SIMD FP assists due to output values.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x10", "EventName": "FP_ASSIST.SIMD_INPUT", "BriefDescription": "Number of SIMD FP assists due to input values", "PublicDescription": "Number of SIMD FP assists due to input values.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCA", "UMask": "0x1E", "EventName": "FP_ASSIST.ANY", "BriefDescription": "Cycles with any input/output SSE or FP assist", "PublicDescription": "Cycles with any input/output SSE* or FP assists.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "1", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCC", "UMask": "0x20", "EventName": "ROB_MISC_EVENTS.LBR_INSERTS", "BriefDescription": "Count cases of saving new LBR", "PublicDescription": "Count cases of saving new LBR records by hardware.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_4", "BriefDescription": "Loads with latency value being above 4", "PublicDescription": "Loads with latency value being above 4.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "100003", "MSRIndex": "0x3F6", "MSRValue": "0x4", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_8", "BriefDescription": "Loads with latency value being above 8", "PublicDescription": "Loads with latency value being above 8.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "50021", "MSRIndex": "0x3F6", "MSRValue": "0x8", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_16", "BriefDescription": "Loads with latency value being above 16", "PublicDescription": "Loads with latency value being above 16.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "20011", "MSRIndex": "0x3F6", "MSRValue": "0x10", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_32", "BriefDescription": "Loads with latency value being above 32", "PublicDescription": "Loads with latency value being above 32.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "100007", "MSRIndex": "0x3F6", "MSRValue": "0x20", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_64", "BriefDescription": "Loads with latency value being above 64", "PublicDescription": "Loads with latency value being above 64.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "2003", "MSRIndex": "0x3F6", "MSRValue": "0x40", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_128", "BriefDescription": "Loads with latency value being above 128", "PublicDescription": "Loads with latency value being above 128.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "1009", "MSRIndex": "0x3F6", "MSRValue": "0x80", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_256", "BriefDescription": "Loads with latency value being above 256", "PublicDescription": "Loads with latency value being above 256.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "503", "MSRIndex": "0x3F6", "MSRValue": "0x100", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x01", "EventName": "MEM_TRANS_RETIRED.LOAD_LATENCY_GT_512", "BriefDescription": "Loads with latency value being above 512", "PublicDescription": "Loads with latency value being above 512.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "101", "MSRIndex": "0x3F6", "MSRValue": "0x200", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xCD", "UMask": "0x02", "EventName": "MEM_TRANS_RETIRED.PRECISE_STORE", "BriefDescription": "Sample stores and collect precise store operation via PEBS record. PMC3 only.", "PublicDescription": "Sample stores and collect precise store operation via PEBS record. PMC3 only.", "Counter": "3", "CounterHTOff": "3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "1", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "2", "PRECISE_STORE": "1", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x11", "EventName": "MEM_UOPS_RETIRED.STLB_MISS_LOADS", "BriefDescription": "Retired load uops that miss the STLB. (Precise Event)", "PublicDescription": "Retired load uops that miss the STLB. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x12", "EventName": "MEM_UOPS_RETIRED.STLB_MISS_STORES", "BriefDescription": "Retired store uops that miss the STLB. (Precise Event)", "PublicDescription": "Retired store uops that miss the STLB. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x21", "EventName": "MEM_UOPS_RETIRED.LOCK_LOADS", "BriefDescription": "Retired load uops with locked access. (Precise Event)", "PublicDescription": "Retired load uops with locked access. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x41", "EventName": "MEM_UOPS_RETIRED.SPLIT_LOADS", "BriefDescription": "Retired load uops that split across a cacheline boundary. (Precise Event)", "PublicDescription": "Retired load uops that split across a cacheline boundary. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x42", "EventName": "MEM_UOPS_RETIRED.SPLIT_STORES", "BriefDescription": "Retired store uops that split across a cacheline boundary. (Precise Event)", "PublicDescription": "Retired store uops that split across a cacheline boundary. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x81", "EventName": "MEM_UOPS_RETIRED.ALL_LOADS", "BriefDescription": "All retired load uops. (Precise Event)", "PublicDescription": "All retired load uops. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD0", "UMask": "0x82", "EventName": "MEM_UOPS_RETIRED.ALL_STORES", "BriefDescription": "All retired store uops. (Precise Event)", "PublicDescription": "All retired store uops. (Precise Event)", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x01", "EventName": "MEM_LOAD_UOPS_RETIRED.L1_HIT", "BriefDescription": "Retired load uops with L1 cache hits as data sources.", "PublicDescription": "Retired load uops with L1 cache hits as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "2000003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x02", "EventName": "MEM_LOAD_UOPS_RETIRED.L2_HIT", "BriefDescription": "Retired load uops with L2 cache hits as data sources.", "PublicDescription": "Retired load uops with L2 cache hits as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x04", "EventName": "MEM_LOAD_UOPS_RETIRED.LLC_HIT", "BriefDescription": "Retired load uops which data sources were data hits in LLC without snoops required.", "PublicDescription": "Retired load uops which data sources were data hits in LLC without snoops required.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "50021", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x08", "EventName": "MEM_LOAD_UOPS_RETIRED.L1_MISS", "BriefDescription": "Retired load uops which data sources following L1 data-cache miss.", "PublicDescription": "Retired load uops which data sources following L1 data-cache miss.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x10", "EventName": "MEM_LOAD_UOPS_RETIRED.L2_MISS", "BriefDescription": "Retired load uops with L2 cache misses as data sources.", "PublicDescription": "Retired load uops with L2 cache misses as data sources.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "50021", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x20", "EventName": "MEM_LOAD_UOPS_RETIRED.LLC_MISS", "BriefDescription": "Miss in last-level (L3) cache. Excludes Unknown data-source.", "PublicDescription": "Miss in last-level (L3) cache. Excludes Unknown data-source.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD1", "UMask": "0x40", "EventName": "MEM_LOAD_UOPS_RETIRED.HIT_LFB", "BriefDescription": "Retired load uops which data sources were load uops missed L1 but hit FB due to preceding miss to the same cache line with data not ready.", "PublicDescription": "Retired load uops which data sources were load uops missed L1 but hit FB due to preceding miss to the same cache line with data not ready.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD2", "UMask": "0x01", "EventName": "MEM_LOAD_UOPS_LLC_HIT_RETIRED.XSNP_MISS", "BriefDescription": "Retired load uops which data sources were LLC hit and cross-core snoop missed in on-pkg core cache.", "PublicDescription": "Retired load uops which data sources were LLC hit and cross-core snoop missed in on-pkg core cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD2", "UMask": "0x02", "EventName": "MEM_LOAD_UOPS_LLC_HIT_RETIRED.XSNP_HIT", "BriefDescription": "Retired load uops which data sources were LLC and cross-core snoop hits in on-pkg core cache.", "PublicDescription": "Retired load uops which data sources were LLC and cross-core snoop hits in on-pkg core cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD2", "UMask": "0x04", "EventName": "MEM_LOAD_UOPS_LLC_HIT_RETIRED.XSNP_HITM", "BriefDescription": "Retired load uops which data sources were HitM responses from shared LLC.", "PublicDescription": "Retired load uops which data sources were HitM responses from shared LLC.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "20011", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD2", "UMask": "0x08", "EventName": "MEM_LOAD_UOPS_LLC_HIT_RETIRED.XSNP_NONE", "BriefDescription": "Retired load uops which data sources were hits in LLC without snoops required.", "PublicDescription": "Retired load uops which data sources were hits in LLC without snoops required.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "1", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xD3", "UMask": "0x01", "EventName": "MEM_LOAD_UOPS_LLC_MISS_RETIRED.LOCAL_DRAM", "BriefDescription": "Retired load uops which data sources missed LLC but serviced from local dram.", "PublicDescription": "Retired load uops whose data source was local memory (cross-socket snoop not needed or missed).", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100007", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xE6", "UMask": "0x1F", "EventName": "BACLEARS.ANY", "BriefDescription": "Counts the total number when the front end is resteered, mainly when the BPU cannot provide a correct prediction and this is corrected by other branch handling mechanisms at the front end.", "PublicDescription": "Number of front end re-steers due to BPU misprediction.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x01", "EventName": "L2_TRANS.DEMAND_DATA_RD", "BriefDescription": "Demand Data Read requests that access L2 cache", "PublicDescription": "Demand Data Read requests that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x02", "EventName": "L2_TRANS.RFO", "BriefDescription": "RFO requests that access L2 cache", "PublicDescription": "RFO requests that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x04", "EventName": "L2_TRANS.CODE_RD", "BriefDescription": "L2 cache accesses when fetching instructions", "PublicDescription": "L2 cache accesses when fetching instructions.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x08", "EventName": "L2_TRANS.ALL_PF", "BriefDescription": "L2 or LLC HW prefetches that access L2 cache", "PublicDescription": "Any MLC or LLC HW prefetch accessing L2, including rejects.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x10", "EventName": "L2_TRANS.L1D_WB", "BriefDescription": "L1D writebacks that access L2 cache", "PublicDescription": "L1D writebacks that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x20", "EventName": "L2_TRANS.L2_FILL", "BriefDescription": "L2 fill requests that access L2 cache", "PublicDescription": "L2 fill requests that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x40", "EventName": "L2_TRANS.L2_WB", "BriefDescription": "L2 writebacks that access L2 cache", "PublicDescription": "L2 writebacks that access L2 cache.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF0", "UMask": "0x80", "EventName": "L2_TRANS.ALL_REQUESTS", "BriefDescription": "Transactions accessing L2 pipe", "PublicDescription": "Transactions accessing L2 pipe.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "200003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF1", "UMask": "0x01", "EventName": "L2_LINES_IN.I", "BriefDescription": "L2 cache lines in I state filling L2", "PublicDescription": "L2 cache lines in I state filling L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF1", "UMask": "0x02", "EventName": "L2_LINES_IN.S", "BriefDescription": "L2 cache lines in S state filling L2", "PublicDescription": "L2 cache lines in S state filling L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF1", "UMask": "0x04", "EventName": "L2_LINES_IN.E", "BriefDescription": "L2 cache lines in E state filling L2", "PublicDescription": "L2 cache lines in E state filling L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF1", "UMask": "0x07", "EventName": "L2_LINES_IN.ALL", "BriefDescription": "L2 cache lines filling L2", "PublicDescription": "L2 cache lines filling L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF2", "UMask": "0x01", "EventName": "L2_LINES_OUT.DEMAND_CLEAN", "BriefDescription": "Clean L2 cache lines evicted by demand", "PublicDescription": "Clean L2 cache lines evicted by demand.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF2", "UMask": "0x02", "EventName": "L2_LINES_OUT.DEMAND_DIRTY", "BriefDescription": "Dirty L2 cache lines evicted by demand", "PublicDescription": "Dirty L2 cache lines evicted by demand.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF2", "UMask": "0x04", "EventName": "L2_LINES_OUT.PF_CLEAN", "BriefDescription": "Clean L2 cache lines evicted by L2 prefetch", "PublicDescription": "Clean L2 cache lines evicted by the MLC prefetcher.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF2", "UMask": "0x08", "EventName": "L2_LINES_OUT.PF_DIRTY", "BriefDescription": "Dirty L2 cache lines evicted by L2 prefetch", "PublicDescription": "Dirty L2 cache lines evicted by the MLC prefetcher.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF2", "UMask": "0x0A", "EventName": "L2_LINES_OUT.DIRTY_ALL", "BriefDescription": "Dirty L2 cache lines filling the L2", "PublicDescription": "Dirty L2 cache lines filling the L2.", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xF4", "UMask": "0x10", "EventName": "SQ_MISC.SPLIT_LOCK", "BriefDescription": "Split locks in SQ", "PublicDescription": "tbd", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3,4,5,6,7", "SampleAfterValue": "100003", "MSRIndex": "0", "MSRValue": "0", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "0", "Offcore": "0"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_CODE_RD.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand & prefetch code reads that hit in the LLC", "PublicDescription": "Counts all demand & prefetch code reads that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0244", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_CODE_RD.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand & prefetch code reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand & prefetch code reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0244", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_CODE_RD.LLC_MISS.DRAM", "BriefDescription": "Counts all demand & prefetch code reads that miss the LLC  and the data returned from dram", "PublicDescription": "Counts all demand & prefetch code reads that miss the LLC  and the data returned from dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x300400244", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand & prefetch data reads that hit in the LLC", "PublicDescription": "Counts all demand & prefetch data reads that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0091", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.LLC_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts demand & prefetch data reads that hit in the LLC and the snoops to sibling cores hit in either E/S state and the line is not forwarded", "PublicDescription": "Counts demand & prefetch data reads that hit in the LLC and the snoops to sibling cores hit in either E/S state and the line is not forwarded", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x4003c0091", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.LLC_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts demand & prefetch data reads that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "PublicDescription": "Counts demand & prefetch data reads that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10003c0091", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand & prefetch data reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand & prefetch data reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0091", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.LLC_MISS.DRAM", "BriefDescription": "Counts all demand & prefetch data reads that miss the LLC  and the data returned from dram", "PublicDescription": "Counts all demand & prefetch data reads that miss the LLC  and the data returned from dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x300400091", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_READS.LLC_MISS.DRAM", "BriefDescription": "Counts all data/code/rfo reads (demand & prefetch) that miss the LLC  and the data returned from dram", "PublicDescription": "Counts all data/code/rfo reads (demand & prefetch) that miss the LLC  and the data returned from dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3004003f7", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand & prefetch RFOs that hit in the LLC", "PublicDescription": "Counts all demand & prefetch RFOs that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand & prefetch RFOs that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand & prefetch RFOs that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.COREWB.ANY_RESPONSE", "BriefDescription": "Counts all writebacks from the core to the LLC", "PublicDescription": "Counts all writebacks from the core to the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10008", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand code reads that hit in the LLC", "PublicDescription": "Counts all demand code reads that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand code reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand code reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.LLC_MISS.DRAM", "BriefDescription": "Counts demand code reads that miss the LLC and the data returned from dram", "PublicDescription": "Counts demand code reads that miss the LLC and the data returned from dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x300400004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand data reads that hit in the LLC", "PublicDescription": "Counts all demand data reads that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.LLC_HIT.HIT_OTHER_CORE_NO_FWD", "BriefDescription": "Counts demand data reads that hit in the LLC and the snoops to sibling cores hit in either E/S state and the line is not forwarded", "PublicDescription": "Counts demand data reads that hit in the LLC and the snoops to sibling cores hit in either E/S state and the line is not forwarded", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x4003c0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.LLC_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts demand data reads that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "PublicDescription": "Counts demand data reads that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10003c0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand data reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand data reads that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.LLC_MISS.DRAM", "BriefDescription": "Counts demand data reads that miss the LLC and the data returned from dram", "PublicDescription": "Counts demand data reads that miss the LLC and the data returned from dram", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x300400001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.LLC_HIT.ANY_RESPONSE", "BriefDescription": "Counts all demand data writes (RFOs) that hit in the LLC", "PublicDescription": "Counts all demand data writes (RFOs) that hit in the LLC", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x3f803c0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.LLC_HIT.HITM_OTHER_CORE", "BriefDescription": "Counts demand data writes (RFOs) that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "PublicDescription": "Counts demand data writes (RFOs) that hit in the LLC and the snoop to one of the sibling cores hits the line in M state and the line is forwarded", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10003c0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.LLC_HIT.NO_SNOOP_NEEDED", "BriefDescription": "Counts demand data writes (RFOs) that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "PublicDescription": "Counts demand data writes (RFOs) that hit in the LLC and sibling core snoops are not needed as either the core-valid bit is not set or the shared line is present in multiple cores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x1003c0002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.OTHER.ANY_RESPONSE", "BriefDescription": "Counts miscellaneous accesses that include port i/o, MMIO and uncacheable memory accesses. It also includes L2 hints sent to LLC to keep a line from being evicted out of the core caches", "PublicDescription": "Counts miscellaneous accesses that include port i/o, MMIO and uncacheable memory accesses. It also includes L2 hints sent to LLC to keep a line from being evicted out of the core caches", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x18000", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.SPLIT_LOCK_UC_LOCK.ANY_RESPONSE", "BriefDescription": "Counts requests where the address of an atomic lock instruction spans a cache line boundary or the lock instruction is executed on uncacheable address ", "PublicDescription": "Counts requests where the address of an atomic lock instruction spans a cache line boundary or the lock instruction is executed on uncacheable address ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10400", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.STREAMING_STORES.ANY_RESPONSE", "BriefDescription": "Counts non-temporal stores", "PublicDescription": "Counts non-temporal stores", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x10800", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_DATA_RD.ANY_RESPONSE", "BriefDescription": "Counts all demand data reads ", "PublicDescription": "Counts all demand data reads ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x00010001", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_RFO.ANY_RESPONSE", "BriefDescription": "Counts all demand rfo's ", "PublicDescription": "Counts all demand rfo's ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x00010002", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DEMAND_CODE_RD.ANY_RESPONSE", "BriefDescription": "Counts all demand code reads", "PublicDescription": "Counts all demand code reads", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x00010004", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_DATA_RD.ANY_RESPONSE", "BriefDescription": "Counts all demand & prefetch data reads", "PublicDescription": "Counts all demand & prefetch data reads", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x000105B3", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_RFO.ANY_RESPONSE", "BriefDescription": "Counts all demand & prefetch prefetch RFOs ", "PublicDescription": "Counts all demand & prefetch prefetch RFOs ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x00010122", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.ALL_READS.ANY_RESPONSE", "BriefDescription": "Counts all data/code/rfo references (demand & prefetch) ", "PublicDescription": "Counts all data/code/rfo references (demand & prefetch) ", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x000107F7", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}, {"EventCode": "0xB7, 0xBB", "UMask": "0x01", "EventName": "OFFCORE_RESPONSE.DATA_IN_SOCKET.LLC_MISS.LOCAL_DRAM", "BriefDescription": "Counts LLC replacements", "PublicDescription": "Counts LLC replacements", "Counter": "0,1,2,3", "CounterHTOff": "0,1,2,3", "SampleAfterValue": "100003", "MSRIndex": "0x1a6,0x1a7", "MSRValue": "0x6004001b3", "TakenAlone": "0", "CounterMask": "0", "Invert": "0", "AnyThread": "0", "EdgeDetect": "0", "PEBS": "0", "PRECISE_STORE": "0", "Errata": "null", "Offcore": "1"}]