# Performance Monitoring Events for Intel(R) Xeon(R) processor E5 family and Intel(R) Xeon(R) processor E7 family Based on the Ivy Bridge-EP Microarchitecture - V20
# 1/17/2018 5:48:43 PM
# Copyright (c) 2007 - 2017 Intel Corporation. All rights reserved.
Unit	EventCode	UMask	EventName	Description	Counter	MSRValue	Filter	Internal
CBO	0x0	0x0	UNC_C_CLOCKTICKS	0	0,1,2,3	0x0	null	0
CBO	0x1f	0x0	UNC_C_COUNTER0_OCCUPANCY	Since occupancy counts can only be captured in the Cbo's 0 counter, this event allows a user to capture occupancy related information by filtering the Cb0 occupancy count captured in Counter 0.   The filtering available is found in the control register - threshold, invert and edge detect.   E.g. setting threshold to 1 can effectively monitor how many cycles the monitored queue has an entry.	1,2,3	0x0	null	0
CBO	0x34	0x11	UNC_C_LLC_LOOKUP.ANY	Counts the number of times the LLC was accessed - this includes code, data, prefetches and hints coming from L2.  This has numerous filters available.  Note the non-standard filtering equation.  This event will count requests that lookup the cache multiple times with multiple increments.  One must ALWAYS set filter mask bit 0 and select a state or states to match.  Otherwise, the event will count nothing.   CBoGlCtrl[22:17] bits correspond to [M'FMESI] state.; Filters for any transaction originating from the IPQ or IRQ.  This does not include lookups originating from the ISMQ.	0,1	0x0	CBoFilter0[23:17]	0
CBO	0x34	0x3	UNC_C_LLC_LOOKUP.DATA_READ	Counts the number of times the LLC was accessed - this includes code, data, prefetches and hints coming from L2.  This has numerous filters available.  Note the non-standard filtering equation.  This event will count requests that lookup the cache multiple times with multiple increments.  One must ALWAYS set filter mask bit 0 and select a state or states to match.  Otherwise, the event will count nothing.   CBoGlCtrl[22:17] bits correspond to [M'FMESI] state.; Read transactions	0,1	0x0	CBoFilter0[23:17]	0
CBO	0x34	0x41	UNC_C_LLC_LOOKUP.NID	Counts the number of times the LLC was accessed - this includes code, data, prefetches and hints coming from L2.  This has numerous filters available.  Note the non-standard filtering equation.  This event will count requests that lookup the cache multiple times with multiple increments.  One must ALWAYS set filter mask bit 0 and select a state or states to match.  Otherwise, the event will count nothing.   CBoGlCtrl[22:17] bits correspond to [M'FMESI] state.; Qualify one of the other subevents by the Target NID.  The NID is programmed in Cn_MSR_PMON_BOX_FILTER.nid.   In conjunction with STATE = I, it is possible to monitor misses to specific NIDs in the system.	0,1	0x0	CBoFilter0[23:17]	0
CBO	0x34	0x9	UNC_C_LLC_LOOKUP.REMOTE_SNOOP	Counts the number of times the LLC was accessed - this includes code, data, prefetches and hints coming from L2.  This has numerous filters available.  Note the non-standard filtering equation.  This event will count requests that lookup the cache multiple times with multiple increments.  One must ALWAYS set filter mask bit 0 and select a state or states to match.  Otherwise, the event will count nothing.   CBoGlCtrl[22:17] bits correspond to [M'FMESI] state.; Filters for only snoop requests coming from the remote socket(s) through the IPQ.	0,1	0x0	CBoFilter0[23:17]	0
CBO	0x34	0x5	UNC_C_LLC_LOOKUP.WRITE	Counts the number of times the LLC was accessed - this includes code, data, prefetches and hints coming from L2.  This has numerous filters available.  Note the non-standard filtering equation.  This event will count requests that lookup the cache multiple times with multiple increments.  One must ALWAYS set filter mask bit 0 and select a state or states to match.  Otherwise, the event will count nothing.   CBoGlCtrl[22:17] bits correspond to [M'FMESI] state.; Writeback transactions from L2 to the LLC  This includes all write transactions -- both Cachable and UC.	0,1	0x0	CBoFilter0[23:17]	0
CBO	0x37	0x2	UNC_C_LLC_VICTIMS.E_STATE	Counts the number of lines that were victimized on a fill.  This can be filtered by the state that the line was in.	0,1	0x0	null	0
CBO	0x37	0x8	UNC_C_LLC_VICTIMS.MISS	Counts the number of lines that were victimized on a fill.  This can be filtered by the state that the line was in.	0,1	0x0	null	0
CBO	0x37	0x1	UNC_C_LLC_VICTIMS.M_STATE	Counts the number of lines that were victimized on a fill.  This can be filtered by the state that the line was in.	0,1	0x0	null	0
CBO	0x37	0x40	UNC_C_LLC_VICTIMS.NID	Counts the number of lines that were victimized on a fill.  This can be filtered by the state that the line was in.; Qualify one of the other subevents by the Target NID.  The NID is programmed in Cn_MSR_PMON_BOX_FILTER.nid.   In conjunction with STATE = I, it is possible to monitor misses to specific NIDs in the system.	0,1	0x0	CBoFilter1[15:0]	0
CBO	0x37	0x4	UNC_C_LLC_VICTIMS.S_STATE	Counts the number of lines that were victimized on a fill.  This can be filtered by the state that the line was in.	0,1	0x0	null	0
CBO	0x39	0x8	UNC_C_MISC.RFO_HIT_S	Miscellaneous events in the Cbo.; Number of times that an RFO hit in S state.  This is useful for determining if it might be good for a workload to use RspIWB instead of RspSWB.	0,1	0x0	null	0
CBO	0x39	0x1	UNC_C_MISC.RSPI_WAS_FSE	Miscellaneous events in the Cbo.; Counts the number of times when a Snoop hit in FSE states and triggered a silent eviction.  This is useful because this information is lost in the PRE encodings.	0,1	0x0	null	0
CBO	0x39	0x4	UNC_C_MISC.STARTED	Miscellaneous events in the Cbo.	0,1	0x0	null	0
CBO	0x39	0x2	UNC_C_MISC.WC_ALIASING	Miscellaneous events in the Cbo.; Counts the number of times that a USWC write (WCIL(F)) transaction hit in the LLC in M state, triggering a WBMtoI followed by the USWC write.  This occurs when there is WC aliasing.	0,1	0x0	null	0
CBO	0x3c	0x1	UNC_C_QLRU.AGE0	How often age was set to 0	0,1	0x0	null	0
CBO	0x3c	0x2	UNC_C_QLRU.AGE1	How often age was set to 1	0,1	0x0	null	0
CBO	0x3c	0x4	UNC_C_QLRU.AGE2	How often age was set to 2	0,1	0x0	null	0
CBO	0x3c	0x8	UNC_C_QLRU.AGE3	How often age was set to 3	0,1	0x0	null	0
CBO	0x3c	0x10	UNC_C_QLRU.LRU_DECREMENT	How often all LRU bits were decremented by 1	0,1	0x0	null	0
CBO	0x3c	0x20	UNC_C_QLRU.VICTIM_NON_ZERO	How often we picked a victim that had a non-zero age	0,1	0x0	null	0
CBO	0x1b	0x4	UNC_C_RING_AD_USED.DOWN_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1b	0x8	UNC_C_RING_AD_USED.DOWN_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1b	0x40	UNC_C_RING_AD_USED.DOWN_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1b	0x80	UNC_C_RING_AD_USED.DOWN_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1b	0x1	UNC_C_RING_AD_USED.UP_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1b	0x2	UNC_C_RING_AD_USED.UP_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1b	0x10	UNC_C_RING_AD_USED.UP_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1b	0x20	UNC_C_RING_AD_USED.UP_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1c	0x4	UNC_C_RING_AK_USED.DOWN_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1c	0x8	UNC_C_RING_AK_USED.DOWN_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1c	0x40	UNC_C_RING_AK_USED.DOWN_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1c	0x80	UNC_C_RING_AK_USED.DOWN_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1c	0x1	UNC_C_RING_AK_USED.UP_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1c	0x2	UNC_C_RING_AK_USED.UP_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1c	0x10	UNC_C_RING_AK_USED.UP_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1c	0x20	UNC_C_RING_AK_USED.UP_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1d	0x4	UNC_C_RING_BL_USED.DOWN_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1d	0x8	UNC_C_RING_BL_USED.DOWN_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1d	0x40	UNC_C_RING_BL_USED.DOWN_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1d	0x80	UNC_C_RING_BL_USED.DOWN_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Down and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1d	0x1	UNC_C_RING_BL_USED.UP_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1d	0x2	UNC_C_RING_BL_USED.UP_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 0.	2,3	0x0	null	0
CBO	0x1d	0x10	UNC_C_RING_BL_USED.UP_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Even ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x1d	0x20	UNC_C_RING_BL_USED.UP_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.; Filters for the Up and Odd ring polarity on Virtual Ring 1.	2,3	0x0	null	0
CBO	0x5	0x2	UNC_C_RING_BOUNCES.AK_CORE	0	0,1	0x0	null	0
CBO	0x5	0x4	UNC_C_RING_BOUNCES.BL_CORE	0	0,1	0x0	null	0
CBO	0x5	0x8	UNC_C_RING_BOUNCES.IV_CORE	0	0,1	0x0	null	0
CBO	0x1e	0xF	UNC_C_RING_IV_USED.ANY	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters any polarity	2,3	0x0	null	0
CBO	0x1e	0xCC	UNC_C_RING_IV_USED.DOWN	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for Down polarity	2,3	0x0	null	0
CBO	0x1e	0x33	UNC_C_RING_IV_USED.UP	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for Up polarity	2,3	0x0	null	0
CBO	0x7	0x0	UNC_C_RING_SRC_THRTL	0	0,1	0x0	null	0
CBO	0x12	0x2	UNC_C_RxR_EXT_STARVED.IPQ	Counts cycles in external starvation.  This occurs when one of the ingress queues is being starved by the other queues.; IPQ is externally startved and therefore we are blocking the IRQ.	0,1	0x0	null	0
CBO	0x12	0x1	UNC_C_RxR_EXT_STARVED.IRQ	Counts cycles in external starvation.  This occurs when one of the ingress queues is being starved by the other queues.; IRQ is externally starved and therefore we are blocking the IPQ.	0,1	0x0	null	0
CBO	0x12	0x4	UNC_C_RxR_EXT_STARVED.PRQ	Counts cycles in external starvation.  This occurs when one of the ingress queues is being starved by the other queues. IRQ is blocking the ingress queue and causing the starvation.	0,1	0x0	null	0
CBO	0x12	0x8	UNC_C_RxR_EXT_STARVED.ISMQ_BIDS	Counts cycles in external starvation.  This occurs when one of the ingress queues is being starved by the other queues.; Number of times that the ISMQ Bid.	0,1	0x0	null	0
CBO	0x13	0x4	UNC_C_RxR_INSERTS.IPQ	Counts number of allocations per cycle into the specified Ingress queue.	0,1	0x0	null	0
CBO	0x13	0x1	UNC_C_RxR_INSERTS.IRQ	Counts number of allocations per cycle into the specified Ingress queue.	0,1	0x0	null	0
CBO	0x13	0x2	UNC_C_RxR_INSERTS.IRQ_REJECTED	Counts number of allocations per cycle into the specified Ingress queue.	0,1	0x0	null	0
CBO	0x13	0x10	UNC_C_RxR_INSERTS.VFIFO	Counts number of allocations per cycle into the specified Ingress queue.; Counts the number of allocations into the IRQ Ordering FIFO.  In JKT, it is necessary to keep IO requests in order.  Therefore, they are allocated into an ordering FIFO that sits next to the IRQ, and must be satisfied from the FIFO in order (with respect to each other).  This event, in conjunction with the Occupancy Accumulator event, can be used to calculate average lifetime in the FIFO.  Transactions are allocated into the FIFO as soon as they enter the Cachebo (and the IRQ) and are deallocated from the FIFO as soon as they are deallocated from the IRQ.	0,1	0x0	null	0
CBO	0x14	0x4	UNC_C_RxR_INT_STARVED.IPQ	Counts cycles in internal starvation.  This occurs when one (or more) of the entries in the ingress queue are being starved out by other entries in that queue.; Cycles with the IPQ in Internal Starvation.	0,1	0x0	null	0
CBO	0x14	0x1	UNC_C_RxR_INT_STARVED.IRQ	Counts cycles in internal starvation.  This occurs when one (or more) of the entries in the ingress queue are being starved out by other entries in that queue.; Cycles with the IRQ in Internal Starvation.	0,1	0x0	null	0
CBO	0x14	0x8	UNC_C_RxR_INT_STARVED.ISMQ	Counts cycles in internal starvation.  This occurs when one (or more) of the entries in the ingress queue are being starved out by other entries in that queue.; Cycles with the ISMQ in Internal Starvation.	0,1	0x0	null	0
CBO	0x31	0x4	UNC_C_RxR_IPQ_RETRY.ADDR_CONFLICT	Number of times a snoop (probe) request had to retry.  Filters exist to cover some of the common cases retries.; Counts the number of times that a request form the IPQ was retried because of a TOR reject from an address conflicts.  Address conflicts out of the IPQ should be rare.  They will generally only occur if two different sockets are sending requests to the same address at the same time.  This is a true conflict case, unlike the IPQ Address Conflict which is commonly caused by prefetching characteristics.	0,1	0x0	null	0
CBO	0x31	0x1	UNC_C_RxR_IPQ_RETRY.ANY	Number of times a snoop (probe) request had to retry.  Filters exist to cover some of the common cases retries.; Counts the number of times that a request form the IPQ was retried because of a TOR reject.  TOR rejects from the IPQ can be caused by the Egress being full or Address Conflicts.	0,1	0x0	null	0
CBO	0x31	0x2	UNC_C_RxR_IPQ_RETRY.FULL	Number of times a snoop (probe) request had to retry.  Filters exist to cover some of the common cases retries.; Counts the number of times that a request form the IPQ was retried because of a TOR reject from the Egress being full.  IPQ requests make use of the AD Egress for regular responses, the BL egress to forward data, and the AK egress to return credits.	0,1	0x0	null	0
CBO	0x31	0x10	UNC_C_RxR_IPQ_RETRY.QPI_CREDITS	Number of times a snoop (probe) request had to retry.  Filters exist to cover some of the common cases retries.	0,1	0x0	null	0
CBO	0x32	0x4	UNC_C_RxR_IRQ_RETRY.ADDR_CONFLICT	Counts the number of times that a request from the IRQ was retried because of an address match in the TOR.  In order to maintain coherency, requests to the same address are not allowed to pass each other up in the Cbo.  Therefore, if there is an outstanding request to a given address, one cannot issue another request to that address until it is complete.  This comes up most commonly with prefetches.  Outstanding prefetches occasionally will not complete their memory fetch and a demand request to the same address will then sit in the IRQ and get retried until the prefetch fills the data into the LLC.  Therefore, it will not be uncommon to see this case in high bandwidth streaming workloads when the LLC Prefetcher in the core is enabled.	0,1	0x0	null	0
CBO	0x32	0x1	UNC_C_RxR_IRQ_RETRY.ANY	Counts the number of IRQ retries that occur.  Requests from the IRQ are retried if they are rejected from the TOR pipeline for a variety of reasons.  Some of the most common reasons include if the Egress is full, there are no RTIDs, or there is a Physical Address match to another outstanding request.	0,1	0x0	null	0
CBO	0x32	0x2	UNC_C_RxR_IRQ_RETRY.FULL	Counts the number of times that a request from the IRQ was retried because it failed to acquire an entry in the Egress.  The egress is the buffer that queues up for allocating onto the ring.  IRQ requests can make use of all four rings and all four Egresses.  If any of the queues that a given request needs to make use of are full, the request will be retried.	0,1	0x0	null	0
CBO	0x32	0x20	UNC_C_RxR_IRQ_RETRY.IIO_CREDITS	Number of times a request attempted to acquire the NCS/NCB credit for sending messages on BL to the IIO.  There is a single credit in each CBo that is shared between the NCS and NCB message classes for sending transactions on the BL ring (such as read data) to the IIO.	0,1	0x0	null	0
CBO	0x32	0x10	UNC_C_RxR_IRQ_RETRY.QPI_CREDITS	Number of requests rejects because of lack of QPI Ingress credits.  These credits are required in order to send transactions to the QPI agent.  Please see the QPI_IGR_CREDITS events for more information.	0,1	0x0	null	0
CBO	0x32	0x8	UNC_C_RxR_IRQ_RETRY.RTID	Counts the number of times that requests from the IRQ were retried because there were no RTIDs available.  RTIDs are required after a request misses the LLC and needs to send snoops and/or requests to memory.  If there are no RTIDs available, requests will queue up in the IRQ and retry until one becomes available.  Note that there are multiple RTID pools for the different sockets.  There may be cases where the local RTIDs are all used, but requests destined for remote memory can still acquire an RTID because there are remote RTIDs available.  This event does not provide any filtering for this case.	0,1	0x0	null	0
CBO	0x33	0x1	UNC_C_RxR_ISMQ_RETRY.ANY	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.; Counts the total number of times that a request from the ISMQ retried because of a TOR reject.  ISMQ requests generally will not need to retry (or at least ISMQ retries are less common than IRQ retries).  ISMQ requests will retry if they are not able to acquire a needed Egress credit to get onto the ring, or for cache evictions that need to acquire an RTID.  Most ISMQ requests already have an RTID, so eviction retries will be less common here.	0,1	0x0	null	0
CBO	0x33	0x2	UNC_C_RxR_ISMQ_RETRY.FULL	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.; Counts the number of times that a request from the ISMQ retried because of a TOR reject caused by a lack of Egress credits. The egress is the buffer that queues up for allocating onto the ring.  If any of the Egress queues that a given request needs to make use of are full, the request will be retried.	0,1	0x0	null	0
CBO	0x33	0x20	UNC_C_RxR_ISMQ_RETRY.IIO_CREDITS	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.; Number of times a request attempted to acquire the NCS/NCB credit for sending messages on BL to the IIO.  There is a single credit in each CBo that is shared between the NCS and NCB message classes for sending transactions on the BL ring (such as read data) to the IIO.	0,1	0x0	null	0
CBO	0x33	0x10	UNC_C_RxR_ISMQ_RETRY.QPI_CREDITS	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.	0,1	0x0	null	0
CBO	0x33	0x8	UNC_C_RxR_ISMQ_RETRY.RTID	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.; Counts the number of times that a request from the ISMQ retried because of a TOR reject caused by no RTIDs.  M-state cache evictions are serviced through the ISMQ, and must acquire an RTID in order to write back to memory.  If no RTIDs are available, they will be retried.	0,1	0x0	null	0
CBO	0x33	0x80	UNC_C_RxR_ISMQ_RETRY.WB_CREDITS	Number of times a transaction flowing through the ISMQ had to retry.  Transaction pass through the ISMQ as responses for requests that already exist in the Cbo.  Some examples include: when data is returned or when snoop responses come back from the cores.; Retries of writes to local memory due to lack of HT WB credits	0,1	0x0	null	0
CBO	0x11	0x4	UNC_C_RxR_OCCUPANCY.IPQ	Counts number of entries in the specified Ingress queue in each cycle.	0	0x0	null	0
CBO	0x11	0x1	UNC_C_RxR_OCCUPANCY.IRQ	Counts number of entries in the specified Ingress queue in each cycle.	0	0x0	null	0
CBO	0x11	0x2	UNC_C_RxR_OCCUPANCY.IRQ_REJECTED	Counts number of entries in the specified Ingress queue in each cycle.	0	0x0	null	0
CBO	0x11	0x10	UNC_C_RxR_OCCUPANCY.VFIFO	Counts number of entries in the specified Ingress queue in each cycle.; Accumulates the number of used entries in the IRQ Ordering FIFO in each cycle.  In JKT, it is necessary to keep IO requests in order.  Therefore, they are allocated into an ordering FIFO that sits next to the IRQ, and must be satisfied from the FIFO in order (with respect to each other).  This event, in conjunction with the Allocations event, can be used to calculate average lifetime in the FIFO.  This event can be used in conjunction with the Not Empty event to calculate average queue occupancy. Transactions are allocated into the FIFO as soon as they enter the Cachebo (and the IRQ) and are deallocated from the FIFO as soon as they are deallocated from the IRQ.	0	0x0	null	0
CBO	0x35	0x8	UNC_C_TOR_INSERTS.ALL	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All transactions inserted into the TOR.    This includes requests that reside in the TOR for a short time, such as LLC Hits that do not need to snoop cores or requests that get rejected and have to be retried through one of the ingress queues.  The TOR is more commonly a bottleneck in skews with smaller core counts, where the ratio of RTIDs to TOR entries is larger.  Note that there are reserved TOR entries for various request types, so it is possible that a given request type be blocked with an occupancy that is less than 20.  Also note that generally requests will not be able to arbitrate into the TOR pipeline if there are no available TOR slots.	0,1	0x0	null	0
CBO	0x35	0x4	UNC_C_TOR_INSERTS.EVICTION	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Eviction transactions inserted into the TOR.  Evictions can be quick, such as when the line is in the F, S, or E states and no core valid bits are set.  They can also be longer if either CV bits are set (so the cores need to be snooped) and/or if there is a HitM (in which case it is necessary to write the request out to memory).	0,1	0x0	null	0
CBO	0x35	0x28	UNC_C_TOR_INSERTS.LOCAL	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All transactions inserted into the TOR that are satisifed by locally HOMed memory.	0,1	0x0	null	0
CBO	0x35	0x21	UNC_C_TOR_INSERTS.LOCAL_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All transactions, satisifed by an opcode,  inserted into the TOR that are satisifed by locally HOMed memory.	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x2A	UNC_C_TOR_INSERTS.MISS_LOCAL	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions inserted into the TOR that are satisifed by locally HOMed memory.	0,1	0x0	null	0
CBO	0x35	0x23	UNC_C_TOR_INSERTS.MISS_LOCAL_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions, satisifed by an opcode, inserted into the TOR that are satisifed by locally HOMed memory.	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x3	UNC_C_TOR_INSERTS.MISS_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions inserted into the TOR that match an opcode.	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x8A	UNC_C_TOR_INSERTS.MISS_REMOTE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions inserted into the TOR that are satisifed by remote caches or remote memory.	0,1	0x0	null	0
CBO	0x35	0x83	UNC_C_TOR_INSERTS.MISS_REMOTE_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions, satisifed by an opcode,  inserted into the TOR that are satisifed by remote caches or remote memory.	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x48	UNC_C_TOR_INSERTS.NID_ALL	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All NID matched (matches an RTID destination) transactions inserted into the TOR.  The NID is programmed in Cn_MSR_PMON_BOX_FILTER.nid.  In conjunction with STATE = I, it is possible to monitor misses to specific NIDs in the system.	0,1	0x0	CBoFilter1[15:0]	0
CBO	0x35	0x44	UNC_C_TOR_INSERTS.NID_EVICTION	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; NID matched eviction transactions inserted into the TOR.	0,1	0x0	CBoFilter1[15:0]	0
CBO	0x35	0x4A	UNC_C_TOR_INSERTS.NID_MISS_ALL	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All NID matched miss requests that were inserted into the TOR.	0,1	0x0	CBoFilter1[15:0]	0
CBO	0x35	0x43	UNC_C_TOR_INSERTS.NID_MISS_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Miss transactions inserted into the TOR that match a NID and an opcode.	0,1	0x0	CBoFilter1[28:20], CBoFilter1[15:0]	0
CBO	0x35	0x41	UNC_C_TOR_INSERTS.NID_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Transactions inserted into the TOR that match a NID and an opcode.	0,1	0x0	CBoFilter1[28:20], CBoFilter1[15:0]	0
CBO	0x35	0x50	UNC_C_TOR_INSERTS.NID_WB	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; NID matched write transactions inserted into the TOR.	0,1	0x0	CBoFilter1[15:0]	0
CBO	0x35	0x1	UNC_C_TOR_INSERTS.OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Transactions inserted into the TOR that match an opcode (matched by Cn_MSR_PMON_BOX_FILTER.opc)	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x88	UNC_C_TOR_INSERTS.REMOTE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All transactions inserted into the TOR that are satisifed by remote caches or remote memory.	0,1	0x0	null	0
CBO	0x35	0x81	UNC_C_TOR_INSERTS.REMOTE_OPCODE	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; All transactions, satisifed by an opcode,  inserted into the TOR that are satisifed by remote caches or remote memory.	0,1	0x0	CBoFilter1[28:20]	0
CBO	0x35	0x10	UNC_C_TOR_INSERTS.WB	Counts the number of entries successfuly inserted into the TOR that match  qualifications specified by the subevent.  There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc  to DRD (0x182).; Write transactions inserted into the TOR.   This does not include RFO, but actual operations that contain data being sent from the core.	0,1	0x0	null	0
CBO	0x36	0x8	UNC_C_TOR_OCCUPANCY.ALL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); All valid TOR entries.  This includes requests that reside in the TOR for a short time, such as LLC Hits that do not need to snoop cores or requests that get rejected and have to be retried through one of the ingress queues.  The TOR is more commonly a bottleneck in skews with smaller core counts, where the ratio of RTIDs to TOR entries is larger.  Note that there are reserved TOR entries for various request types, so it is possible that a given request type be blocked with an occupancy that is less than 20.  Also note that generally requests will not be able to arbitrate into the TOR pipeline if there are no available TOR slots.	0	0x0	null	0
CBO	0x36	0x4	UNC_C_TOR_OCCUPANCY.EVICTION	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding eviction transactions in the TOR.  Evictions can be quick, such as when the line is in the F, S, or E states and no core valid bits are set.  They can also be longer if either CV bits are set (so the cores need to be snooped) and/or if there is a HitM (in which case it is necessary to write the request out to memory).	0	0x0	null	0
CBO	0x36	0x28	UNC_C_TOR_OCCUPANCY.LOCAL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182)	0	0x0	null	0
CBO	0x36	0x21	UNC_C_TOR_OCCUPANCY.LOCAL_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding  transactions, satisifed by an opcode,  in the TOR that are satisifed by locally HOMed memory.	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0xA	UNC_C_TOR_OCCUPANCY.MISS_ALL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding miss requests in the TOR.  'Miss' means the allocation requires an RTID.  This generally means that the request was sent to memory or MMIO.	0	0x0	null	0
CBO	0x36	0x2A	UNC_C_TOR_OCCUPANCY.MISS_LOCAL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182)	0	0x0	null	0
CBO	0x36	0x23	UNC_C_TOR_OCCUPANCY.MISS_LOCAL_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding Miss transactions, satisifed by an opcode, in the TOR that are satisifed by locally HOMed memory.	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0x3	UNC_C_TOR_OCCUPANCY.MISS_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); TOR entries for miss transactions that match an opcode. This generally means that the request was sent to memory or MMIO.	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0x8A	UNC_C_TOR_OCCUPANCY.MISS_REMOTE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182)	0	0x0	null	0
CBO	0x36	0x83	UNC_C_TOR_OCCUPANCY.MISS_REMOTE_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding Miss transactions, satisifed by an opcode, in the TOR that are satisifed by remote caches or remote memory.	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0x48	UNC_C_TOR_OCCUPANCY.NID_ALL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of NID matched outstanding requests in the TOR.  The NID is programmed in Cn_MSR_PMON_BOX_FILTER.nid.In conjunction with STATE = I, it is possible to monitor misses to specific NIDs in the system.	0	0x0	CBoFilter1[15:0]	0
CBO	0x36	0x44	UNC_C_TOR_OCCUPANCY.NID_EVICTION	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding NID matched eviction transactions in the TOR .	0	0x0	CBoFilter1[15:0]	0
CBO	0x36	0x4A	UNC_C_TOR_OCCUPANCY.NID_MISS_ALL	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding Miss requests in the TOR that match a NID.	0	0x0	CBoFilter1[15:0]	0
CBO	0x36	0x43	UNC_C_TOR_OCCUPANCY.NID_MISS_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding Miss requests in the TOR that match a NID and an opcode.	0	0x0	CBoFilter1[28:20], CBoFilter1[15:0]	0
CBO	0x36	0x41	UNC_C_TOR_OCCUPANCY.NID_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); TOR entries that match a NID and an opcode.	0	0x0	CBoFilter1[28:20], CBoFilter1[15:0]	0
CBO	0x36	0x50	UNC_C_TOR_OCCUPANCY.NID_WB	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); NID matched write transactions int the TOR.	0	0x0	CBoFilter1[15:0]	0
CBO	0x36	0x1	UNC_C_TOR_OCCUPANCY.OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); TOR entries that match an opcode (matched by Cn_MSR_PMON_BOX_FILTER.opc).	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0x88	UNC_C_TOR_OCCUPANCY.REMOTE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182)	0	0x0	null	0
CBO	0x36	0x81	UNC_C_TOR_OCCUPANCY.REMOTE_OPCODE	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Number of outstanding  transactions, satisifed by an opcode,  in the TOR that are satisifed by remote caches or remote memory.	0	0x0	CBoFilter1[28:20]	0
CBO	0x36	0x10	UNC_C_TOR_OCCUPANCY.WB	For each cycle, this event accumulates the number of valid entries in the TOR that match qualifications specified by the subevent.   There are a number of subevent 'filters' but only a subset of the subevent combinations are valid.  Subevents that require an opcode or NID match require the Cn_MSR_PMON_BOX_FILTER.{opc, nid} field to be set.  If, for example, one wanted to count DRD Local Misses, one should select MISS_OPC_MATCH and set Cn_MSR_PMON_BOX_FILTER.opc to DRD (0x182); Write transactions in the TOR.   This does not include RFO, but actual operations that contain data being sent from the core.	0	0x0	null	0
CBO	0x4	0x1	UNC_C_TxR_ADS_USED.AD	0	0,1	0x0	null	0
CBO	0x4	0x2	UNC_C_TxR_ADS_USED.AK	0	0,1	0x0	null	0
CBO	0x4	0x4	UNC_C_TxR_ADS_USED.BL	0	0,1	0x0	null	0
CBO	0x2	0x1	UNC_C_TxR_INSERTS.AD_CACHE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Cachebo destined for the AD ring.  Some example include outbound requests, snoop requests, and snoop responses.	0,1	0x0	null	0
CBO	0x2	0x10	UNC_C_TxR_INSERTS.AD_CORE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Corebo destined for the AD ring.  This is commonly used for outbound requests.	0,1	0x0	null	0
CBO	0x2	0x2	UNC_C_TxR_INSERTS.AK_CACHE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Cachebo destined for the AK ring.  This is commonly used for credit returns and GO responses.	0,1	0x0	null	0
CBO	0x2	0x20	UNC_C_TxR_INSERTS.AK_CORE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Corebo destined for the AK ring.  This is commonly used for snoop responses coming from the core and destined for a Cachebo.	0,1	0x0	null	0
CBO	0x2	0x4	UNC_C_TxR_INSERTS.BL_CACHE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Cachebo destined for the BL ring.  This is commonly used to send data from the cache to various destinations.	0,1	0x0	null	0
CBO	0x2	0x40	UNC_C_TxR_INSERTS.BL_CORE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Corebo destined for the BL ring.  This is commonly used for transfering writeback data to the cache.	0,1	0x0	null	0
CBO	0x2	0x8	UNC_C_TxR_INSERTS.IV_CACHE	Number of allocations into the Cbo Egress.  The Egress is used to queue up requests destined for the ring.; Ring transactions from the Cachebo destined for the IV ring.  This is commonly used for snoops to the cores.	0,1	0x0	null	0
CBO	0x3	0x10	UNC_C_TxR_STARVED.AD_CORE	Counts injection starvation.  This starvation is triggered when the Egress cannot send a transaction onto the ring for a long period of time.; cycles that the core AD egress spent in starvation	0,1	0x0	null	0
CBO	0x3	0x2	UNC_C_TxR_STARVED.AK_BOTH	Counts injection starvation.  This starvation is triggered when the Egress cannot send a transaction onto the ring for a long period of time.; cycles that both AK egresses spent in starvation	0,1	0x0	null	0
CBO	0x3	0x8	UNC_C_TxR_STARVED.IV	Counts injection starvation.  This starvation is triggered when the Egress cannot send a transaction onto the ring for a long period of time.; cycles that the cachebo IV egress spent in starvation	0,1	0x0	null	0
CBO	0x1B	0x33	UNC_C_RING_AD_USED.UP	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1B	0xCC	UNC_C_RING_AD_USED.DOWN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1C	0x33	UNC_C_RING_AK_USED.UP	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1C	0xCC	UNC_C_RING_AK_USED.DOWN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1D	0x33	UNC_C_RING_BL_USED.UP	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1D	0xCC	UNC_C_RING_BL_USED.DOWN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x5	0x2	UNC_C_RING_BOUNCES.AD_IRQ	0	0,1	0	null	0
CBO	0x5	0x4	UNC_C_RING_BOUNCES.AK	0	0,1	0	null	0
CBO	0x5	0x8	UNC_C_RING_BOUNCES.BL	0	0,1	0	null	0
CBO	0x5	0x10	UNC_C_RING_BOUNCES.IV	0	0,1	0	null	0
CBO	0x6	0x1	UNC_C_RING_SINK_STARVED.AD_IRQ	0	0,1	0	null	0
CBO	0x6	0x2	UNC_C_RING_SINK_STARVED.AD_IPQ	0	0,1	0	null	0
CBO	0x6	0x10	UNC_C_RING_SINK_STARVED.IV	0	0,1	0	null	0
CBO	0x12	0x4	UNC_C_RxR_EXT_STARVED.PRQ	IRQ is blocking the ingress queue and causing the starvation.	0,1	0	null	0
CBO	0x13	0x2	UNC_C_RxR_INSERTS.IRQ_REJ	Counts number of allocations per cycle into the specified Ingress queue.	0,1	0	null	0
CBO	0x1B	0x3	UNC_C_RING_AD_USED.CW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1B	0xC	UNC_C_RING_AD_USED.CCW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.  We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1C	0x3	UNC_C_RING_AK_USED.CW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1C	0xC	UNC_C_RING_AK_USED.CCW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1D	0x3	UNC_C_RING_BL_USED.CW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x1D	0xC	UNC_C_RING_BL_USED.CCW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.We really have two rings in JKT -- a clockwise ring and a counter-clockwise ring.  On the left side of the ring, the UP direction is on the clockwise ring and DN is on the counter-clockwise ring.  On the right side of the ring, this is reversed.  The first half of the CBos are on the left side of the ring, and the 2nd half are on the right side of the ring.  In other words (for example), in a 4c part, Cbo 0 UP AD is NOT the same ring as CBo 2 UP AD because they are on opposite sides of the ring.	2,3	0	null	0
CBO	0x11	0x2	UNC_C_RxR_OCCUPANCY.IRQ_REJ	Counts number of entries in the specified Ingress queue in each cycle.	0	0	null	0
HA	0x20	0x3	UNC_H_ADDR_OPC_MATCH.FILT	0	0,1,2,3	0x0	HA_AddrMatch0[31:6], HA_AddrMatch1[13:0], HA_OpcodeMatch[5:0]	0
HA	0x52	0x0	UNC_H_BT_BYPASS	Number of transactions that bypass the BT (fifo) to HT	0,1,2,3	0x0	null	0
HA	0x42	0x1	UNC_H_BT_CYCLES_NE.LOCAL	Cycles the Backup Tracker (BT) is not empty. The BT is the actual HOM tracker in IVT.	0,1,2,3	0x0	null	0
HA	0x42	0x2	UNC_H_BT_CYCLES_NE.REMOTE	Cycles the Backup Tracker (BT) is not empty. The BT is the actual HOM tracker in IVT.	0,1,2,3	0x0	null	0
HA	0x43	0x1	UNC_H_BT_OCCUPANCY.LOCAL	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x43	0x4	UNC_H_BT_OCCUPANCY.READS_LOCAL	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x43	0x8	UNC_H_BT_OCCUPANCY.READS_REMOTE	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x43	0x2	UNC_H_BT_OCCUPANCY.REMOTE	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x43	0x10	UNC_H_BT_OCCUPANCY.WRITES_LOCAL	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x43	0x20	UNC_H_BT_OCCUPANCY.WRITES_REMOTE	Accumulates the occupancy of the HA BT pool in every cycle.  This can be used with the not empty stat to calculate average queue occupancy or the allocations stat in order to calculate average queue latency.  HA BTs are allocated as soon as a request enters the HA and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x51	0x4	UNC_H_BT_TO_HT_NOT_ISSUED.INCOMING_BL_HAZARD	Counts the number of cycles when the HA does not issue transaction from BT to HT.; Cycles unable to issue from BT due to incoming BL data hazard	0,1,2,3	0x0	null	0
HA	0x51	0x2	UNC_H_BT_TO_HT_NOT_ISSUED.INCOMING_SNP_HAZARD	Counts the number of cycles when the HA does not issue transaction from BT to HT.; Cycles unable to issue from BT due to incoming snoop hazard	0,1,2,3	0x0	null	0
HA	0x14	0x2	UNC_H_BYPASS_IMC.NOT_TAKEN	Counts the number of times when the HA was able to bypass was attempted.  This is a latency optimization for situations when there is light loadings on the memory subsystem.  This can be filted by when the bypass was taken and when it was not.; Filter for transactions that could not take the bypass.	0,1,2,3	0x0	null	0
HA	0x14	0x1	UNC_H_BYPASS_IMC.TAKEN	Counts the number of times when the HA was able to bypass was attempted.  This is a latency optimization for situations when there is light loadings on the memory subsystem.  This can be filted by when the bypass was taken and when it was not.; Filter for transactions that succeeded in taking the bypass.	0,1,2,3	0x0	null	0
HA	0x0	0x0	UNC_H_CLOCKTICKS	Counts the number of uclks in the HA.  This will be slightly different than the count in the Ubox because of enable/freeze delays.  The HA is on the other side of the die from the fixed Ubox uclk counter, so the drift could be somewhat larger than in units that are closer like the QPI Agent.	0,1,2,3	0x0	null	0
HA	0xb	0x8	UNC_H_CONFLICT_CYCLES.ACKCNFLTS	Count the number of Ackcnflts	0,1,2,3	0x0	null	0
HA	0xb	0x10	UNC_H_CONFLICT_CYCLES.CMP_FWDS	Count the number of Cmp_Fwd. This will give the number of late conflicts.	0,1,2,3	0x0	null	0
HA	0xb	0x2	UNC_H_CONFLICT_CYCLES.CONFLICT	Counts the number of cycles that we are handling conflicts.	0,1,2,3	0x0	null	0
HA	0xb	0x4	UNC_H_CONFLICT_CYCLES.LAST	Count every last conflictor in conflict chain. Can be used to compute the average conflict chain length as (#Ackcnflts/#LastConflictor)+1. This can be used to give a feel for the conflict chain lenghts while analyzing lock kernels.	0,1,2,3	0x0	null	0
HA	0x11	0x0	UNC_H_DIRECT2CORE_COUNT	Number of Direct2Core messages sent	0,1,2,3	0x0	null	0
HA	0x12	0x0	UNC_H_DIRECT2CORE_CYCLES_DISABLED	Number of cycles in which Direct2Core was disabled	0,1,2,3	0x0	null	0
HA	0x13	0x0	UNC_H_DIRECT2CORE_TXN_OVERRIDE	Number of Reads where Direct2Core overridden	0,1,2,3	0x0	null	0
HA	0x41	0x0	UNC_H_DIRECTORY_LAT_OPT	Directory Latency Optimization Data Return Path Taken. When directory mode is enabled and the directory retuned for a read is Dir=I, then data can be returned using a faster path if certain conditions are met (credits, free pipeline, etc).	0,1,2,3	0x0	null	0
HA	0xc	0x10	UNC_H_DIRECTORY_LOOKUP.ANY	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xc	0x2	UNC_H_DIRECTORY_LOOKUP.NO_SNP	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.; Filters for transactions that did not have to send any snoops because the directory bit was clear.	0,1,2,3	0x0	null	0
HA	0xc	0x8	UNC_H_DIRECTORY_LOOKUP.SNOOP_A	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xc	0x2	UNC_H_DIRECTORY_LOOKUP.SNOOP_S	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xc	0x1	UNC_H_DIRECTORY_LOOKUP.SNP	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.; Filters for transactions that had to send one or more snoops because the directory bit was set.	0,1,2,3	0x0	null	0
HA	0xc	0x80	UNC_H_DIRECTORY_LOOKUP.STATE_A	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xc	0x20	UNC_H_DIRECTORY_LOOKUP.STATE_I	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xc	0x40	UNC_H_DIRECTORY_LOOKUP.STATE_S	Counts the number of transactions that looked up the directory.  Can be filtered by requests that had to snoop and those that did not have to.	0,1,2,3	0x0	null	0
HA	0xd	0x20	UNC_H_DIRECTORY_UPDATE.A2I	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x40	UNC_H_DIRECTORY_UPDATE.A2S	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x3	UNC_H_DIRECTORY_UPDATE.ANY	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x4	UNC_H_DIRECTORY_UPDATE.I2A	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x2	UNC_H_DIRECTORY_UPDATE.I2S	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x10	UNC_H_DIRECTORY_UPDATE.S2A	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0xd	0x8	UNC_H_DIRECTORY_UPDATE.S2I	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.	0,1,2,3	0x0	null	0
HA	0x59	0x0	UNC_H_IGR_AD_QPI2_ACCUMULATOR	Accumulates the number of credits available to the QPI Link 2 AD Ingress buffer.	0,1,2,3	0x0	null	0
HA	0x5a	0x0	UNC_H_IGR_BL_QPI2_ACCUMULATOR	Accumulates the number of credits available to the QPI Link 2 BL Ingress buffer.	0,1,2,3	0x0	null	0
HA	0x22	0x1	UNC_H_IGR_NO_CREDIT_CYCLES.AD_QPI0	Counts the number of cycles when the HA does not have credits to send messages to the QPI Agent.  This can be filtered by the different credit pools and the different links.	0,1,2,3	0x0	null	0
HA	0x22	0x2	UNC_H_IGR_NO_CREDIT_CYCLES.AD_QPI1	Counts the number of cycles when the HA does not have credits to send messages to the QPI Agent.  This can be filtered by the different credit pools and the different links.	0,1,2,3	0x0	null	0
HA	0x22	0x4	UNC_H_IGR_NO_CREDIT_CYCLES.BL_QPI0	Counts the number of cycles when the HA does not have credits to send messages to the QPI Agent.  This can be filtered by the different credit pools and the different links.	0,1,2,3	0x0	null	0
HA	0x22	0x8	UNC_H_IGR_NO_CREDIT_CYCLES.BL_QPI1	Counts the number of cycles when the HA does not have credits to send messages to the QPI Agent.  This can be filtered by the different credit pools and the different links.	0,1,2,3	0x0	null	0
HA	0x17	0x1	UNC_H_IMC_READS.NORMAL	Count of the number of reads issued to any of the memory controller channels.  This can be filtered by the priority of the reads.	0,1,2,3	0x0	null	0
HA	0x1e	0x0	UNC_H_IMC_RETRY	0	0,1,2,3	0x0	null	0
HA	0x1a	0xF	UNC_H_IMC_WRITES.ALL	Counts the total number of full line writes issued from the HA into the memory controller.  This counts for all four channels.  It can be filtered by full/partial and ISOCH/non-ISOCH.	0,1,2,3	0x0	null	0
HA	0x1a	0x1	UNC_H_IMC_WRITES.FULL	Counts the total number of full line writes issued from the HA into the memory controller.  This counts for all four channels.  It can be filtered by full/partial and ISOCH/non-ISOCH.	0,1,2,3	0x0	null	0
HA	0x1a	0x4	UNC_H_IMC_WRITES.FULL_ISOCH	Counts the total number of full line writes issued from the HA into the memory controller.  This counts for all four channels.  It can be filtered by full/partial and ISOCH/non-ISOCH.	0,1,2,3	0x0	null	0
HA	0x1a	0x2	UNC_H_IMC_WRITES.PARTIAL	Counts the total number of full line writes issued from the HA into the memory controller.  This counts for all four channels.  It can be filtered by full/partial and ISOCH/non-ISOCH.	0,1,2,3	0x0	null	0
HA	0x1a	0x8	UNC_H_IMC_WRITES.PARTIAL_ISOCH	Counts the total number of full line writes issued from the HA into the memory controller.  This counts for all four channels.  It can be filtered by full/partial and ISOCH/non-ISOCH.	0,1,2,3	0x0	null	0
HA	0x57	0x1	UNC_H_IODC_CONFLICTS.REMOTE_INVI2E_SAME_RTID	0	0,1,2,3	0x0	null	0
HA	0x57	0x4	UNC_H_IODC_CONFLICTS.REMOTE_OTHER_SAME_ADDR	0	0,1,2,3	0x0	null	0
HA	0x56	0x0	UNC_H_IODC_INSERTS	IODC Allocations	0,1,2,3	0x0	null	0
HA	0x58	0x0	UNC_H_IODC_OLEN_WBMTOI	Num IODC 0 Length Writebacks M to I - All of which are dropped.	0,1,2,3	0x0	null	0
HA	0x53	0x4	UNC_H_OSB.INVITOE_LOCAL	Count of OSB snoop broadcasts. Counts by 1 per request causing OSB snoops to be broadcast. Does not count all the snoops generated by OSB.	0,1,2,3	0x0	null	0
HA	0x53	0x2	UNC_H_OSB.READS_LOCAL	Count of OSB snoop broadcasts. Counts by 1 per request causing OSB snoops to be broadcast. Does not count all the snoops generated by OSB.	0,1,2,3	0x0	null	0
HA	0x53	0x8	UNC_H_OSB.REMOTE	Count of OSB snoop broadcasts. Counts by 1 per request causing OSB snoops to be broadcast. Does not count all the snoops generated by OSB.	0,1,2,3	0x0	null	0
HA	0x54	0x1	UNC_H_OSB_EDR.ALL	Counts the number of transactions that broadcast snoop due to OSB, but found clean data in memory and was able to do early data return	0,1,2,3	0x0	null	0
HA	0x54	0x2	UNC_H_OSB_EDR.READS_LOCAL_I	Counts the number of transactions that broadcast snoop due to OSB, but found clean data in memory and was able to do early data return	0,1,2,3	0x0	null	0
HA	0x54	0x8	UNC_H_OSB_EDR.READS_LOCAL_S	Counts the number of transactions that broadcast snoop due to OSB, but found clean data in memory and was able to do early data return	0,1,2,3	0x0	null	0
HA	0x54	0x4	UNC_H_OSB_EDR.READS_REMOTE_I	Counts the number of transactions that broadcast snoop due to OSB, but found clean data in memory and was able to do early data return	0,1,2,3	0x0	null	0
HA	0x54	0x10	UNC_H_OSB_EDR.READS_REMOTE_S	Counts the number of transactions that broadcast snoop due to OSB, but found clean data in memory and was able to do early data return	0,1,2,3	0x0	null	0
HA	0x1	0x10	UNC_H_REQUESTS.INVITOE_LOCAL	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only InvItoEs coming from the local socket.	0,1,2,3	0x0	null	0
HA	0x1	0x20	UNC_H_REQUESTS.INVITOE_REMOTE	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only InvItoEs coming from remote sockets.	0,1,2,3	0x0	null	0
HA	0x1	0x3	UNC_H_REQUESTS.READS	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; Incoming ead requests.  This is a good proxy for LLC Read Misses (including RFOs).	0,1,2,3	0x0	null	0
HA	0x1	0x1	UNC_H_REQUESTS.READS_LOCAL	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only read requests coming from the local socket.  This is a good proxy for LLC Read Misses (including RFOs) from the local socket.	0,1,2,3	0x0	null	0
HA	0x1	0x2	UNC_H_REQUESTS.READS_REMOTE	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only read requests coming from the remote socket.  This is a good proxy for LLC Read Misses (including RFOs) from the remote socket.	0,1,2,3	0x0	null	0
HA	0x1	0xC	UNC_H_REQUESTS.WRITES	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; Incoming write requests.	0,1,2,3	0x0	null	0
HA	0x1	0x4	UNC_H_REQUESTS.WRITES_LOCAL	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only writes coming from the local socket.	0,1,2,3	0x0	null	0
HA	0x1	0x8	UNC_H_REQUESTS.WRITES_REMOTE	Counts the total number of read requests made into the Home Agent. Reads include all read opcodes (including RFO).  Writes include all writes (streaming, evictions, HitM, etc).; This filter includes only writes coming from remote sockets.	0,1,2,3	0x0	null	0
HA	0x3e	0x4	UNC_H_RING_AD_USED.CCW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3e	0x8	UNC_H_RING_AD_USED.CCW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3e	0x40	UNC_H_RING_AD_USED.CCW_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3e	0x80	UNC_H_RING_AD_USED.CCW_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3e	0x1	UNC_H_RING_AD_USED.CW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3e	0x2	UNC_H_RING_AD_USED.CW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3e	0x10	UNC_H_RING_AD_USED.CW_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3e	0x20	UNC_H_RING_AD_USED.CW_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3f	0x4	UNC_H_RING_AK_USED.CCW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3f	0x8	UNC_H_RING_AK_USED.CCW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3f	0x40	UNC_H_RING_AK_USED.CCW_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3f	0x80	UNC_H_RING_AK_USED.CCW_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3f	0x1	UNC_H_RING_AK_USED.CW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3f	0x2	UNC_H_RING_AK_USED.CW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x3f	0x10	UNC_H_RING_AK_USED.CW_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x3f	0x20	UNC_H_RING_AK_USED.CW_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x40	0x4	UNC_H_RING_BL_USED.CCW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x40	0x8	UNC_H_RING_BL_USED.CCW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x40	0x40	UNC_H_RING_BL_USED.CCW_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x40	0x80	UNC_H_RING_BL_USED.CCW_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x40	0x1	UNC_H_RING_BL_USED.CW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x40	0x2	UNC_H_RING_BL_USED.CW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
HA	0x40	0x10	UNC_H_RING_BL_USED.CW_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x40	0x20	UNC_H_RING_BL_USED.CW_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
HA	0x15	0x1	UNC_H_RPQ_CYCLES_NO_REG_CREDITS.CHN0	Counts the number of cycles when there are no regular credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 0 only.	0,1,2,3	0x0	null	0
HA	0x15	0x2	UNC_H_RPQ_CYCLES_NO_REG_CREDITS.CHN1	Counts the number of cycles when there are no regular credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 1 only.	0,1,2,3	0x0	null	0
HA	0x15	0x4	UNC_H_RPQ_CYCLES_NO_REG_CREDITS.CHN2	Counts the number of cycles when there are no regular credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 2 only.	0,1,2,3	0x0	null	0
HA	0x15	0x8	UNC_H_RPQ_CYCLES_NO_REG_CREDITS.CHN3	Counts the number of cycles when there are no regular credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 3 only.	0,1,2,3	0x0	null	0
HA	0x16	0x1	UNC_H_RPQ_CYCLES_NO_SPEC_CREDITS.CHN0	Counts the number of cycles when there are no special credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 0 only.	0,1,2,3	0x0	null	0
HA	0x16	0x2	UNC_H_RPQ_CYCLES_NO_SPEC_CREDITS.CHN1	Counts the number of cycles when there are no special credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 1 only.	0,1,2,3	0x0	null	0
HA	0x16	0x4	UNC_H_RPQ_CYCLES_NO_SPEC_CREDITS.CHN2	Counts the number of cycles when there are no special credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 2 only.	0,1,2,3	0x0	null	0
HA	0x16	0x8	UNC_H_RPQ_CYCLES_NO_SPEC_CREDITS.CHN3	Counts the number of cycles when there are no special credits available for posting reads from the HA into the iMC.  In order to send reads into the memory controller, the HA must first acquire a credit for the iMC's RPQ (read pending queue).  This queue is broken into regular credits/buffers that are used by general reads, and special requests such as ISOCH reads.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 3 only.	0,1,2,3	0x0	null	0
HA	0x21	0x40	UNC_H_SNOOP_RESP.RSPCNFLCT	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for snoops responses of RspConflict.  This is returned when a snoop finds an existing outstanding transaction in a remote caching agent when it CAMs that caching agent.  This triggers conflict resolution hardware.  This covers both RspCnflct and RspCnflctWbI.	0,1,2,3	0x0	null	0
HA	0x21	0x1	UNC_H_SNOOP_RESP.RSPI	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for snoops responses of RspI.  RspI is returned when the remote cache does not have the data, or when the remote cache silently evicts data (such as when an RFO hits non-modified data).	0,1,2,3	0x0	null	0
HA	0x21	0x4	UNC_H_SNOOP_RESP.RSPIFWD	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for snoop responses of RspIFwd.  This is returned when a remote caching agent forwards data and the requesting agent is able to acquire the data in E or M states.  This is commonly returned with RFO transactions.  It can be either a HitM or a HitFE.	0,1,2,3	0x0	null	0
HA	0x21	0x2	UNC_H_SNOOP_RESP.RSPS	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for snoop responses of RspS.  RspS is returned when a remote cache has data but is not forwarding it.  It is a way to let the requesting socket know that it cannot allocate the data in E state.  No data is sent with S RspS.	0,1,2,3	0x0	null	0
HA	0x21	0x8	UNC_H_SNOOP_RESP.RSPSFWD	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for a snoop response of RspSFwd.  This is returned when a remote caching agent forwards data but holds on to its currentl copy.  This is common for data and code reads that hit in a remote socket in E or F state.	0,1,2,3	0x0	null	0
HA	0x21	0x20	UNC_H_SNOOP_RESP.RSP_FWD_WB	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for a snoop response of Rsp*Fwd*WB.  This snoop response is only used in 4s systems.  It is used when a snoop HITM's in a remote caching agent and it directly forwards data to a requestor, and simultaneously returns data to the home to be written back to memory.	0,1,2,3	0x0	null	0
HA	0x21	0x10	UNC_H_SNOOP_RESP.RSP_WB	Counts the total number of RspI snoop responses received.  Whenever a snoops are issued, one or more snoop responses will be returned depending on the topology of the system.   In systems larger than 2s, when multiple snoops are returned this will count all the snoops that are received.  For example, if 3 snoops were issued and returned RspI, RspS, and RspSFwd; then each of these sub-events would increment by 1.; Filters for a snoop response of RspIWB or RspSWB.  This is returned when a non-RFO request hits in M state.  Data and Code Reads can return either RspIWB or RspSWB depending on how the system has been configured.  InvItoE transactions will also return RspIWB because they must acquire ownership.	0,1,2,3	0x0	null	0
HA	0x60	0x80	UNC_H_SNP_RESP_RECV_LOCAL.OTHER	Number of snoop responses received for a Local  request; Filters for all other snoop responses.	0,1,2,3	0x0	null	0
HA	0x60	0x40	UNC_H_SNP_RESP_RECV_LOCAL.RSPCNFLCT	Number of snoop responses received for a Local  request; Filters for snoops responses of RspConflict.  This is returned when a snoop finds an existing outstanding transaction in a remote caching agent when it CAMs that caching agent.  This triggers conflict resolution hardware.  This covers both RspCnflct and RspCnflctWbI.	0,1,2,3	0x0	null	0
HA	0x60	0x1	UNC_H_SNP_RESP_RECV_LOCAL.RSPI	Number of snoop responses received for a Local  request; Filters for snoops responses of RspI.  RspI is returned when the remote cache does not have the data, or when the remote cache silently evicts data (such as when an RFO hits non-modified data).	0,1,2,3	0x0	null	0
HA	0x60	0x4	UNC_H_SNP_RESP_RECV_LOCAL.RSPIFWD	Number of snoop responses received for a Local  request; Filters for snoop responses of RspIFwd.  This is returned when a remote caching agent forwards data and the requesting agent is able to acquire the data in E or M states.  This is commonly returned with RFO transactions.  It can be either a HitM or a HitFE.	0,1,2,3	0x0	null	0
HA	0x60	0x2	UNC_H_SNP_RESP_RECV_LOCAL.RSPS	Number of snoop responses received for a Local  request; Filters for snoop responses of RspS.  RspS is returned when a remote cache has data but is not forwarding it.  It is a way to let the requesting socket know that it cannot allocate the data in E state.  No data is sent with S RspS.	0,1,2,3	0x0	null	0
HA	0x60	0x8	UNC_H_SNP_RESP_RECV_LOCAL.RSPSFWD	Number of snoop responses received for a Local  request; Filters for a snoop response of RspSFwd.  This is returned when a remote caching agent forwards data but holds on to its currentl copy.  This is common for data and code reads that hit in a remote socket in E or F state.	0,1,2,3	0x0	null	0
HA	0x60	0x20	UNC_H_SNP_RESP_RECV_LOCAL.RSPxFWDxWB	Number of snoop responses received for a Local  request; Filters for a snoop response of Rsp*Fwd*WB.  This snoop response is only used in 4s systems.  It is used when a snoop HITM's in a remote caching agent and it directly forwards data to a requestor, and simultaneously returns data to the home to be written back to memory.	0,1,2,3	0x0	null	0
HA	0x60	0x10	UNC_H_SNP_RESP_RECV_LOCAL.RSPxWB	Number of snoop responses received for a Local  request; Filters for a snoop response of RspIWB or RspSWB.  This is returned when a non-RFO request hits in M state.  Data and Code Reads can return either RspIWB or RspSWB depending on how the system has been configured.  InvItoE transactions will also return RspIWB because they must acquire ownership.	0,1,2,3	0x0	null	0
HA	0x1b	0x1	UNC_H_TAD_REQUESTS_G0.REGION0	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 0	0,1,2,3	0x0	null	0
HA	0x1b	0x2	UNC_H_TAD_REQUESTS_G0.REGION1	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 1	0,1,2,3	0x0	null	0
HA	0x1b	0x4	UNC_H_TAD_REQUESTS_G0.REGION2	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 2	0,1,2,3	0x0	null	0
HA	0x1b	0x8	UNC_H_TAD_REQUESTS_G0.REGION3	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 3	0,1,2,3	0x0	null	0
HA	0x1b	0x10	UNC_H_TAD_REQUESTS_G0.REGION4	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 4	0,1,2,3	0x0	null	0
HA	0x1b	0x20	UNC_H_TAD_REQUESTS_G0.REGION5	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 5	0,1,2,3	0x0	null	0
HA	0x1b	0x40	UNC_H_TAD_REQUESTS_G0.REGION6	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 6	0,1,2,3	0x0	null	0
HA	0x1b	0x80	UNC_H_TAD_REQUESTS_G0.REGION7	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 0 to 7.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 7	0,1,2,3	0x0	null	0
HA	0x1c	0x4	UNC_H_TAD_REQUESTS_G1.REGION10	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 8 to 10.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 10	0,1,2,3	0x0	null	0
HA	0x1c	0x8	UNC_H_TAD_REQUESTS_G1.REGION11	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 8 to 10.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 11	0,1,2,3	0x0	null	0
HA	0x1c	0x1	UNC_H_TAD_REQUESTS_G1.REGION8	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 8 to 10.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 8	0,1,2,3	0x0	null	0
HA	0x1c	0x2	UNC_H_TAD_REQUESTS_G1.REGION9	Counts the number of HA requests to a given TAD region.  There are up to 11 TAD (target address decode) regions in each home agent.  All requests destined for the memory controller must first be decoded to determine which TAD region they are in.  This event is filtered based on the TAD region ID, and covers regions 8 to 10.  This event is useful for understanding how applications are using the memory that is spread across the different memory regions.  It is particularly useful for Monroe systems that use the TAD to enable individual channels to enter self-refresh to save power.; Filters request made to TAD Region 9	0,1,2,3	0x0	null	0
HA	0x3	0x0	UNC_H_TRACKER_CYCLES_NE	Counts the number of cycles when the local HA tracker pool is not empty.  This can be used with edge detect to identify the number of situations when the pool became empty.  This should not be confused with RTID credit usage -- which must be tracked inside each cbo individually -- but represents the actual tracker buffer structure.  In other words, this buffer could be completely empty, but there may still be credits in use by the CBos.  This stat can be used in conjunction with the occupancy accumulation stat in order to calculate average queue occpancy.  HA trackers are allocated as soon as a request enters the HA if an HT (Home Tracker) entry is available and is released after the snoop response and data return (or post in the case of a write) and the response is returned on the ring.	0,1,2,3	0x0	null	0
HA	0x2a	0x3	UNC_H_TxR_AD_CYCLES_FULL.ALL	AD Egress Full; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x2a	0x1	UNC_H_TxR_AD_CYCLES_FULL.SCHED0	AD Egress Full; Filter for cycles full  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x2a	0x2	UNC_H_TxR_AD_CYCLES_FULL.SCHED1	AD Egress Full; Filter for cycles full  from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x29	0x3	UNC_H_TxR_AD_CYCLES_NE.ALL	AD Egress Not Empty; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x29	0x1	UNC_H_TxR_AD_CYCLES_NE.SCHED0	AD Egress Not Empty; Filter for cycles not empty  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x29	0x2	UNC_H_TxR_AD_CYCLES_NE.SCHED1	AD Egress Not Empty; Filter for cycles not empty from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x27	0x3	UNC_H_TxR_AD_INSERTS.ALL	AD Egress Allocations; Allocations from both schedulers	0,1,2,3	0x0	null	0
HA	0x27	0x1	UNC_H_TxR_AD_INSERTS.SCHED0	AD Egress Allocations; Filter for allocations from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x27	0x2	UNC_H_TxR_AD_INSERTS.SCHED1	AD Egress Allocations; Filter for allocations from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x28	0x1	UNC_H_TxR_AD_OCCUPANCY.SCHED0	AD Egress Occupancy; Filter for occupancy from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x28	0x2	UNC_H_TxR_AD_OCCUPANCY.SCHED1	AD Egress Occupancy; Filter for occupancy from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0xe	0x2	UNC_H_TxR_AK.CRD_CBO	0	0,1,2,3	0x0	null	0
HA	0x32	0x3	UNC_H_TxR_AK_CYCLES_FULL.ALL	AK Egress Full; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x32	0x1	UNC_H_TxR_AK_CYCLES_FULL.SCHED0	AK Egress Full; Filter for cycles full  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x32	0x2	UNC_H_TxR_AK_CYCLES_FULL.SCHED1	AK Egress Full; Filter for cycles full  from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x31	0x3	UNC_H_TxR_AK_CYCLES_NE.ALL	AK Egress Not Empty; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x31	0x1	UNC_H_TxR_AK_CYCLES_NE.SCHED0	AK Egress Not Empty; Filter for cycles not empty  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x31	0x2	UNC_H_TxR_AK_CYCLES_NE.SCHED1	AK Egress Not Empty; Filter for cycles not empty from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x2f	0x3	UNC_H_TxR_AK_INSERTS.ALL	AK Egress Allocations; Allocations from both schedulers	0,1,2,3	0x0	null	0
HA	0x2f	0x1	UNC_H_TxR_AK_INSERTS.SCHED0	AK Egress Allocations; Filter for allocations from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x2f	0x2	UNC_H_TxR_AK_INSERTS.SCHED1	AK Egress Allocations; Filter for allocations from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x30	0x1	UNC_H_TxR_AK_OCCUPANCY.SCHED0	AK Egress Occupancy; Filter for occupancy from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x30	0x2	UNC_H_TxR_AK_OCCUPANCY.SCHED1	AK Egress Occupancy; Filter for occupancy from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x10	0x1	UNC_H_TxR_BL.DRS_CACHE	Counts the number of DRS messages sent out on the BL ring.   This can be filtered by the destination.; Filter for data being sent to the cache.	0,1,2,3	0x0	null	0
HA	0x10	0x2	UNC_H_TxR_BL.DRS_CORE	Counts the number of DRS messages sent out on the BL ring.   This can be filtered by the destination.; Filter for data being sent directly to the requesting core.	0,1,2,3	0x0	null	0
HA	0x10	0x4	UNC_H_TxR_BL.DRS_QPI	Counts the number of DRS messages sent out on the BL ring.   This can be filtered by the destination.; Filter for data being sent to a remote socket over QPI.	0,1,2,3	0x0	null	0
HA	0x36	0x3	UNC_H_TxR_BL_CYCLES_FULL.ALL	BL Egress Full; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x36	0x1	UNC_H_TxR_BL_CYCLES_FULL.SCHED0	BL Egress Full; Filter for cycles full  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x36	0x2	UNC_H_TxR_BL_CYCLES_FULL.SCHED1	BL Egress Full; Filter for cycles full  from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x35	0x3	UNC_H_TxR_BL_CYCLES_NE.ALL	BL Egress Not Empty; Cycles full from both schedulers	0,1,2,3	0x0	null	0
HA	0x35	0x1	UNC_H_TxR_BL_CYCLES_NE.SCHED0	BL Egress Not Empty; Filter for cycles not empty  from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x35	0x2	UNC_H_TxR_BL_CYCLES_NE.SCHED1	BL Egress Not Empty; Filter for cycles not empty from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x33	0x3	UNC_H_TxR_BL_INSERTS.ALL	BL Egress Allocations; Allocations from both schedulers	0,1,2,3	0x0	null	0
HA	0x33	0x1	UNC_H_TxR_BL_INSERTS.SCHED0	BL Egress Allocations; Filter for allocations from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x33	0x2	UNC_H_TxR_BL_INSERTS.SCHED1	BL Egress Allocations; Filter for allocations from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x34	0x3	UNC_H_TxR_BL_OCCUPANCY.ALL	BL Egress Occupancy	0,1,2,3	0x0	null	0
HA	0x34	0x1	UNC_H_TxR_BL_OCCUPANCY.SCHED0	BL Egress Occupancy; Filter for occupancy from scheduler bank 0	0,1,2,3	0x0	null	0
HA	0x34	0x2	UNC_H_TxR_BL_OCCUPANCY.SCHED1	BL Egress Occupancy; Filter for occupancy from scheduler bank 1	0,1,2,3	0x0	null	0
HA	0x18	0x1	UNC_H_WPQ_CYCLES_NO_REG_CREDITS.CHN0	Counts the number of cycles when there are no regular credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 0 only.	0,1,2,3	0x0	null	0
HA	0x18	0x2	UNC_H_WPQ_CYCLES_NO_REG_CREDITS.CHN1	Counts the number of cycles when there are no regular credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 1 only.	0,1,2,3	0x0	null	0
HA	0x18	0x4	UNC_H_WPQ_CYCLES_NO_REG_CREDITS.CHN2	Counts the number of cycles when there are no regular credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 2 only.	0,1,2,3	0x0	null	0
HA	0x18	0x8	UNC_H_WPQ_CYCLES_NO_REG_CREDITS.CHN3	Counts the number of cycles when there are no regular credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the regular credits  Common high banwidth workloads should be able to make use of all of the regular buffers, but it will be difficult (and uncommon) to make use of both the regular and special buffers at the same time.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 3 only.	0,1,2,3	0x0	null	0
HA	0x19	0x1	UNC_H_WPQ_CYCLES_NO_SPEC_CREDITS.CHN0	Counts the number of cycles when there are no special credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 0 only.	0,1,2,3	0x0	null	0
HA	0x19	0x2	UNC_H_WPQ_CYCLES_NO_SPEC_CREDITS.CHN1	Counts the number of cycles when there are no special credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 1 only.	0,1,2,3	0x0	null	0
HA	0x19	0x4	UNC_H_WPQ_CYCLES_NO_SPEC_CREDITS.CHN2	Counts the number of cycles when there are no special credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 2 only.	0,1,2,3	0x0	null	0
HA	0x19	0x8	UNC_H_WPQ_CYCLES_NO_SPEC_CREDITS.CHN3	Counts the number of cycles when there are no special credits available for posting writes from the HA into the iMC.  In order to send writes into the memory controller, the HA must first acquire a credit for the iMC's WPQ (write pending queue).  This queue is broken into regular credits/buffers that are used by general writes, and special requests such as ISOCH writes.  This count only tracks the special credits.  This statistic is generally not interesting for general IA workloads, but may be of interest for understanding the characteristics of systems using ISOCH.  One can filter based on the memory controller channel.  One or more channels can be tracked at a given time.; Filter for memory controller channel 3 only.	0,1,2,3	0x0	null	0
HA	0x20	0x1	UNC_H_ADDR_OPC_MATCH.ADDR	0	0,1,2,3	0	HA_AddrMatch0[31:6], HA_AddrMatch1[13:0]	0
HA	0x20	0x2	UNC_H_ADDR_OPC_MATCH.OPC	0	0,1,2,3	0	HA_OpcodeMatch[5:0]	0
HA	0x20	0x4	UNC_H_ADDR_OPC_MATCH.AD	0	0,1,2,3	0	HA_OpcodeMatch[5:0]	0
HA	0x20	0x8	UNC_H_ADDR_OPC_MATCH.BL	0	0,1,2,3	0	HA_OpcodeMatch[5:0]	0
HA	0x20	0x10	UNC_H_ADDR_OPC_MATCH.AK	0	0,1,2,3	0	HA_OpcodeMatch[5:0]	0
HA	0x42	0x0	UNC_H_BT_CYCLES_NE	Cycles the Backup Tracker (BT) is not empty. The BT is the actual HOM tracker in IVT.	0,1,2,3	0	null	0
HA	0x51	0x8	UNC_H_BT_TO_HT_NOT_ISSUED.RSPACKCFLT_HAZARD	Counts the number of cycles when the HA does not issue transaction from BT to HT.; Cycles unable to issue from BT due to incoming BL data hazard	0,1,2,3	0	null	0
HA	0x51	0x10	UNC_H_BT_TO_HT_NOT_ISSUED.WBMDATA_HAZARD	Counts the number of cycles when the HA does not issue transaction from BT to HT.; Cycles unable to issue from BT due to incoming BL data hazard	0,1,2,3	0	null	0
HA	0xD	0x1	UNC_H_DIRECTORY_UPDATE.SET	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.; Filter for directory sets.  This occurs when a remote read transaction requests memory, bringing it to a remote cache.	0,1,2,3	0	null	0
HA	0xD	0x2	UNC_H_DIRECTORY_UPDATE.CLEAR	Counts the number of directory updates that were required.  These result in writes to the memory controller.  This can be filtered by directory sets and directory clears.; Filter for directory clears.  This occurs when snoops were sent and all returned with RspI.	0,1,2,3	0	null	0
HA	0x59	0x0	UNC_H_IGR_CREDITS_AD_QPI2	Accumulates the number of credits available to the QPI Link 2 AD Ingress buffer.	0,1,2,3	0	null	0
HA	0x5A	0x0	UNC_H_IGR_CREDITS_BL_QPI2	Accumulates the number of credits available to the QPI Link 2 BL Ingress buffer.	0,1,2,3	0	null	0
HA	0x57	0x1	UNC_H_IODC_CONFLICTS.ANY	0	0,1,2,3	0	null	0
HA	0x57	0x4	UNC_H_IODC_CONFLICTS.LAST	0	0,1,2,3	0	null	0
HA	0x3E	0x33	UNC_H_RING_AD_USED.CW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0x3E	0xCC	UNC_H_RING_AD_USED.CCW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0x3F	0x33	UNC_H_RING_AK_USED.CW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0x3F	0xCC	UNC_H_RING_AK_USED.CCW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0x40	0x33	UNC_H_RING_BL_USED.CW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0x40	0xCC	UNC_H_RING_BL_USED.CCW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
HA	0xF	0x4	UNC_H_TxR_AD.HOM	Counts the number of outbound transactions on the AD ring.  This can be filtered by the NDR and SNP message classes.  See the filter descriptions for more details.; Filter for outbound NDR transactions sent on the AD ring.  NDR stands for non-data response and is generally used for completions that do not include data.  AD NDR is used for transactions to remote sockets.	0,1,2,3	0	null	0
iMC	0x1	0x1	UNC_M_ACT_COUNT.RD	Counts the number of DRAM Activate commands sent on this channel.  Activate commands are issued to open up a page on the DRAM devices so that it can be read or written to with a CAS.  One can calculate the number of Page Misses by subtracting the number of Page Miss precharges from the number of Activates.	0,1,2,3	0x0	null	0
iMC	0x1	0x2	UNC_M_ACT_COUNT.WR	Counts the number of DRAM Activate commands sent on this channel.  Activate commands are issued to open up a page on the DRAM devices so that it can be read or written to with a CAS.  One can calculate the number of Page Misses by subtracting the number of Page Miss precharges from the number of Activates.	0,1,2,3	0x0	null	0
iMC	0xa1	0x1	UNC_M_BYP_CMDS.ACT	0	0,1,2,3	0x0	null	0
iMC	0xa1	0x2	UNC_M_BYP_CMDS.CAS	0	0,1,2,3	0x0	null	0
iMC	0xa1	0x4	UNC_M_BYP_CMDS.PRE	0	0,1,2,3	0x0	null	0
iMC	0x4	0xF	UNC_M_CAS_COUNT.ALL	DRAM RD_CAS and WR_CAS Commands; Counts the total number of DRAM CAS commands issued on this channel.	0,1,2,3	0x0	null	0
iMC	0x4	0x3	UNC_M_CAS_COUNT.RD	DRAM RD_CAS and WR_CAS Commands; Counts the total number of DRAM Read CAS commands issued on this channel (including underfills).	0,1,2,3	0x0	null	0
iMC	0x4	0x1	UNC_M_CAS_COUNT.RD_REG	DRAM RD_CAS and WR_CAS Commands; Counts the total number or DRAM Read CAS commands issued on this channel.  This includes both regular RD CAS commands as well as those with implicit Precharge.  AutoPre is only used in systems that are using closed page policy.  We do not filter based on major mode, as RD_CAS is not issued during WMM (with the exception of underfills).	0,1,2,3	0x0	null	0
iMC	0x4	0x20	UNC_M_CAS_COUNT.RD_RMM	DRAM RD_CAS and WR_CAS Commands	0,1,2,3	0x0	null	0
iMC	0x4	0x2	UNC_M_CAS_COUNT.RD_UNDERFILL	DRAM RD_CAS and WR_CAS Commands; Counts the number of underfill reads that are issued by the memory controller.  This will generally be about the same as the number of partial writes, but may be slightly less because of partials hitting in the WPQ.  While it is possible for underfills to be issed in both WMM and RMM, this event counts both.	0,1,2,3	0x0	null	0
iMC	0x4	0x10	UNC_M_CAS_COUNT.RD_WMM	DRAM RD_CAS and WR_CAS Commands	0,1,2,3	0x0	null	0
iMC	0x4	0xC	UNC_M_CAS_COUNT.WR	DRAM RD_CAS and WR_CAS Commands; Counts the total number of DRAM Write CAS commands issued on this channel.	0,1,2,3	0x0	null	0
iMC	0x4	0x8	UNC_M_CAS_COUNT.WR_RMM	DRAM RD_CAS and WR_CAS Commands; Counts the total number of Opportunistic DRAM Write CAS commands issued on this channel while in Read-Major-Mode.	0,1,2,3	0x0	null	0
iMC	0x4	0x4	UNC_M_CAS_COUNT.WR_WMM	DRAM RD_CAS and WR_CAS Commands; Counts the total number or DRAM Write CAS commands issued on this channel while in Write-Major-Mode.	0,1,2,3	0x0	null	0
iMC	0x6	0x0	UNC_M_DRAM_PRE_ALL	Counts the number of times that the precharge all command was sent.	0,1,2,3	0x0	null	0
iMC	0x5	0x4	UNC_M_DRAM_REFRESH.HIGH	Counts the number of refreshes issued.	0,1,2,3	0x0	null	0
iMC	0x5	0x2	UNC_M_DRAM_REFRESH.PANIC	Counts the number of refreshes issued.	0,1,2,3	0x0	null	0
iMC	0x9	0x0	UNC_M_ECC_CORRECTABLE_ERRORS	Counts the number of ECC errors detected and corrected by the iMC on this channel.  This counter is only useful with ECC DRAM devices.  This count will increment one time for each correction regardless of the number of bits corrected.  The iMC can correct up to 4 bit errors in independent channel mode and 8 bit erros in lockstep mode.	0,1,2,3	0x0	null	0
iMC	0x7	0x8	UNC_M_MAJOR_MODES.ISOCH	Counts the total number of cycles spent in a major mode (selected by a filter) on the given channel.   Major modea are channel-wide, and not a per-rank (or dimm or bank) mode.; We group these two modes together so that we can use four counters to track each of the major modes at one time.  These major modes are used whenever there is an ISOCH txn in the memory controller.  In these mode, only ISOCH transactions are processed.	0,1,2,3	0x0	null	0
iMC	0x7	0x4	UNC_M_MAJOR_MODES.PARTIAL	Counts the total number of cycles spent in a major mode (selected by a filter) on the given channel.   Major modea are channel-wide, and not a per-rank (or dimm or bank) mode.; This major mode is used to drain starved underfill reads.  Regular reads and writes are blocked and only underfill reads will be processed.	0,1,2,3	0x0	null	0
iMC	0x7	0x1	UNC_M_MAJOR_MODES.READ	Counts the total number of cycles spent in a major mode (selected by a filter) on the given channel.   Major modea are channel-wide, and not a per-rank (or dimm or bank) mode.; Read Major Mode is the default mode for the iMC, as reads are generally more critical to forward progress than writes.	0,1,2,3	0x0	null	0
iMC	0x7	0x2	UNC_M_MAJOR_MODES.WRITE	Counts the total number of cycles spent in a major mode (selected by a filter) on the given channel.   Major modea are channel-wide, and not a per-rank (or dimm or bank) mode.; This mode is triggered when the WPQ hits high occupancy and causes writes to be higher priority than reads.  This can cause blips in the available read bandwidth in the system and temporarily increase read latencies in order to achieve better bus utilizations and higher bandwidth.	0,1,2,3	0x0	null	0
iMC	0x84	0x0	UNC_M_POWER_CHANNEL_DLLOFF	Number of cycles when all the ranks in the channel are in CKE Slow (DLLOFF) mode.	0,1,2,3	0x0	null	0
iMC	0x85	0x0	UNC_M_POWER_CHANNEL_PPD	Number of cycles when all the ranks in the channel are in PPD mode.  If IBT=off is enabled, then this can be used to count those cycles.  If it is not enabled, then this can count the number of cycles when that could have been taken advantage of.	0,1,2,3	0x0	null	0
iMC	0x83	0x1	UNC_M_POWER_CKE_CYCLES.RANK0	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x2	UNC_M_POWER_CKE_CYCLES.RANK1	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x4	UNC_M_POWER_CKE_CYCLES.RANK2	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x8	UNC_M_POWER_CKE_CYCLES.RANK3	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x10	UNC_M_POWER_CKE_CYCLES.RANK4	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x20	UNC_M_POWER_CKE_CYCLES.RANK5	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x40	UNC_M_POWER_CKE_CYCLES.RANK6	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x83	0x80	UNC_M_POWER_CKE_CYCLES.RANK7	Number of cycles spent in CKE ON mode.  The filter allows you to select a rank to monitor.  If multiple ranks are in CKE ON mode at one time, the counter will ONLY increment by one rather than doing accumulation.  Multiple counters will need to be used to track multiple ranks simultaneously.  There is no distinction between the different CKE modes (APD, PPDS, PPDF).  This can be determined based on the system programming.  These events should commonly be used with Invert to get the number of cycles in power saving mode.  Edge Detect is also useful here.  Make sure that you do NOT use Invert with Edge Detect (this just confuses the system and is not necessary).	0,1,2,3	0x0	null	0
iMC	0x86	0x0	UNC_M_POWER_CRITICAL_THROTTLE_CYCLES	Counts the number of cycles when the iMC is in critical thermal throttling.  When this happens, all traffic is blocked.  This should be rare unless something bad is going on in the platform.  There is no filtering by rank for this event.	0,1,2,3	0x0	null	0
iMC	0x43	0x0	UNC_M_POWER_SELF_REFRESH	Counts the number of cycles when the iMC is in self-refresh and the iMC still has a clock.  This happens in some package C-states.  For example, the PCU may ask the iMC to enter self-refresh even though some of the cores are still processing.  One use of this is for Monroe technology.  Self-refresh is required during package C3 and C6, but there is no clock in the iMC at this time, so it is not possible to count these cases.	0,1,2,3	0x0	null	0
iMC	0x41	0x1	UNC_M_POWER_THROTTLE_CYCLES.RANK0	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.; Thermal throttling is performed per DIMM.  We support 3 DIMMs per channel.  This ID allows us to filter by ID.	0,1,2,3	0x0	null	0
iMC	0x41	0x2	UNC_M_POWER_THROTTLE_CYCLES.RANK1	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x4	UNC_M_POWER_THROTTLE_CYCLES.RANK2	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x8	UNC_M_POWER_THROTTLE_CYCLES.RANK3	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x10	UNC_M_POWER_THROTTLE_CYCLES.RANK4	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x20	UNC_M_POWER_THROTTLE_CYCLES.RANK5	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x40	UNC_M_POWER_THROTTLE_CYCLES.RANK6	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x41	0x80	UNC_M_POWER_THROTTLE_CYCLES.RANK7	Counts the number of cycles while the iMC is being throttled by either thermal constraints or by the PCU throttling.  It is not possible to distinguish between the two.  This can be filtered by rank.  If multiple ranks are selected and are being throttled at the same time, the counter will only increment by 1.	0,1,2,3	0x0	null	0
iMC	0x8	0x1	UNC_M_PREEMPTION.RD_PREEMPT_RD	Counts the number of times a read in the iMC preempts another read or write.  Generally reads to an open page are issued ahead of requests to closed pages.  This improves the page hit rate of the system.  However, high priority requests can cause pages of active requests to be closed in order to get them out.  This will reduce the latency of the high-priority request at the expense of lower bandwidth and increased overall average latency.; Filter for when a read preempts another read.	0,1,2,3	0x0	null	0
iMC	0x8	0x2	UNC_M_PREEMPTION.RD_PREEMPT_WR	Counts the number of times a read in the iMC preempts another read or write.  Generally reads to an open page are issued ahead of requests to closed pages.  This improves the page hit rate of the system.  However, high priority requests can cause pages of active requests to be closed in order to get them out.  This will reduce the latency of the high-priority request at the expense of lower bandwidth and increased overall average latency.; Filter for when a read preempts a write.	0,1,2,3	0x0	null	0
iMC	0x2	0x2	UNC_M_PRE_COUNT.PAGE_CLOSE	Counts the number of DRAM Precharge commands sent on this channel.; Counts the number of DRAM Precharge commands sent on this channel as a result of the page close counter expiring.  This does not include implicit precharge commands sent in auto-precharge mode.	0,1,2,3	0x0	null	0
iMC	0x2	0x1	UNC_M_PRE_COUNT.PAGE_MISS	Counts the number of DRAM Precharge commands sent on this channel.; Counts the number of DRAM Precharge commands sent on this channel as a result of page misses.  This does not include explicit precharge commands sent with CAS commands in Auto-Precharge mode.  This does not include PRE commands sent as a result of the page close counter expiration.	0,1,2,3	0x0	null	0
iMC	0x2	0x4	UNC_M_PRE_COUNT.RD	Counts the number of DRAM Precharge commands sent on this channel.	0,1,2,3	0x0	null	0
iMC	0x2	0x8	UNC_M_PRE_COUNT.WR	Counts the number of DRAM Precharge commands sent on this channel.	0,1,2,3	0x0	null	0
iMC	0xa0	0x4	UNC_M_RD_CAS_PRIO.HIGH	0	0,1,2,3	0x0	null	0
iMC	0xa0	0x1	UNC_M_RD_CAS_PRIO.LOW	0	0,1,2,3	0x0	null	0
iMC	0xa0	0x2	UNC_M_RD_CAS_PRIO.MED	0	0,1,2,3	0x0	null	0
iMC	0xa0	0x8	UNC_M_RD_CAS_PRIO.PANIC	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x1	UNC_M_RD_CAS_RANK0.BANK0	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x2	UNC_M_RD_CAS_RANK0.BANK1	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x4	UNC_M_RD_CAS_RANK0.BANK2	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x8	UNC_M_RD_CAS_RANK0.BANK3	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x10	UNC_M_RD_CAS_RANK0.BANK4	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x20	UNC_M_RD_CAS_RANK0.BANK5	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x40	UNC_M_RD_CAS_RANK0.BANK6	0	0,1,2,3	0x0	null	0
iMC	0xb0	0x80	UNC_M_RD_CAS_RANK0.BANK7	0	0,1,2,3	0x0	null	0
iMC	0x11	0x0	UNC_M_RPQ_CYCLES_NE	Counts the number of cycles that the Read Pending Queue is not empty.  This can then be used to calculate the average occupancy (in conjunction with the Read Pending Queue Occupancy count).  The RPQ is used to schedule reads out to the memory controller and to track the requests.  Requests allocate into the RPQ soon after they enter the memory controller, and need credits for an entry in this buffer before being sent from the HA to the iMC.  They deallocate after the CAS command has been issued to memory.  This filter is to be used in conjunction with the occupancy filter so that one can correctly track the average occupancies for schedulable entries and scheduled requests.	0,1,2,3	0x0	null	0
iMC	0x10	0x0	UNC_M_RPQ_INSERTS	Counts the number of allocations into the Read Pending Queue.  This queue is used to schedule reads out to the memory controller and to track the requests.  Requests allocate into the RPQ soon after they enter the memory controller, and need credits for an entry in this buffer before being sent from the HA to the iMC.  They deallocate after the CAS command has been issued to memory.  This includes both ISOCH and non-ISOCH requests.	0,1,2,3	0x0	null	0
iMC	0x91	0x0	UNC_M_VMSE_MXB_WR_OCCUPANCY	0	0,1,2,3	0x0	null	0
iMC	0x90	0x2	UNC_M_VMSE_WR_PUSH.RMM	0	0,1,2,3	0x0	null	0
iMC	0x90	0x1	UNC_M_VMSE_WR_PUSH.WMM	0	0,1,2,3	0x0	null	0
iMC	0xc0	0x1	UNC_M_WMM_TO_RMM.LOW_THRESH	0	0,1,2,3	0x0	null	0
iMC	0xc0	0x2	UNC_M_WMM_TO_RMM.STARVE	0	0,1,2,3	0x0	null	0
iMC	0xc0	0x4	UNC_M_WMM_TO_RMM.VMSE_RETRY	0	0,1,2,3	0x0	null	0
iMC	0x22	0x0	UNC_M_WPQ_CYCLES_FULL	Counts the number of cycles when the Write Pending Queue is full.  When the WPQ is full, the HA will not be able to issue any additional read requests into the iMC.  This count should be similar count in the HA which tracks the number of cycles that the HA has no WPQ credits, just somewhat smaller to account for the credit return overhead.	0,1,2,3	0x0	null	0
iMC	0x21	0x0	UNC_M_WPQ_CYCLES_NE	Counts the number of cycles that the Write Pending Queue is not empty.  This can then be used to calculate the average queue occupancy (in conjunction with the WPQ Occupancy Accumulation count).  The WPQ is used to schedule write out to the memory controller and to track the writes.  Requests allocate into the WPQ soon after they enter the memory controller, and need credits for an entry in this buffer before being sent from the HA to the iMC.  They deallocate after being issued to DRAM.  Write requests themselves are able to complete (from the perspective of the rest of the system) as soon they have posted to the iMC.  This is not to be confused with actually performing the write to DRAM.  Therefore, the average latency for this queue is actually not useful for deconstruction intermediate write latencies.	0,1,2,3	0x0	null	0
iMC	0x20	0x0	UNC_M_WPQ_INSERTS	Counts the number of allocations into the Write Pending Queue.  This can then be used to calculate the average queuing latency (in conjunction with the WPQ occupancy count).  The WPQ is used to schedule write out to the memory controller and to track the writes.  Requests allocate into the WPQ soon after they enter the memory controller, and need credits for an entry in this buffer before being sent from the HA to the iMC.  They deallocate after being issued to DRAM.  Write requests themselves are able to complete (from the perspective of the rest of the system) as soon they have posted to the iMC.	0,1,2,3	0x0	null	0
iMC	0x23	0x0	UNC_M_WPQ_READ_HIT	Counts the number of times a request hits in the WPQ (write-pending queue).  The iMC allows writes and reads to pass up other writes to different addresses.  Before a read or a write is issued, it will first CAM the WPQ to see if there is a write pending to that address.  When reads hit, they are able to directly pull their data from the WPQ instead of going to memory.  Writes that hit will overwrite the existing data.  Partial writes that hit will not need to do underfill reads and will simply update their relevant sections.	0,1,2,3	0x0	null	0
iMC	0x24	0x0	UNC_M_WPQ_WRITE_HIT	Counts the number of times a request hits in the WPQ (write-pending queue).  The iMC allows writes and reads to pass up other writes to different addresses.  Before a read or a write is issued, it will first CAM the WPQ to see if there is a write pending to that address.  When reads hit, they are able to directly pull their data from the WPQ instead of going to memory.  Writes that hit will overwrite the existing data.  Partial writes that hit will not need to do underfill reads and will simply update their relevant sections.	0,1,2,3	0x0	null	0
iMC	0xc1	0x0	UNC_M_WRONG_MM	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x1	UNC_M_WR_CAS_RANK0.BANK0	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x2	UNC_M_WR_CAS_RANK0.BANK1	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x4	UNC_M_WR_CAS_RANK0.BANK2	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x8	UNC_M_WR_CAS_RANK0.BANK3	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x10	UNC_M_WR_CAS_RANK0.BANK4	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x20	UNC_M_WR_CAS_RANK0.BANK5	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x40	UNC_M_WR_CAS_RANK0.BANK6	0	0,1,2,3	0x0	null	0
iMC	0xb8	0x80	UNC_M_WR_CAS_RANK0.BANK7	0	0,1,2,3	0x0	null	0
iMC	0x1	0x8	UNC_M_ACT_COUNT.BYP	Counts the number of DRAM Activate commands sent on this channel.  Activate commands are issued to open up a page on the DRAM devices so that it can be read or written to with a CAS.  One can calculate the number of Page Misses by subtracting the number of Page Miss precharges from the number of Activates.	0,1,2,3	0	null	0
iMC	0x0	0x0	UNC_M_DCLOCKTICKS	0	0,1,2,3	0	null	0
iMC	0x42	0x0	UNC_M_POWER_PCU_THROTTLING	0	0,1,2,3	0	null	0
iMC	0x2	0x10	UNC_M_PRE_COUNT.BYP	Counts the number of DRAM Precharge commands sent on this channel.	0,1,2,3	0	null	0
iMC	0xB1	0x1	UNC_M_RD_CAS_RANK1.BANK0	0	0,1,2,3	0	null	0
iMC	0xB1	0x2	UNC_M_RD_CAS_RANK1.BANK1	0	0,1,2,3	0	null	0
iMC	0xB1	0x4	UNC_M_RD_CAS_RANK1.BANK2	0	0,1,2,3	0	null	0
iMC	0xB1	0x8	UNC_M_RD_CAS_RANK1.BANK3	0	0,1,2,3	0	null	0
iMC	0xB1	0x10	UNC_M_RD_CAS_RANK1.BANK4	0	0,1,2,3	0	null	0
iMC	0xB1	0x20	UNC_M_RD_CAS_RANK1.BANK5	0	0,1,2,3	0	null	0
iMC	0xB1	0x40	UNC_M_RD_CAS_RANK1.BANK6	0	0,1,2,3	0	null	0
iMC	0xB1	0x80	UNC_M_RD_CAS_RANK1.BANK7	0	0,1,2,3	0	null	0
iMC	0xB2	0x1	UNC_M_RD_CAS_RANK2.BANK0	0	0,1,2,3	0	null	0
iMC	0xB2	0x2	UNC_M_RD_CAS_RANK2.BANK1	0	0,1,2,3	0	null	0
iMC	0xB2	0x4	UNC_M_RD_CAS_RANK2.BANK2	0	0,1,2,3	0	null	0
iMC	0xB2	0x8	UNC_M_RD_CAS_RANK2.BANK3	0	0,1,2,3	0	null	0
iMC	0xB2	0x10	UNC_M_RD_CAS_RANK2.BANK4	0	0,1,2,3	0	null	0
iMC	0xB2	0x20	UNC_M_RD_CAS_RANK2.BANK5	0	0,1,2,3	0	null	0
iMC	0xB2	0x40	UNC_M_RD_CAS_RANK2.BANK6	0	0,1,2,3	0	null	0
iMC	0xB2	0x80	UNC_M_RD_CAS_RANK2.BANK7	0	0,1,2,3	0	null	0
iMC	0xB3	0x1	UNC_M_RD_CAS_RANK3.BANK0	0	0,1,2,3	0	null	0
iMC	0xB3	0x2	UNC_M_RD_CAS_RANK3.BANK1	0	0,1,2,3	0	null	0
iMC	0xB3	0x4	UNC_M_RD_CAS_RANK3.BANK2	0	0,1,2,3	0	null	0
iMC	0xB3	0x8	UNC_M_RD_CAS_RANK3.BANK3	0	0,1,2,3	0	null	0
iMC	0xB3	0x10	UNC_M_RD_CAS_RANK3.BANK4	0	0,1,2,3	0	null	0
iMC	0xB3	0x20	UNC_M_RD_CAS_RANK3.BANK5	0	0,1,2,3	0	null	0
iMC	0xB3	0x40	UNC_M_RD_CAS_RANK3.BANK6	0	0,1,2,3	0	null	0
iMC	0xB3	0x80	UNC_M_RD_CAS_RANK3.BANK7	0	0,1,2,3	0	null	0
iMC	0xB4	0x1	UNC_M_RD_CAS_RANK4.BANK0	0	0,1,2,3	0	null	0
iMC	0xB4	0x2	UNC_M_RD_CAS_RANK4.BANK1	0	0,1,2,3	0	null	0
iMC	0xB4	0x4	UNC_M_RD_CAS_RANK4.BANK2	0	0,1,2,3	0	null	0
iMC	0xB4	0x8	UNC_M_RD_CAS_RANK4.BANK3	0	0,1,2,3	0	null	0
iMC	0xB4	0x10	UNC_M_RD_CAS_RANK4.BANK4	0	0,1,2,3	0	null	0
iMC	0xB4	0x20	UNC_M_RD_CAS_RANK4.BANK5	0	0,1,2,3	0	null	0
iMC	0xB4	0x40	UNC_M_RD_CAS_RANK4.BANK6	0	0,1,2,3	0	null	0
iMC	0xB4	0x80	UNC_M_RD_CAS_RANK4.BANK7	0	0,1,2,3	0	null	0
iMC	0xB5	0x1	UNC_M_RD_CAS_RANK5.BANK0	0	0,1,2,3	0	null	0
iMC	0xB5	0x2	UNC_M_RD_CAS_RANK5.BANK1	0	0,1,2,3	0	null	0
iMC	0xB5	0x4	UNC_M_RD_CAS_RANK5.BANK2	0	0,1,2,3	0	null	0
iMC	0xB5	0x8	UNC_M_RD_CAS_RANK5.BANK3	0	0,1,2,3	0	null	0
iMC	0xB5	0x10	UNC_M_RD_CAS_RANK5.BANK4	0	0,1,2,3	0	null	0
iMC	0xB5	0x20	UNC_M_RD_CAS_RANK5.BANK5	0	0,1,2,3	0	null	0
iMC	0xB5	0x40	UNC_M_RD_CAS_RANK5.BANK6	0	0,1,2,3	0	null	0
iMC	0xB5	0x80	UNC_M_RD_CAS_RANK5.BANK7	0	0,1,2,3	0	null	0
iMC	0xB6	0x1	UNC_M_RD_CAS_RANK6.BANK0	0	0,1,2,3	0	null	0
iMC	0xB6	0x2	UNC_M_RD_CAS_RANK6.BANK1	0	0,1,2,3	0	null	0
iMC	0xB6	0x4	UNC_M_RD_CAS_RANK6.BANK2	0	0,1,2,3	0	null	0
iMC	0xB6	0x8	UNC_M_RD_CAS_RANK6.BANK3	0	0,1,2,3	0	null	0
iMC	0xB6	0x10	UNC_M_RD_CAS_RANK6.BANK4	0	0,1,2,3	0	null	0
iMC	0xB6	0x20	UNC_M_RD_CAS_RANK6.BANK5	0	0,1,2,3	0	null	0
iMC	0xB6	0x40	UNC_M_RD_CAS_RANK6.BANK6	0	0,1,2,3	0	null	0
iMC	0xB6	0x80	UNC_M_RD_CAS_RANK6.BANK7	0	0,1,2,3	0	null	0
iMC	0xB7	0x1	UNC_M_RD_CAS_RANK7.BANK0	0	0,1,2,3	0	null	0
iMC	0xB7	0x2	UNC_M_RD_CAS_RANK7.BANK1	0	0,1,2,3	0	null	0
iMC	0xB7	0x4	UNC_M_RD_CAS_RANK7.BANK2	0	0,1,2,3	0	null	0
iMC	0xB7	0x8	UNC_M_RD_CAS_RANK7.BANK3	0	0,1,2,3	0	null	0
iMC	0xB7	0x10	UNC_M_RD_CAS_RANK7.BANK4	0	0,1,2,3	0	null	0
iMC	0xB7	0x20	UNC_M_RD_CAS_RANK7.BANK5	0	0,1,2,3	0	null	0
iMC	0xB7	0x40	UNC_M_RD_CAS_RANK7.BANK6	0	0,1,2,3	0	null	0
iMC	0xB7	0x80	UNC_M_RD_CAS_RANK7.BANK7	0	0,1,2,3	0	null	0
iMC	0xB9	0x1	UNC_M_WR_CAS_RANK1.BANK0	0	0,1,2,3	0	null	0
iMC	0xB9	0x2	UNC_M_WR_CAS_RANK1.BANK1	0	0,1,2,3	0	null	0
iMC	0xB9	0x4	UNC_M_WR_CAS_RANK1.BANK2	0	0,1,2,3	0	null	0
iMC	0xB9	0x8	UNC_M_WR_CAS_RANK1.BANK3	0	0,1,2,3	0	null	0
iMC	0xB9	0x10	UNC_M_WR_CAS_RANK1.BANK4	0	0,1,2,3	0	null	0
iMC	0xB9	0x20	UNC_M_WR_CAS_RANK1.BANK5	0	0,1,2,3	0	null	0
iMC	0xB9	0x40	UNC_M_WR_CAS_RANK1.BANK6	0	0,1,2,3	0	null	0
iMC	0xB9	0x80	UNC_M_WR_CAS_RANK1.BANK7	0	0,1,2,3	0	null	0
iMC	0xBA	0x1	UNC_M_WR_CAS_RANK2.BANK0	0	0,1,2,3	0	null	0
iMC	0xBA	0x2	UNC_M_WR_CAS_RANK2.BANK1	0	0,1,2,3	0	null	0
iMC	0xBA	0x4	UNC_M_WR_CAS_RANK2.BANK2	0	0,1,2,3	0	null	0
iMC	0xBA	0x8	UNC_M_WR_CAS_RANK2.BANK3	0	0,1,2,3	0	null	0
iMC	0xBA	0x10	UNC_M_WR_CAS_RANK2.BANK4	0	0,1,2,3	0	null	0
iMC	0xBA	0x20	UNC_M_WR_CAS_RANK2.BANK5	0	0,1,2,3	0	null	0
iMC	0xBA	0x40	UNC_M_WR_CAS_RANK2.BANK6	0	0,1,2,3	0	null	0
iMC	0xBA	0x80	UNC_M_WR_CAS_RANK2.BANK7	0	0,1,2,3	0	null	0
iMC	0xBB	0x1	UNC_M_WR_CAS_RANK3.BANK0	0	0,1,2,3	0	null	0
iMC	0xBB	0x2	UNC_M_WR_CAS_RANK3.BANK1	0	0,1,2,3	0	null	0
iMC	0xBB	0x4	UNC_M_WR_CAS_RANK3.BANK2	0	0,1,2,3	0	null	0
iMC	0xBB	0x8	UNC_M_WR_CAS_RANK3.BANK3	0	0,1,2,3	0	null	0
iMC	0xBB	0x10	UNC_M_WR_CAS_RANK3.BANK4	0	0,1,2,3	0	null	0
iMC	0xBB	0x20	UNC_M_WR_CAS_RANK3.BANK5	0	0,1,2,3	0	null	0
iMC	0xBB	0x40	UNC_M_WR_CAS_RANK3.BANK6	0	0,1,2,3	0	null	0
iMC	0xBB	0x80	UNC_M_WR_CAS_RANK3.BANK7	0	0,1,2,3	0	null	0
iMC	0xBC	0x1	UNC_M_WR_CAS_RANK4.BANK0	0	0,1,2,3	0	null	0
iMC	0xBC	0x2	UNC_M_WR_CAS_RANK4.BANK1	0	0,1,2,3	0	null	0
iMC	0xBC	0x4	UNC_M_WR_CAS_RANK4.BANK2	0	0,1,2,3	0	null	0
iMC	0xBC	0x8	UNC_M_WR_CAS_RANK4.BANK3	0	0,1,2,3	0	null	0
iMC	0xBC	0x10	UNC_M_WR_CAS_RANK4.BANK4	0	0,1,2,3	0	null	0
iMC	0xBC	0x20	UNC_M_WR_CAS_RANK4.BANK5	0	0,1,2,3	0	null	0
iMC	0xBC	0x40	UNC_M_WR_CAS_RANK4.BANK6	0	0,1,2,3	0	null	0
iMC	0xBC	0x80	UNC_M_WR_CAS_RANK4.BANK7	0	0,1,2,3	0	null	0
iMC	0xBD	0x1	UNC_M_WR_CAS_RANK5.BANK0	0	0,1,2,3	0	null	0
iMC	0xBD	0x2	UNC_M_WR_CAS_RANK5.BANK1	0	0,1,2,3	0	null	0
iMC	0xBD	0x4	UNC_M_WR_CAS_RANK5.BANK2	0	0,1,2,3	0	null	0
iMC	0xBD	0x8	UNC_M_WR_CAS_RANK5.BANK3	0	0,1,2,3	0	null	0
iMC	0xBD	0x10	UNC_M_WR_CAS_RANK5.BANK4	0	0,1,2,3	0	null	0
iMC	0xBD	0x20	UNC_M_WR_CAS_RANK5.BANK5	0	0,1,2,3	0	null	0
iMC	0xBD	0x40	UNC_M_WR_CAS_RANK5.BANK6	0	0,1,2,3	0	null	0
iMC	0xBD	0x80	UNC_M_WR_CAS_RANK5.BANK7	0	0,1,2,3	0	null	0
iMC	0xBE	0x1	UNC_M_WR_CAS_RANK6.BANK0	0	0,1,2,3	0	null	0
iMC	0xBE	0x2	UNC_M_WR_CAS_RANK6.BANK1	0	0,1,2,3	0	null	0
iMC	0xBE	0x4	UNC_M_WR_CAS_RANK6.BANK2	0	0,1,2,3	0	null	0
iMC	0xBE	0x8	UNC_M_WR_CAS_RANK6.BANK3	0	0,1,2,3	0	null	0
iMC	0xBE	0x10	UNC_M_WR_CAS_RANK6.BANK4	0	0,1,2,3	0	null	0
iMC	0xBE	0x20	UNC_M_WR_CAS_RANK6.BANK5	0	0,1,2,3	0	null	0
iMC	0xBE	0x40	UNC_M_WR_CAS_RANK6.BANK6	0	0,1,2,3	0	null	0
iMC	0xBE	0x80	UNC_M_WR_CAS_RANK6.BANK7	0	0,1,2,3	0	null	0
iMC	0xBF	0x1	UNC_M_WR_CAS_RANK7.BANK0	0	0,1,2,3	0	null	0
iMC	0xBF	0x2	UNC_M_WR_CAS_RANK7.BANK1	0	0,1,2,3	0	null	0
iMC	0xBF	0x4	UNC_M_WR_CAS_RANK7.BANK2	0	0,1,2,3	0	null	0
iMC	0xBF	0x8	UNC_M_WR_CAS_RANK7.BANK3	0	0,1,2,3	0	null	0
iMC	0xBF	0x10	UNC_M_WR_CAS_RANK7.BANK4	0	0,1,2,3	0	null	0
iMC	0xBF	0x20	UNC_M_WR_CAS_RANK7.BANK5	0	0,1,2,3	0	null	0
iMC	0xBF	0x40	UNC_M_WR_CAS_RANK7.BANK6	0	0,1,2,3	0	null	0
iMC	0xBF	0x80	UNC_M_WR_CAS_RANK7.BANK7	0	0,1,2,3	0	null	0
IRP	0x17	0x2	UNC_I_ADDRESS_MATCH.MERGE_COUNT	Counts the number of times when an inbound write (from a device to memory or another device) had an address match with another request in the write cache.; When two requests to the same address from the same source are received back to back, it is possible to merge the two of them together.	0,1	0x0	null	0
IRP	0x17	0x1	UNC_I_ADDRESS_MATCH.STALL_COUNT	Counts the number of times when an inbound write (from a device to memory or another device) had an address match with another request in the write cache.; When it is not possible to merge two conflicting requests, a stall event occurs.  This is bad for performance.	0,1	0x0	null	0
IRP	0x14	0x1	UNC_I_CACHE_ACK_PENDING_OCCUPANCY.ANY	Accumulates the number of writes that have acquired ownership but have not yet returned their data to the uncore.  These writes are generally queued up in the switch trying to get to the head of their queues so that they can post their data.  The queue occuapancy increments when the ACK is received, and decrements when either the data is returned OR a tickle is received and ownership is released.  Note that a single tickle can result in multiple decrements.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.	0,1	0x0	null	0
IRP	0x14	0x2	UNC_I_CACHE_ACK_PENDING_OCCUPANCY.SOURCE	Accumulates the number of writes that have acquired ownership but have not yet returned their data to the uncore.  These writes are generally queued up in the switch trying to get to the head of their queues so that they can post their data.  The queue occuapancy increments when the ACK is received, and decrements when either the data is returned OR a tickle is received and ownership is released.  Note that a single tickle can result in multiple decrements.; Tracks all requests from any source port.	0,1	0x0	null	0
IRP	0x13	0x1	UNC_I_CACHE_OWN_OCCUPANCY.ANY	Accumulates the number of writes (and write prefetches) that are outstanding in the uncore trying to acquire ownership in each cycle.  This can be used with the write transaction count to calculate the average write latency in the uncore.  The occupancy increments when a write request is issued, and decrements when the data is returned.; Tracks all requests from any source port.	0,1	0x0	null	0
IRP	0x13	0x2	UNC_I_CACHE_OWN_OCCUPANCY.SOURCE	Accumulates the number of writes (and write prefetches) that are outstanding in the uncore trying to acquire ownership in each cycle.  This can be used with the write transaction count to calculate the average write latency in the uncore.  The occupancy increments when a write request is issued, and decrements when the data is returned.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.	0,1	0x0	null	0
IRP	0x10	0x1	UNC_I_CACHE_READ_OCCUPANCY.ANY	Accumulates the number of reads that are outstanding in the uncore in each cycle.  This can be used with the read transaction count to calculate the average read latency in the uncore.  The occupancy increments when a read request is issued, and decrements when the data is returned.; Tracks all requests from any source port.	0,1	0x0	null	0
IRP	0x10	0x2	UNC_I_CACHE_READ_OCCUPANCY.SOURCE	Accumulates the number of reads that are outstanding in the uncore in each cycle.  This can be used with the read transaction count to calculate the average read latency in the uncore.  The occupancy increments when a read request is issued, and decrements when the data is returned.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.	0,1	0x0	null	0
IRP	0x12	0x1	UNC_I_CACHE_TOTAL_OCCUPANCY.ANY	Accumulates the number of reads and writes that are outstanding in the uncore in each cycle.  This is effectively the sum of the READ_OCCUPANCY and WRITE_OCCUPANCY events.; Tracks all requests from any source port.	0,1	0x0	null	0
IRP	0x12	0x2	UNC_I_CACHE_TOTAL_OCCUPANCY.SOURCE	Accumulates the number of reads and writes that are outstanding in the uncore in each cycle.  This is effectively the sum of the READ_OCCUPANCY and WRITE_OCCUPANCY events.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.	0,1	0x0	null	0
IRP	0x11	0x1	UNC_I_CACHE_WRITE_OCCUPANCY.ANY	Accumulates the number of writes (and write prefetches)  that are outstanding in the uncore in each cycle.  This can be used with the transaction count event to calculate the average latency in the uncore.  The occupancy increments when the ownership fetch/prefetch is issued, and decrements the data is returned to the uncore.; Tracks all requests from any source port.	0,1	0x0	null	0
IRP	0x11	0x2	UNC_I_CACHE_WRITE_OCCUPANCY.SOURCE	Accumulates the number of writes (and write prefetches)  that are outstanding in the uncore in each cycle.  This can be used with the transaction count event to calculate the average latency in the uncore.  The occupancy increments when the ownership fetch/prefetch is issued, and decrements the data is returned to the uncore.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.	0,1	0x0	null	0
IRP	0x0	0x0	UNC_I_CLOCKTICKS	Number of clocks in the IRP.	0,1	0x0	null	0
IRP	0xb	0x0	UNC_I_RxR_AK_CYCLES_FULL	Counts the number of cycles when the AK Ingress is full.  This queue is where the IRP receives responses from R2PCIe (the ring).	0,1	0x0	null	0
IRP	0xa	0x0	UNC_I_RxR_AK_INSERTS	Counts the number of allocations into the AK Ingress.  This queue is where the IRP receives responses from R2PCIe (the ring).	0,1	0x0	null	0
IRP	0xc	0x0	UNC_I_RxR_AK_OCCUPANCY	Accumulates the occupancy of the AK Ingress in each cycles.  This queue is where the IRP receives responses from R2PCIe (the ring).	0,1	0x0	null	0
IRP	0x4	0x0	UNC_I_RxR_BL_DRS_CYCLES_FULL	Counts the number of cycles when the BL Ingress is full.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x1	0x0	UNC_I_RxR_BL_DRS_INSERTS	Counts the number of allocations into the BL Ingress.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x7	0x0	UNC_I_RxR_BL_DRS_OCCUPANCY	Accumulates the occupancy of the BL Ingress in each cycles.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x5	0x0	UNC_I_RxR_BL_NCB_CYCLES_FULL	Counts the number of cycles when the BL Ingress is full.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x2	0x0	UNC_I_RxR_BL_NCB_INSERTS	Counts the number of allocations into the BL Ingress.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x8	0x0	UNC_I_RxR_BL_NCB_OCCUPANCY	Accumulates the occupancy of the BL Ingress in each cycles.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x6	0x0	UNC_I_RxR_BL_NCS_CYCLES_FULL	Counts the number of cycles when the BL Ingress is full.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x3	0x0	UNC_I_RxR_BL_NCS_INSERTS	Counts the number of allocations into the BL Ingress.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x9	0x0	UNC_I_RxR_BL_NCS_OCCUPANCY	Accumulates the occupancy of the BL Ingress in each cycles.  This queue is where the IRP receives data from R2PCIe (the ring).  It is used for data returns from read requets as well as outbound MMIO writes.	0,1	0x0	null	0
IRP	0x16	0x1	UNC_I_TICKLES.LOST_OWNERSHIP	Counts the number of tickles that are received.  This is for both explicit (from Cbo) and implicit (internal conflict) tickles.; Tracks the number of requests that lost ownership as a result of a tickle.  When a tickle comes in, if the request is not at the head of the queue in the switch, then that request as well as any requests behind it in the switch queue will lose ownership and have to re-acquire it later when they get to the head of the queue.  This will therefore track the number of requests that lost ownership and not just the number of tickles.	0,1	0x0	null	0
IRP	0x16	0x2	UNC_I_TICKLES.TOP_OF_QUEUE	Counts the number of tickles that are received.  This is for both explicit (from Cbo) and implicit (internal conflict) tickles.; Tracks the number of cases when a tickle was received but the requests was at the head of the queue in the switch.  In this case, data is returned rather than releasing ownership.	0,1	0x0	null	0
IRP	0x15	0x8	UNC_I_TRANSACTIONS.ORDERINGQ	Counts the number of Inbound transactions from the IRP to the Uncore.  This can be filtered based on request type in addition to the source queue.  Note the special filtering equation.  We do OR-reduction on the request type.  If the SOURCE bit is set, then we also do AND qualification based on the source portID.; Tracks only those requests that come from the port specified in the IRP_PmonFilter.OrderingQ register.  This register allows one to select one specific queue.  It is not possible to monitor multiple queues at a time.  If this bit is not set, then requests from all sources will be counted.	0,1	0x0	IRPFilter[4:0]	0
IRP	0x15	0x4	UNC_I_TRANSACTIONS.PD_PREFETCHES	Counts the number of \Inbound\ transactions from the IRP to the Uncore.  This can be filtered based on request type in addition to the source queue.  Note the special filtering equation.  We do OR-reduction on the request type.  If the SOURCE bit is set, then we also do AND qualification based on the source portID.	0,1	0x0	null	0
IRP	0x15	0x1	UNC_I_TRANSACTIONS.READS	Counts the number of Inbound transactions from the IRP to the Uncore.  This can be filtered based on request type in addition to the source queue.  Note the special filtering equation.  We do OR-reduction on the request type.  If the SOURCE bit is set, then we also do AND qualification based on the source portID.; Tracks only read requests (not including read prefetches).	0,1	0x0	null	0
IRP	0x15	0x2	UNC_I_TRANSACTIONS.WRITES	Counts the number of Inbound transactions from the IRP to the Uncore.  This can be filtered based on request type in addition to the source queue.  Note the special filtering equation.  We do OR-reduction on the request type.  If the SOURCE bit is set, then we also do AND qualification based on the source portID.; Trackes only write requests.  Each write request should have a prefetch, so there is no need to explicitly track these requests.  For writes that are tickled and have to retry, the counter will be incremented for each retry.	0,1	0x0	null	0
IRP	0x18	0x0	UNC_I_TxR_AD_STALL_CREDIT_CYCLES	Counts the number times when it is not possible to issue a request to the R2PCIe because there are no AD Egress Credits available.	0,1	0x0	null	0
IRP	0x19	0x0	UNC_I_TxR_BL_STALL_CREDIT_CYCLES	Counts the number times when it is not possible to issue data to the R2PCIe because there are no BL Egress Credits available.	0,1	0x0	null	0
IRP	0xe	0x0	UNC_I_TxR_DATA_INSERTS_NCB	Counts the number of requests issued to the switch (towards the devices).	0,1	0x0	null	0
IRP	0xf	0x0	UNC_I_TxR_DATA_INSERTS_NCS	Counts the number of requests issued to the switch (towards the devices).	0,1	0x0	null	0
IRP	0xd	0x0	UNC_I_TxR_REQUEST_OCCUPANCY	Accumultes the number of outstanding outbound requests from the IRP to the switch (towards the devices).  This can be used in conjuection with the allocations event in order to calculate average latency of outbound requests.	0,1	0x0	null	0
IRP	0x1a	0x0	UNC_I_WRITE_ORDERING_STALL_CYCLES	Counts the number of cycles when there are pending write ACK's in the switch but the switch->IRP pipeline is not utilized.	0,1	0x0	null	0
IRP	0x15	0x4	UNC_I_TRANSACTIONS.RD_PREFETCHES	Counts the number of Inbound transactions from the IRP to the Uncore.  This can be filtered based on request type in addition to the source queue.  Note the special filtering equation.  We do OR-reduction on the request type.  If the SOURCE bit is set, then we also do AND qualification based on the source portID.; Tracks the number of read prefetches.	0,1	0	null	0
PCU	0x0	0x0	UNC_P_CLOCKTICKS	The PCU runs off a fixed 800 MHz clock.  This event counts the number of pclk cycles measured while the counter was enabled.  The pclk, like the Memory Controller's dclk, counts at a constant rate making it a good measure of actual wall time.	0,1,2,3	0x0	null	0
PCU	0x70	0x0	UNC_P_CORE0_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x7a	0x0	UNC_P_CORE10_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x7b	0x0	UNC_P_CORE11_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x7c	0x0	UNC_P_CORE12_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x7d	0x0	UNC_P_CORE13_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x7e	0x0	UNC_P_CORE14_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x71	0x0	UNC_P_CORE1_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x72	0x0	UNC_P_CORE2_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x73	0x0	UNC_P_CORE3_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x74	0x0	UNC_P_CORE4_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x75	0x0	UNC_P_CORE5_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x76	0x0	UNC_P_CORE6_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x77	0x0	UNC_P_CORE7_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x78	0x0	UNC_P_CORE8_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x79	0x0	UNC_P_CORE9_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions.  There is one event per core.	0,1,2,3	0x0	null	0
PCU	0x17	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE0	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x18	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE1	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x21	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE10	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x22	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE11	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x23	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE12	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x24	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE13	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x25	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE14	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x19	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE2	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1a	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE3	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1b	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE4	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1c	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE5	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1d	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE6	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1e	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE7	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1f	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE8	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x20	0x0	UNC_P_DELAYED_C_STATE_ABORT_CORE9	Number of times that a deep C state was requested, but the delayed C state algorithm rejected the deep sleep state.  In other words, a wake event occurred before the timer expired that causes a transition into the deeper C state.	0,1,2,3	0x0	null	1
PCU	0x1e	0x0	UNC_P_DEMOTIONS_CORE0	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x1f	0x0	UNC_P_DEMOTIONS_CORE1	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x20	0x0	UNC_P_DEMOTIONS_CORE2	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x21	0x0	UNC_P_DEMOTIONS_CORE3	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x22	0x0	UNC_P_DEMOTIONS_CORE4	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x23	0x0	UNC_P_DEMOTIONS_CORE5	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x24	0x0	UNC_P_DEMOTIONS_CORE6	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0x25	0x0	UNC_P_DEMOTIONS_CORE7	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0xb	0x0	UNC_P_FREQ_BAND0_CYCLES	Counts the number of cycles that the uncore was running at a frequency greater than or equal to the frequency that is configured in the filter.  One can use all four counters with this event, so it is possible to track up to 4 configurable bands.  One can use edge detect in conjunction with this event to track the number of times that we transitioned into a frequency greater than or equal to the configurable frequency. One can also use inversion to track cycles when we were less than the configured frequency.	0,1,2,3	0x0	PCUFilter[7:0]	0
PCU	0xc	0x0	UNC_P_FREQ_BAND1_CYCLES	Counts the number of cycles that the uncore was running at a frequency greater than or equal to the frequency that is configured in the filter.  One can use all four counters with this event, so it is possible to track up to 4 configurable bands.  One can use edge detect in conjunction with this event to track the number of times that we transitioned into a frequency greater than or equal to the configurable frequency. One can also use inversion to track cycles when we were less than the configured frequency.	0,1,2,3	0x0	PCUFilter[15:8]	0
PCU	0xd	0x0	UNC_P_FREQ_BAND2_CYCLES	Counts the number of cycles that the uncore was running at a frequency greater than or equal to the frequency that is configured in the filter.  One can use all four counters with this event, so it is possible to track up to 4 configurable bands.  One can use edge detect in conjunction with this event to track the number of times that we transitioned into a frequency greater than or equal to the configurable frequency. One can also use inversion to track cycles when we were less than the configured frequency.	0,1,2,3	0x0	PCUFilter[23:16]	0
PCU	0xe	0x0	UNC_P_FREQ_BAND3_CYCLES	Counts the number of cycles that the uncore was running at a frequency greater than or equal to the frequency that is configured in the filter.  One can use all four counters with this event, so it is possible to track up to 4 configurable bands.  One can use edge detect in conjunction with this event to track the number of times that we transitioned into a frequency greater than or equal to the configurable frequency. One can also use inversion to track cycles when we were less than the configured frequency.	0,1,2,3	0x0	PCUFilter[31:24]	0
PCU	0x7	0x0	UNC_P_FREQ_MAX_CURRENT_CYCLES	Counts the number of cycles when current is the upper limit on frequency.	0,1,2,3	0x0	null	0
PCU	0x4	0x0	UNC_P_FREQ_MAX_LIMIT_THERMAL_CYCLES	Counts the number of cycles when thermal conditions are the upper limit on frequency.  This is related to the THERMAL_THROTTLE CYCLES_ABOVE_TEMP event, which always counts cycles when we are above the thermal temperature.  This event (STRONGEST_UPPER_LIMIT) is sampled at the output of the algorithm that determines the actual frequency, while THERMAL_THROTTLE looks at the input.	0,1,2,3	0x0	null	0
PCU	0x6	0x0	UNC_P_FREQ_MAX_OS_CYCLES	Counts the number of cycles when the OS is the upper limit on frequency.	0,1,2,3	0x0	null	0
PCU	0x5	0x0	UNC_P_FREQ_MAX_POWER_CYCLES	Counts the number of cycles when power is the upper limit on frequency.	0,1,2,3	0x0	null	0
PCU	0x61	0x0	UNC_P_FREQ_MIN_IO_P_CYCLES	Counts the number of cycles when IO P Limit is preventing us from dropping the frequency lower.  This algorithm monitors the needs to the IO subsystem on both local and remote sockets and will maintain a frequency high enough to maintain good IO BW.  This is necessary for when all the IA cores on a socket are idle but a user still would like to maintain high IO Bandwidth.	0,1,2,3	0x0	null	0
PCU	0x62	0x0	UNC_P_FREQ_MIN_PERF_P_CYCLES	Counts the number of cycles when Perf P Limit is preventing us from dropping the frequency lower.  Perf P Limit is an algorithm that takes input from remote sockets when determining if a socket should drop it's frequency down.  This is largely to minimize increases in snoop and remote read latencies.	0,1,2,3	0x0	null	0
PCU	0x60	0x0	UNC_P_FREQ_TRANS_CYCLES	Counts the number of cycles when the system is changing frequency.  This can not be filtered by thread ID.  One can also use it with the occupancy counter that monitors number of threads in C0 to estimate the performance impact that frequency transitions had on the system.	0,1,2,3	0x0	null	0
PCU	0x2f	0x0	UNC_P_MEMORY_PHASE_SHEDDING_CYCLES	Counts the number of cycles that the PCU has triggered memory phase shedding.  This is a mode that can be run in the iMC physicals that saves power at the expense of additional latency.	0,1,2,3	0x0	null	0
PCU	0x26	0x0	UNC_P_PKG_C_EXIT_LATENCY_SEL	Counts the number of cycles that the package is transitioning from package C2 to C3.	0,1,2,3	0x0	null	1
PCU	0x2a	0x0	UNC_P_PKG_C_STATE_RESIDENCY_C0_CYCLES	Counts the number of cycles that the package is in C0	0,1,2,3	0x0	null	1
PCU	0x2b	0x0	UNC_P_PKG_C_STATE_RESIDENCY_C2_CYCLES	Counts the number of cycles that the package is in C2	0,1,2,3	0x0	null	1
PCU	0x2c	0x0	UNC_P_PKG_C_STATE_RESIDENCY_C3_CYCLES	Counts the number of cycles that the package is in C3	0,1,2,3	0x0	null	1
PCU	0x2d	0x0	UNC_P_PKG_C_STATE_RESIDENCY_C6_CYCLES	Counts the number of cycles that the package is in C6	0,1,2,3	0x0	null	1
PCU	0x80	0x40	UNC_P_POWER_STATE_OCCUPANCY.CORES_C0	This is an occupancy event that tracks the number of cores that are in the chosen C-State.  It can be used by itself to get the average number of cores in that C-state with threshholding to generate histograms, or with other PCU events and occupancy triggering to capture other details.	0,1,2,3	0x0	null	0
PCU	0x80	0x80	UNC_P_POWER_STATE_OCCUPANCY.CORES_C3	This is an occupancy event that tracks the number of cores that are in the chosen C-State.  It can be used by itself to get the average number of cores in that C-state with threshholding to generate histograms, or with other PCU events and occupancy triggering to capture other details.	0,1,2,3	0x0	null	0
PCU	0x80	0xC0	UNC_P_POWER_STATE_OCCUPANCY.CORES_C6	This is an occupancy event that tracks the number of cores that are in the chosen C-State.  It can be used by itself to get the average number of cores in that C-state with threshholding to generate histograms, or with other PCU events and occupancy triggering to capture other details.	0,1,2,3	0x0	null	0
PCU	0xa	0x0	UNC_P_PROCHOT_EXTERNAL_CYCLES	Counts the number of cycles that we are in external PROCHOT mode.  This mode is triggered when a sensor off the die determines that something off-die (like DRAM) is too hot and must throttle to avoid damaging the chip.	0,1,2,3	0x0	null	0
PCU	0x9	0x0	UNC_P_PROCHOT_INTERNAL_CYCLES	Counts the number of cycles that we are in Interal PROCHOT mode.  This mode is triggered when a sensor on the die determines that we are too hot and must throttle to avoid damaging the chip.	0,1,2,3	0x0	null	0
PCU	0x63	0x0	UNC_P_TOTAL_TRANSITION_CYCLES	Number of cycles spent performing core C state transitions across all cores.	0,1,2,3	0x0	null	0
PCU	0x3	0x0	UNC_P_VOLT_TRANS_CYCLES_CHANGE	Counts the number of cycles when the system is changing voltage.  There is no filtering supported with this event.  One can use it as a simple event, or use it conjunction with the occupancy events to monitor the number of cores or threads that were impacted by the transition.  This event is calculated by or'ing together the increasing and decreasing events.	0,1,2,3	0x0	null	0
PCU	0x2	0x0	UNC_P_VOLT_TRANS_CYCLES_DECREASE	Counts the number of cycles when the system is decreasing voltage.  There is no filtering supported with this event.  One can use it as a simple event, or use it conjunction with the occupancy events to monitor the number of cores or threads that were impacted by the transition.	0,1,2,3	0x0	null	0
PCU	0x1	0x0	UNC_P_VOLT_TRANS_CYCLES_INCREASE	Counts the number of cycles when the system is increasing voltage.  There is no filtering supported with this event.  One can use it as a simple event, or use it conjunction with the occupancy events to monitor the number of cores or threads that were impacted by the transition.	0,1,2,3	0x0	null	0
PCU	0x32	0x0	UNC_P_VR_HOT_CYCLES	0	0,1,2,3	0x0	null	0
PCU	0x42	0x0	UNC_P_DEMOTIONS_CORE10	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x43	0x0	UNC_P_DEMOTIONS_CORE11	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x44	0x0	UNC_P_DEMOTIONS_CORE12	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x45	0x0	UNC_P_DEMOTIONS_CORE13	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x46	0x0	UNC_P_DEMOTIONS_CORE14	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x40	0x0	UNC_P_DEMOTIONS_CORE8	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x41	0x0	UNC_P_DEMOTIONS_CORE9	Counts the number of times when a configurable cores had a C-state demotion	0,1,2,3	0	PCUFilter[7:0]	0
PCU	0x2	0x0	UNC_P_FREQ_MIN_PERF_P_CYCLES	Counts the number of cycles when Perf P Limit is preventing us from dropping the frequency lower.  Perf P Limit is an algorithm that takes input from remote sockets when determining if a socket should drop it's frequency down.  This is largely to minimize increases in snoop and remote read latencies.	0,1,2,3	0	null	1
PCU	0x26	0x0	UNC_P_PKG_C_EXIT_LATENCY	Counts the number of cycles that the package is transitioning from package C2 to C3.	0,1,2,3	0	null	1
QPI LL	0x14	0x0	UNC_Q_CLOCKTICKS	Counts the number of clocks in the QPI LL.  This clock runs at 1/8th the GT/s speed of the QPI link.  For example, a 8GT/s link will have qfclk or 1GHz.  JKT does not support dynamic link speeds, so this frequency is fixed.	0,1,2,3	0x0	null	0
QPI LL	0x38	0x0	UNC_Q_CTO_COUNT	Counts the number of CTO (cluster trigger outs) events that were asserted across the two slots.  If both slots trigger in a given cycle, the event will increment by 2.  You can use edge detect to count the number of cases when both events triggered.	0,1,2,3	0x0	QPIMask0[17:0],QPIMatch0[17:0],QPIMask1[19:16],QPIMatch1[19:16]	1
QPI LL	0x13	0x2	UNC_Q_DIRECT2CORE.FAILURE_CREDITS	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because there were not enough Egress credits.  Had there been enough credits, the spawn would have worked as the RBT bit was set and the RBT tag matched.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x20	UNC_Q_DIRECT2CORE.FAILURE_CREDITS_MISS	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because the RBT tag did not match and there weren't enough Egress credits.   The valid bit was set.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x8	UNC_Q_DIRECT2CORE.FAILURE_CREDITS_RBT	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because there were not enough Egress credits AND the RBT bit was not set, but the RBT tag matched.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x80	UNC_Q_DIRECT2CORE.FAILURE_CREDITS_RBT_MISS	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because the RBT tag did not match, the valid bit was not set and there weren't enough Egress credits.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x10	UNC_Q_DIRECT2CORE.FAILURE_MISS	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because the RBT tag did not match although the valid bit was set and there were enough Egress credits.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x4	UNC_Q_DIRECT2CORE.FAILURE_RBT_HIT	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because the route-back table (RBT) specified that the transaction should not trigger a direct2core tranaction.  This is common for IO transactions.  There were enough Egress credits and the RBT tag matched but the valid bit was not set.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x40	UNC_Q_DIRECT2CORE.FAILURE_RBT_MISS	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn failed because the RBT tag did not match and the valid bit was not set although there were enough Egress credits.	0,1,2,3	0x0	null	0
QPI LL	0x13	0x1	UNC_Q_DIRECT2CORE.SUCCESS_RBT_HIT	Counts the number of DRS packets that we attempted to do direct2core on.  There are 4 mutually exlusive filters.  Filter [0] can be used to get successful spawns, while [1:3] provide the different failure cases.  Note that this does not count packets that are not candidates for Direct2Core.  The only candidates for Direct2Core are DRS packets destined for Cbos.; The spawn was successful.  There were sufficient credits, the RBT valid bit was set and there was an RBT tag match.  The message was marked to spawn direct2core.	0,1,2,3	0x0	null	0
QPI LL	0x12	0x0	UNC_Q_L1_POWER_CYCLES	Number of QPI qfclk cycles spent in L1 power mode.  L1 is a mode that totally shuts down a QPI link.  Use edge detect to count the number of instances when the QPI link entered L1.  Link power states are per link and per direction, so for example the Tx direction could be in one state while Rx was in another. Because L1 totally shuts down the link, it takes a good amount of time to exit this mode.	0,1,2,3	0x0	null	0
QPI LL	0x10	0x0	UNC_Q_RxL0P_POWER_CYCLES	Number of QPI qfclk cycles spent in L0p power mode.  L0p is a mode where we disable 1/2 of the QPI lanes, decreasing our bandwidth in order to save power.  It increases snoop and data transfer latencies and decreases overall bandwidth.  This mode can be very useful in NUMA optimized workloads that largely only utilize QPI for snoops and their responses.  Use edge detect to count the number of instances when the QPI link entered L0p.  Link power states are per link and per direction, so for example the Tx direction could be in one state while Rx was in another.	0,1,2,3	0x0	null	0
QPI LL	0xf	0x0	UNC_Q_RxL0_POWER_CYCLES	Number of QPI qfclk cycles spent in L0 power mode in the Link Layer.  L0 is the default mode which provides the highest performance with the most power.  Use edge detect to count the number of instances that the link entered L0.  Link power states are per link and per direction, so for example the Tx direction could be in one state while Rx was in another.  The phy layer  sometimes leaves L0 for training, which will not be captured by this event.	0,1,2,3	0x0	null	0
QPI LL	0x9	0x0	UNC_Q_RxL_BYPASSED	Counts the number of times that an incoming flit was able to bypass the flit buffer and pass directly across the BGF and into the Egress.  This is a latency optimization, and should generally be the common case.  If this value is less than the number of flits transfered, it implies that there was queueing getting onto the ring, and thus the transactions saw higher latency.	0,1,2,3	0x0	null	0
QPI LL	0x3	0x1	UNC_Q_RxL_CRC_ERRORS.LINK_INIT	Number of CRC errors detected in the QPI Agent.  Each QPI flit incorporates 8 bits of CRC for error detection.  This counts the number of flits where the CRC was able to detect an error.  After an error has been detected, the QPI agent will send a request to the transmitting socket to resend the flit (as well as any flits that came after it).; CRC errors detected during link initialization.	0,1,2,3	0x0	null	0
QPI LL	0x3	0x2	UNC_Q_RxL_CRC_ERRORS.NORMAL_OP	Number of CRC errors detected in the QPI Agent.  Each QPI flit incorporates 8 bits of CRC for error detection.  This counts the number of flits where the CRC was able to detect an error.  After an error has been detected, the QPI agent will send a request to the transmitting socket to resend the flit (as well as any flits that came after it).; CRC errors detected during normal operation.	0,1,2,3	0x0	null	0
QPI LL	0x1e	0x1	UNC_Q_RxL_CREDITS_CONSUMED_VN0.DRS	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the DRS message class.	0,1,2,3	0x0	null	1
QPI LL	0x1e	0x8	UNC_Q_RxL_CREDITS_CONSUMED_VN0.HOM	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the HOM message class.	0,1,2,3	0x0	null	1
QPI LL	0x1e	0x2	UNC_Q_RxL_CREDITS_CONSUMED_VN0.NCB	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the NCB message class.	0,1,2,3	0x0	null	1
QPI LL	0x1e	0x4	UNC_Q_RxL_CREDITS_CONSUMED_VN0.NCS	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the NCS message class.	0,1,2,3	0x0	null	1
QPI LL	0x1e	0x20	UNC_Q_RxL_CREDITS_CONSUMED_VN0.NDR	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the NDR message class.	0,1,2,3	0x0	null	1
QPI LL	0x1e	0x10	UNC_Q_RxL_CREDITS_CONSUMED_VN0.SNP	Counts the number of times that an RxQ VN0 credit was consumed (i.e. message uses a VN0 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN0 credit for the SNP message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x1	UNC_Q_RxL_CREDITS_CONSUMED_VN1.DRS	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the DRS message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x8	UNC_Q_RxL_CREDITS_CONSUMED_VN1.HOM	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the HOM message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x2	UNC_Q_RxL_CREDITS_CONSUMED_VN1.NCB	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the NCB message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x4	UNC_Q_RxL_CREDITS_CONSUMED_VN1.NCS	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the NCS message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x20	UNC_Q_RxL_CREDITS_CONSUMED_VN1.NDR	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the NDR message class.	0,1,2,3	0x0	null	1
QPI LL	0x39	0x10	UNC_Q_RxL_CREDITS_CONSUMED_VN1.SNP	Counts the number of times that an RxQ VN1 credit was consumed (i.e. message uses a VN1 credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.; VN1 credit for the SNP message class.	0,1,2,3	0x0	null	1
QPI LL	0x1d	0x0	UNC_Q_RxL_CREDITS_CONSUMED_VNA	Counts the number of times that an RxQ VNA credit was consumed (i.e. message uses a VNA credit for the Rx Buffer).  This includes packets that went through the RxQ and those that were bypasssed.	0,1,2,3	0x0	null	1
QPI LL	0xa	0x0	UNC_Q_RxL_CYCLES_NE	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.	0,1,2,3	0x0	null	0
QPI LL	0x1	0x2	UNC_Q_RxL_FLITS_G0.DATA	Counts the number of flits received from the QPI Link.  It includes filters for Idle, protocol, and Data Flits.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time (for L0) or 4B instead of 8B for L0p.; Number of data flitsreceived over QPI.  Each flit contains 64b of data.  This includes both DRS and NCB data flits (coherent and non-coherent).  This can be used to calculate the data bandwidth of the QPI link.  One can get a good picture of the QPI-link characteristics by evaluating the protocol flits, data flits, and idle/null flits.  This does not include the header flits that go in data packets.	0,1,2,3	0x0	null	0
QPI LL	0x1	0x1	UNC_Q_RxL_FLITS_G0.IDLE	Counts the number of flits received from the QPI Link.  It includes filters for Idle, protocol, and Data Flits.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time (for L0) or 4B instead of 8B for L0p.; Number of flits received over QPI that do not hold protocol payload.  When QPI is not in a power saving state, it continuously transmits flits across the link.  When there are no protocol flits to send, it will send IDLE and NULL flits  across.  These flits sometimes do carry a payload, such as credit returns, but are generall not considered part of the QPI bandwidth.	0,1,2,3	0x0	null	0
QPI LL	0x1	0x4	UNC_Q_RxL_FLITS_G0.NON_DATA	Counts the number of flits received from the QPI Link.  It includes filters for Idle, protocol, and Data Flits.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time (for L0) or 4B instead of 8B for L0p.; Number of non-NULL non-data flits received across QPI.  This basically tracks the protocol overhead on the QPI link.  One can get a good picture of the QPI-link characteristics by evaluating the protocol flits, data flits, and idle/null flits.  This includes the header flits for data packets.	0,1,2,3	0x0	null	0
QPI LL	0x2	0x18	UNC_Q_RxL_FLITS_G1.DRS	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits received over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.  This does not count data flits received over the NCB channel which transmits non-coherent data.	0,1,2,3	0x0	null	1
QPI LL	0x2	0x8	UNC_Q_RxL_FLITS_G1.DRS_DATA	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of data flits received over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.  This does not count data flits received over the NCB channel which transmits non-coherent data.  This includes only the data flits (not the header).	0,1,2,3	0x0	null	1
QPI LL	0x2	0x10	UNC_Q_RxL_FLITS_G1.DRS_NONDATA	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of protocol flits received over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.  This does not count data flits received over the NCB channel which transmits non-coherent data.  This includes only the header flits (not the data).  This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x2	0x6	UNC_Q_RxL_FLITS_G1.HOM	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of flits received over QPI on the home channel.	0,1,2,3	0x0	null	1
QPI LL	0x2	0x4	UNC_Q_RxL_FLITS_G1.HOM_NONREQ	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of non-request flits received over QPI on the home channel.  These are most commonly snoop responses, and this event can be used as a proxy for that.	0,1,2,3	0x0	null	1
QPI LL	0x2	0x2	UNC_Q_RxL_FLITS_G1.HOM_REQ	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of data request received over QPI on the home channel.  This basically counts the number of remote memory requests received over QPI.  In conjunction with the local read count in the Home Agent, one can calculate the number of LLC Misses.	0,1,2,3	0x0	null	1
QPI LL	0x2	0x1	UNC_Q_RxL_FLITS_G1.SNP	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of snoop request flits received over QPI.  These requests are contained in the snoop channel.  This does not include snoop responses, which are received on the home channel.	0,1,2,3	0x0	null	1
QPI LL	0x3	0xC	UNC_Q_RxL_FLITS_G2.NCB	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass flits.  These packets are generally used to transmit non-coherent data across QPI.	0,1,2,3	0x0	null	1
QPI LL	0x3	0x4	UNC_Q_RxL_FLITS_G2.NCB_DATA	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass data flits.  These flits are generally used to transmit non-coherent data across QPI.  This does not include a count of the DRS (coherent) data flits.  This only counts the data flits, not the NCB headers.	0,1,2,3	0x0	null	1
QPI LL	0x3	0x8	UNC_Q_RxL_FLITS_G2.NCB_NONDATA	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass non-data flits.  These packets are generally used to transmit non-coherent data across QPI, and the flits counted here are for headers and other non-data flits.  This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x3	0x10	UNC_Q_RxL_FLITS_G2.NCS	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of NCS (non-coherent standard) flits received over QPI.    This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x3	0x1	UNC_Q_RxL_FLITS_G2.NDR_AD	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits received over the NDR (Non-Data Response) channel.  This channel is used to send a variety of protocol flits including grants and completions.  This is only for NDR packets to the local socket which use the AK ring.	0,1,2,3	0x0	null	1
QPI LL	0x3	0x2	UNC_Q_RxL_FLITS_G2.NDR_AK	Counts the number of flits received from the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits received over the NDR (Non-Data Response) channel.  This channel is used to send a variety of protocol flits including grants and completions.  This is only for NDR packets destined for Route-thru to a remote socket.	0,1,2,3	0x0	null	1
QPI LL	0x8	0x0	UNC_Q_RxL_INSERTS	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.	0,1,2,3	0x0	null	0
QPI LL	0x9	0x0	UNC_Q_RxL_INSERTS_DRS	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only DRS flits.	0,1,2,3	0x0	null	1
QPI LL	0xc	0x0	UNC_Q_RxL_INSERTS_HOM	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only HOM flits.	0,1,2,3	0x0	null	1
QPI LL	0xa	0x0	UNC_Q_RxL_INSERTS_NCB	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCB flits.	0,1,2,3	0x0	null	1
QPI LL	0xb	0x0	UNC_Q_RxL_INSERTS_NCS	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCS flits.	0,1,2,3	0x0	null	1
QPI LL	0xe	0x0	UNC_Q_RxL_INSERTS_NDR	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NDR flits.	0,1,2,3	0x0	null	1
QPI LL	0xd	0x0	UNC_Q_RxL_INSERTS_SNP	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only SNP flits.	0,1,2,3	0x0	null	1
QPI LL	0xb	0x0	UNC_Q_RxL_OCCUPANCY	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.	0,1,2,3	0x0	null	0
QPI LL	0x15	0x0	UNC_Q_RxL_OCCUPANCY_DRS	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors DRS flits only.	0,1,2,3	0x0	null	1
QPI LL	0x18	0x0	UNC_Q_RxL_OCCUPANCY_HOM	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors HOM flits only.	0,1,2,3	0x0	null	1
QPI LL	0x16	0x0	UNC_Q_RxL_OCCUPANCY_NCB	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCB flits only.	0,1,2,3	0x0	null	1
QPI LL	0x17	0x0	UNC_Q_RxL_OCCUPANCY_NCS	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCS flits only.	0,1,2,3	0x0	null	1
QPI LL	0x1a	0x0	UNC_Q_RxL_OCCUPANCY_NDR	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NDR flits only.	0,1,2,3	0x0	null	1
QPI LL	0x19	0x0	UNC_Q_RxL_OCCUPANCY_SNP	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors SNP flits only.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x1	UNC_Q_RxL_STALLS_VN0.BGF_DRS	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the HOM message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x8	UNC_Q_RxL_STALLS_VN0.BGF_HOM	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the DRS message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x2	UNC_Q_RxL_STALLS_VN0.BGF_NCB	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the SNP message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x4	UNC_Q_RxL_STALLS_VN0.BGF_NCS	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the NDR message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x20	UNC_Q_RxL_STALLS_VN0.BGF_NDR	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the NCS message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x10	UNC_Q_RxL_STALLS_VN0.BGF_SNP	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet from the NCB message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x40	UNC_Q_RxL_STALLS_VN0.EGRESS_CREDITS	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled a packet because there were insufficient BGF credits.  For details on a message class granularity, use the Egress Credit Occupancy events.	0,1,2,3	0x0	null	1
QPI LL	0x35	0x80	UNC_Q_RxL_STALLS_VN0.GV	Number of stalls trying to send to R3QPI on Virtual Network 0; Stalled because a GV transition (frequency transition) was taking place.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x1	UNC_Q_RxL_STALLS_VN1.BGF_DRS	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the HOM message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x8	UNC_Q_RxL_STALLS_VN1.BGF_HOM	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the DRS message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x2	UNC_Q_RxL_STALLS_VN1.BGF_NCB	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the SNP message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x4	UNC_Q_RxL_STALLS_VN1.BGF_NCS	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the NDR message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x20	UNC_Q_RxL_STALLS_VN1.BGF_NDR	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the NCS message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0x3a	0x10	UNC_Q_RxL_STALLS_VN1.BGF_SNP	Number of stalls trying to send to R3QPI on Virtual Network 1.; Stalled a packet from the NCB message class because there were not enough BGF credits.  In bypass mode, we will stall on the packet boundary, while in RxQ mode we will stall on the flit boundary.	0,1,2,3	0x0	null	1
QPI LL	0xd	0x0	UNC_Q_TxL0P_POWER_CYCLES	Number of QPI qfclk cycles spent in L0p power mode.  L0p is a mode where we disable 1/2 of the QPI lanes, decreasing our bandwidth in order to save power.  It increases snoop and data transfer latencies and decreases overall bandwidth.  This mode can be very useful in NUMA optimized workloads that largely only utilize QPI for snoops and their responses.  Use edge detect to count the number of instances when the QPI link entered L0p.  Link power states are per link and per direction, so for example the Tx direction could be in one state while Rx was in another.	0,1,2,3	0x0	null	0
QPI LL	0xc	0x0	UNC_Q_TxL0_POWER_CYCLES	Number of QPI qfclk cycles spent in L0 power mode in the Link Layer.  L0 is the default mode which provides the highest performance with the most power.  Use edge detect to count the number of instances that the link entered L0.  Link power states are per link and per direction, so for example the Tx direction could be in one state while Rx was in another.  The phy layer  sometimes leaves L0 for training, which will not be captured by this event.	0,1,2,3	0x0	null	0
QPI LL	0x5	0x0	UNC_Q_TxL_BYPASSED	Counts the number of times that an incoming flit was able to bypass the Tx flit buffer and pass directly out the QPI Link. Generally, when data is transmitted across QPI, it will bypass the TxQ and pass directly to the link.  However, the TxQ will be used with L0p and when LLR occurs, increasing latency to transfer out to the link.	0,1,2,3	0x0	null	0
QPI LL	0x2	0x2	UNC_Q_TxL_CRC_NO_CREDITS.ALMOST_FULL	Number of cycles when the Tx side ran out of Link Layer Retry credits, causing the Tx to stall.; When LLR is almost full, we block some but not all packets.	0,1,2,3	0x0	null	0
QPI LL	0x2	0x1	UNC_Q_TxL_CRC_NO_CREDITS.FULL	Number of cycles when the Tx side ran out of Link Layer Retry credits, causing the Tx to stall.; When LLR is totally full, we are not allowed to send any packets.	0,1,2,3	0x0	null	0
QPI LL	0x6	0x0	UNC_Q_TxL_CYCLES_NE	Counts the number of cycles when the TxQ is not empty. Generally, when data is transmitted across QPI, it will bypass the TxQ and pass directly to the link.  However, the TxQ will be used with L0p and when LLR occurs, increasing latency to transfer out to the link.	0,1,2,3	0x0	null	0
QPI LL	0x0	0x2	UNC_Q_TxL_FLITS_G0.DATA	Counts the number of flits transmitted across the QPI Link.  It includes filters for Idle, protocol, and Data Flits.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time (for L0) or 4B instead of 8B for L0p.; Number of data flits transmitted over QPI.  Each flit contains 64b of data.  This includes both DRS and NCB data flits (coherent and non-coherent).  This can be used to calculate the data bandwidth of the QPI link.  One can get a good picture of the QPI-link characteristics by evaluating the protocol flits, data flits, and idle/null flits.  This does not include the header flits that go in data packets.	0,1,2,3	0x0	null	0
QPI LL	0x0	0x4	UNC_Q_TxL_FLITS_G0.NON_DATA	Counts the number of flits transmitted across the QPI Link.  It includes filters for Idle, protocol, and Data Flits.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time (for L0) or 4B instead of 8B for L0p.; Number of non-NULL non-data flits transmitted across QPI.  This basically tracks the protocol overhead on the QPI link.  One can get a good picture of the QPI-link characteristics by evaluating the protocol flits, data flits, and idle/null flits.  This includes the header flits for data packets.	0,1,2,3	0x0	null	0
QPI LL	0x0	0x18	UNC_Q_TxL_FLITS_G1.DRS	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits transmitted over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.	0,1,2,3	0x0	null	1
QPI LL	0x0	0x8	UNC_Q_TxL_FLITS_G1.DRS_DATA	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of data flits transmitted over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.  This does not count data flits transmitted over the NCB channel which transmits non-coherent data.  This includes only the data flits (not the header).	0,1,2,3	0x0	null	1
QPI LL	0x0	0x10	UNC_Q_TxL_FLITS_G1.DRS_NONDATA	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of protocol flits transmitted over QPI on the DRS (Data Response) channel.  DRS flits are used to transmit data with coherency.  This does not count data flits transmitted over the NCB channel which transmits non-coherent data.  This includes only the header flits (not the data).  This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x0	0x6	UNC_Q_TxL_FLITS_G1.HOM	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of flits transmitted over QPI on the home channel.	0,1,2,3	0x0	null	1
QPI LL	0x0	0x4	UNC_Q_TxL_FLITS_G1.HOM_NONREQ	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of non-request flits transmitted over QPI on the home channel.  These are most commonly snoop responses, and this event can be used as a proxy for that.	0,1,2,3	0x0	null	1
QPI LL	0x0	0x2	UNC_Q_TxL_FLITS_G1.HOM_REQ	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of data request transmitted over QPI on the home channel.  This basically counts the number of remote memory requests transmitted over QPI.  In conjunction with the local read count in the Home Agent, one can calculate the number of LLC Misses.	0,1,2,3	0x0	null	1
QPI LL	0x0	0x1	UNC_Q_TxL_FLITS_G1.SNP	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for SNP, HOM, and DRS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the number of snoop request flits transmitted over QPI.  These requests are contained in the snoop channel.  This does not include snoop responses, which are transmitted on the home channel.	0,1,2,3	0x0	null	1
QPI LL	0x1	0xC	UNC_Q_TxL_FLITS_G2.NCB	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass flits.  These packets are generally used to transmit non-coherent data across QPI.	0,1,2,3	0x0	null	1
QPI LL	0x1	0x4	UNC_Q_TxL_FLITS_G2.NCB_DATA	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass data flits.  These flits are generally used to transmit non-coherent data across QPI.  This does not include a count of the DRS (coherent) data flits.  This only counts the data flits, not te NCB headers.	0,1,2,3	0x0	null	1
QPI LL	0x1	0x8	UNC_Q_TxL_FLITS_G2.NCB_NONDATA	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of Non-Coherent Bypass non-data flits.  These packets are generally used to transmit non-coherent data across QPI, and the flits counted here are for headers and other non-data flits.  This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x1	0x10	UNC_Q_TxL_FLITS_G2.NCS	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Number of NCS (non-coherent standard) flits transmitted over QPI.    This includes extended headers.	0,1,2,3	0x0	null	1
QPI LL	0x1	0x1	UNC_Q_TxL_FLITS_G2.NDR_AD	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits transmitted over the NDR (Non-Data Response) channel.  This channel is used to send a variety of protocol flits including grants and completions.  This is only for NDR packets to the local socket which use the AK ring.	0,1,2,3	0x0	null	1
QPI LL	0x1	0x2	UNC_Q_TxL_FLITS_G2.NDR_AK	Counts the number of flits trasmitted across the QPI Link.  This is one of three groups that allow us to track flits.  It includes filters for NDR, NCB, and NCS message classes.  Each flit is made up of 80 bits of information (in addition to some ECC data).  In full-width (L0) mode, flits are made up of four fits, each of which contains 20 bits of data (along with some additional ECC data).   In half-width (L0p) mode, the fits are only 10 bits, and therefore it takes twice as many fits to transmit a flit.  When one talks about QPI speed (for example, 8.0 GT/s), the transfers here refer to fits.  Therefore, in L0, the system will transfer 1 flit at the rate of 1/4th the QPI speed.  One can calculate the bandwidth of the link by taking: flits*80b/time.  Note that this is not the same as data bandwidth.  For example, when we are transfering a 64B cacheline across QPI, we will break it into 9 flits -- 1 with header information and 8 with 64 bits of actual data and an additional 16 bits of other information.  To calculate data bandwidth, one should therefore do: data flits * 8B / time.; Counts the total number of flits transmitted over the NDR (Non-Data Response) channel.  This channel is used to send a variety of protocol flits including grants and completions.  This is only for NDR packets destined for Route-thru to a remote socket.	0,1,2,3	0x0	null	1
QPI LL	0x4	0x0	UNC_Q_TxL_INSERTS	Number of allocations into the QPI Tx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the TxQ and pass directly to the link.  However, the TxQ will be used with L0p and when LLR occurs, increasing latency to transfer out to the link.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.	0,1,2,3	0x0	null	0
QPI LL	0x7	0x0	UNC_Q_TxL_OCCUPANCY	Accumulates the number of flits in the TxQ.  Generally, when data is transmitted across QPI, it will bypass the TxQ and pass directly to the link.  However, the TxQ will be used with L0p and when LLR occurs, increasing latency to transfer out to the link. This can be used with the cycles not empty event to track average occupancy, or the allocations event to track average lifetime in the TxQ.	0,1,2,3	0x0	null	0
QPI LL	0x26	0x1	UNC_Q_TxR_AD_HOM_CREDIT_ACQUIRED.VN0	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle. Flow Control FIFO for Home messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x26	0x2	UNC_Q_TxR_AD_HOM_CREDIT_ACQUIRED.VN1	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle. Flow Control FIFO for Home messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x22	0x1	UNC_Q_TxR_AD_HOM_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle.  Flow Control FIFO for HOM messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x22	0x2	UNC_Q_TxR_AD_HOM_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle.  Flow Control FIFO for HOM messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x28	0x1	UNC_Q_TxR_AD_NDR_CREDIT_ACQUIRED.VN0	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle.  Flow Control FIFO for NDR messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x28	0x2	UNC_Q_TxR_AD_NDR_CREDIT_ACQUIRED.VN1	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle.  Flow Control FIFO for NDR messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x24	0x1	UNC_Q_TxR_AD_NDR_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle. Flow Control FIFO  for NDR messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x24	0x2	UNC_Q_TxR_AD_NDR_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle. Flow Control FIFO  for NDR messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x27	0x1	UNC_Q_TxR_AD_SNP_CREDIT_ACQUIRED.VN0	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle.  Flow Control FIFO for Snoop messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x27	0x2	UNC_Q_TxR_AD_SNP_CREDIT_ACQUIRED.VN1	Number of link layer credits into the R3 (for transactions across the BGF) acquired each cycle.  Flow Control FIFO for Snoop messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x23	0x1	UNC_Q_TxR_AD_SNP_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle.  Flow Control FIFO fro Snoop messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x23	0x2	UNC_Q_TxR_AD_SNP_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of link layer credits into the R3 (for transactions across the BGF) available in each cycle.  Flow Control FIFO fro Snoop messages on AD.	0,1,2,3	0x0	null	1
QPI LL	0x29	0x1	UNC_Q_TxR_AK_NDR_CREDIT_ACQUIRED.VN0	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. Local NDR message class to AK Egress.	0,1,2,3	0x0	null	1
QPI LL	0x29	0x2	UNC_Q_TxR_AK_NDR_CREDIT_ACQUIRED.VN1	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. Local NDR message class to AK Egress.	0,1,2,3	0x0	null	1
QPI LL	0x25	0x1	UNC_Q_TxR_AK_NDR_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  Local NDR message class to AK Egress.	0,1,2,3	0x0	null	1
QPI LL	0x25	0x2	UNC_Q_TxR_AK_NDR_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  Local NDR message class to AK Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2a	0x1	UNC_Q_TxR_BL_DRS_CREDIT_ACQUIRED.VN0	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2a	0x2	UNC_Q_TxR_BL_DRS_CREDIT_ACQUIRED.VN1	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2a	0x4	UNC_Q_TxR_BL_DRS_CREDIT_ACQUIRED.VN_SHR	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x1f	0x1	UNC_Q_TxR_BL_DRS_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x1f	0x2	UNC_Q_TxR_BL_DRS_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x1f	0x4	UNC_Q_TxR_BL_DRS_CREDIT_OCCUPANCY.VN_SHR	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  DRS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2b	0x1	UNC_Q_TxR_BL_NCB_CREDIT_ACQUIRED.VN0	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. NCB message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2b	0x2	UNC_Q_TxR_BL_NCB_CREDIT_ACQUIRED.VN1	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. NCB message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x20	0x1	UNC_Q_TxR_BL_NCB_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  NCB message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x20	0x2	UNC_Q_TxR_BL_NCB_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  NCB message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2c	0x1	UNC_Q_TxR_BL_NCS_CREDIT_ACQUIRED.VN0	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. NCS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x2c	0x2	UNC_Q_TxR_BL_NCS_CREDIT_ACQUIRED.VN1	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. NCS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x21	0x1	UNC_Q_TxR_BL_NCS_CREDIT_OCCUPANCY.VN0	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  NCS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x21	0x2	UNC_Q_TxR_BL_NCS_CREDIT_OCCUPANCY.VN1	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  NCS message class to BL Egress.	0,1,2,3	0x0	null	1
QPI LL	0x1c	0x0	UNC_Q_VNA_CREDIT_RETURNS	Number of VNA credits returned.	0,1,2,3	0x0	null	1
QPI LL	0x1b	0x0	UNC_Q_VNA_CREDIT_RETURN_OCCUPANCY	Number of VNA credits in the Rx side that are waitng to be returned back across the link.	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.AnyResp9flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.AnyResp11flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_M	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.WbIData	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.WbSData	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.WbEData	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.AnyResp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.AnyDataC	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCB.AnyMsg	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCB.AnyInt	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_F	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_F_FrcAckCnflt	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_F_Cmp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_E	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_E_FrcAckCnflt	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.DRS.DataC_E_Cmp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespFwd	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespFwdI	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespFwdS	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespFwdIWb	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespFwdSWb	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespIWb	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.RespSWb	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.AnyReq	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.HOM.AnyResp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.SNP.AnySnp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NDR.AnyCmp	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCS.NcRd	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCS.AnyMsg1or2flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCS.AnyMsg3flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCB.AnyMsg9flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MESSAGE.NCB.AnyMsg11flits	0	0,1,2,3	0x0	null	1
QPI LL	0x38	0x0	UNC_Q_MATCH_MASK	0	0,1,2,3	0x0	null	1
QPI LL	0xF	0x1	UNC_Q_RxL_CYCLES_NE_DRS.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors DRS flits only.	0,1,2,3	0	null	1
QPI LL	0xF	0x2	UNC_Q_RxL_CYCLES_NE_DRS.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors DRS flits only.	0,1,2,3	0	null	1
QPI LL	0x12	0x1	UNC_Q_RxL_CYCLES_NE_HOM.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors HOM flits only.	0,1,2,3	0	null	1
QPI LL	0x12	0x2	UNC_Q_RxL_CYCLES_NE_HOM.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors HOM flits only.	0,1,2,3	0	null	1
QPI LL	0x10	0x1	UNC_Q_RxL_CYCLES_NE_NCB.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NCB flits only.	0,1,2,3	0	null	1
QPI LL	0x10	0x2	UNC_Q_RxL_CYCLES_NE_NCB.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NCB flits only.	0,1,2,3	0	null	1
QPI LL	0x11	0x1	UNC_Q_RxL_CYCLES_NE_NCS.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NCS flits only.	0,1,2,3	0	null	1
QPI LL	0x11	0x2	UNC_Q_RxL_CYCLES_NE_NCS.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NCS flits only.	0,1,2,3	0	null	1
QPI LL	0x14	0x1	UNC_Q_RxL_CYCLES_NE_NDR.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NDR flits only.	0,1,2,3	0	null	1
QPI LL	0x14	0x2	UNC_Q_RxL_CYCLES_NE_NDR.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors NDR flits only.	0,1,2,3	0	null	1
QPI LL	0x13	0x1	UNC_Q_RxL_CYCLES_NE_SNP.VN0	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors SNP flits only.	0,1,2,3	0	null	1
QPI LL	0x13	0x2	UNC_Q_RxL_CYCLES_NE_SNP.VN1	Counts the number of cycles that the QPI RxQ was not empty.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy Accumulator event to calculate the average occupancy.  This monitors SNP flits only.	0,1,2,3	0	null	1
QPI LL	0x9	0x1	UNC_Q_RxL_INSERTS_DRS.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only DRS flits.	0,1,2,3	0	null	1
QPI LL	0x9	0x2	UNC_Q_RxL_INSERTS_DRS.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only DRS flits.	0,1,2,3	0	null	1
QPI LL	0xC	0x1	UNC_Q_RxL_INSERTS_HOM.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only HOM flits.	0,1,2,3	0	null	1
QPI LL	0xC	0x2	UNC_Q_RxL_INSERTS_HOM.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only HOM flits.	0,1,2,3	0	null	1
QPI LL	0xA	0x1	UNC_Q_RxL_INSERTS_NCB.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCB flits.	0,1,2,3	0	null	1
QPI LL	0xA	0x2	UNC_Q_RxL_INSERTS_NCB.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCB flits.	0,1,2,3	0	null	1
QPI LL	0xB	0x1	UNC_Q_RxL_INSERTS_NCS.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCS flits.	0,1,2,3	0	null	1
QPI LL	0xB	0x2	UNC_Q_RxL_INSERTS_NCS.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NCS flits.	0,1,2,3	0	null	1
QPI LL	0xE	0x1	UNC_Q_RxL_INSERTS_NDR.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NDR flits.	0,1,2,3	0	null	1
QPI LL	0xE	0x2	UNC_Q_RxL_INSERTS_NDR.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only NDR flits.	0,1,2,3	0	null	1
QPI LL	0xD	0x1	UNC_Q_RxL_INSERTS_SNP.VN0	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only SNP flits.	0,1,2,3	0	null	1
QPI LL	0xD	0x2	UNC_Q_RxL_INSERTS_SNP.VN1	Number of allocations into the QPI Rx Flit Buffer.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Occupancy event in order to calculate the average flit buffer lifetime.  This monitors only SNP flits.	0,1,2,3	0	null	1
QPI LL	0x15	0x1	UNC_Q_RxL_OCCUPANCY_DRS.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors DRS flits only.	0,1,2,3	0	null	1
QPI LL	0x15	0x2	UNC_Q_RxL_OCCUPANCY_DRS.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors DRS flits only.	0,1,2,3	0	null	1
QPI LL	0x18	0x1	UNC_Q_RxL_OCCUPANCY_HOM.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors HOM flits only.	0,1,2,3	0	null	1
QPI LL	0x18	0x2	UNC_Q_RxL_OCCUPANCY_HOM.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors HOM flits only.	0,1,2,3	0	null	1
QPI LL	0x16	0x1	UNC_Q_RxL_OCCUPANCY_NCB.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCB flits only.	0,1,2,3	0	null	1
QPI LL	0x16	0x2	UNC_Q_RxL_OCCUPANCY_NCB.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCB flits only.	0,1,2,3	0	null	1
QPI LL	0x17	0x1	UNC_Q_RxL_OCCUPANCY_NCS.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCS flits only.	0,1,2,3	0	null	1
QPI LL	0x17	0x2	UNC_Q_RxL_OCCUPANCY_NCS.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NCS flits only.	0,1,2,3	0	null	1
QPI LL	0x1A	0x1	UNC_Q_RxL_OCCUPANCY_NDR.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NDR flits only.	0,1,2,3	0	null	1
QPI LL	0x1A	0x2	UNC_Q_RxL_OCCUPANCY_NDR.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors NDR flits only.	0,1,2,3	0	null	1
QPI LL	0x19	0x1	UNC_Q_RxL_OCCUPANCY_SNP.VN0	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors SNP flits only.	0,1,2,3	0	null	1
QPI LL	0x19	0x2	UNC_Q_RxL_OCCUPANCY_SNP.VN1	Accumulates the number of elements in the QPI RxQ in each cycle.  Generally, when data is transmitted across QPI, it will bypass the RxQ and pass directly to the ring interface.  If things back up getting transmitted onto the ring, however, it may need to allocate into this buffer, thus increasing the latency.  This event can be used in conjunction with the Flit Buffer Not Empty event to calculate average occupancy, or with the Flit Buffer Allocations event to track average lifetime.  This monitors SNP flits only.	0,1,2,3	0	null	1
QPI LL	0x29	0x0	UNC_Q_TxR_AK_NDR_CREDIT_ACQUIRED	Number of credits into the R3 (for transactions across the BGF) acquired each cycle. Local NDR message class to AK Egress.	0,1,2,3	0	null	1
QPI LL	0x25	0x0	UNC_Q_TxR_AK_NDR_CREDIT_OCCUPANCY	Occupancy event that tracks the number of credits into the R3 (for transactions across the BGF) available in each cycle.  Local NDR message class to AK Egress.	0,1,2,3	0	null	1
R2PCIe	0x1	0x0	UNC_R2_CLOCKTICKS	Counts the number of uclks in the R2PCIe uclk domain.  This could be slightly different than the count in the Ubox because of enable/freeze delays.  However, because the R2PCIe is close to the Ubox, they generally should not diverge by more than a handful of cycles.	0,1,2,3	0x0	null	0
R2PCIe	0x33	0x8	UNC_R2_IIO_CREDITS_ACQUIRED.DRS	Counts the number of credits that are acquired in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the DRS message class.	0,1	0x0	null	0
R2PCIe	0x33	0x10	UNC_R2_IIO_CREDITS_ACQUIRED.NCB	Counts the number of credits that are acquired in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the NCB message class.	0,1	0x0	null	0
R2PCIe	0x33	0x20	UNC_R2_IIO_CREDITS_ACQUIRED.NCS	Counts the number of credits that are acquired in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the NCS message class.	0,1	0x0	null	0
R2PCIe	0x34	0x8	UNC_R2_IIO_CREDITS_REJECT.DRS	Counts the number of times that a request pending in the BL Ingress attempted to acquire either a NCB or NCS credit to transmit into the IIO, but was rejected because no credits were available.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the DRS message class.	0,1	0x0	null	0
R2PCIe	0x32	0x8	UNC_R2_IIO_CREDITS_USED.DRS	Counts the number of cycles when one or more credits in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the DRS message class.	0,1	0x0	null	0
R2PCIe	0x32	0x10	UNC_R2_IIO_CREDITS_USED.NCB	Counts the number of cycles when one or more credits in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the NCB message class.	0,1	0x0	null	0
R2PCIe	0x32	0x20	UNC_R2_IIO_CREDITS_USED.NCS	Counts the number of cycles when one or more credits in the R2PCIe agent for sending transactions into the IIO on either NCB or NCS are in use.  Transactions from the BL ring going into the IIO Agent must first acquire a credit.  These credits are for either the NCB or NCS message classes.  NCB, or non-coherent bypass messages are used to transmit data without coherency (and are common).  NCS is used for reads to PCIe (and should be used sparingly).; Credits to the IIO for the NCS message class.	0,1	0x0	null	0
R2PCIe	0x7	0x4	UNC_R2_RING_AD_USED.CCW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x8	UNC_R2_RING_AD_USED.CCW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x40	UNC_R2_RING_AD_USED.CCW_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x80	UNC_R2_RING_AD_USED.CCW_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x1	UNC_R2_RING_AD_USED.CW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x2	UNC_R2_RING_AD_USED.CW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x10	UNC_R2_RING_AD_USED.CW_VR1_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x7	0x20	UNC_R2_RING_AD_USED.CW_VR1_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x4	UNC_R2_RING_AK_USED.CCW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x8	UNC_R2_RING_AK_USED.CCW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x40	UNC_R2_RING_AK_USED.CCW_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x80	UNC_R2_RING_AK_USED.CCW_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x1	UNC_R2_RING_AK_USED.CW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x2	UNC_R2_RING_AK_USED.CW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x10	UNC_R2_RING_AK_USED.CW_VR1_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x8	0x20	UNC_R2_RING_AK_USED.CW_VR1_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x4	UNC_R2_RING_BL_USED.CCW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x8	UNC_R2_RING_BL_USED.CCW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x40	UNC_R2_RING_BL_USED.CCW_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x80	UNC_R2_RING_BL_USED.CCW_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x1	UNC_R2_RING_BL_USED.CW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x2	UNC_R2_RING_BL_USED.CW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x10	UNC_R2_RING_BL_USED.CW_VR1_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0x9	0x20	UNC_R2_RING_BL_USED.CW_VR1_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 1.	0,1,2,3	0x0	null	0
R2PCIe	0xa	0xCC	UNC_R2_RING_IV_USED.CCW	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters for Counterclockwise polarity	0,1,2,3	0x0	null	0
R2PCIe	0xa	0x33	UNC_R2_RING_IV_USED.CW	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters for Clockwise polarity	0,1,2,3	0x0	null	0
R2PCIe	0x12	0x0	UNC_R2_RxR_AK_BOUNCES	Counts the number of times when a request destined for the AK ingress bounced.	0	0x0	null	0
R2PCIe	0x10	0x10	UNC_R2_RxR_CYCLES_NE.NCB	Counts the number of cycles when the R2PCIe Ingress is not empty.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Ingress Occupancy Accumulator event in order to calculate average queue occupancy.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCB Ingress Queue	0,1	0x0	null	0
R2PCIe	0x10	0x20	UNC_R2_RxR_CYCLES_NE.NCS	Counts the number of cycles when the R2PCIe Ingress is not empty.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Ingress Occupancy Accumulator event in order to calculate average queue occupancy.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCS Ingress Queue	0,1	0x0	null	0
R2PCIe	0x11	0x10	UNC_R2_RxR_INSERTS.NCB	Counts the number of allocations into the R2PCIe Ingress.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCB Ingress Queue	0,1	0x0	null	0
R2PCIe	0x11	0x20	UNC_R2_RxR_INSERTS.NCS	Counts the number of allocations into the R2PCIe Ingress.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCS Ingress Queue	0,1	0x0	null	0
R2PCIe	0x13	0x8	UNC_R2_RxR_OCCUPANCY.DRS	Accumulates the occupancy of a given R2PCIe Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the R2PCIe Ingress Not Empty event to calculate average occupancy or the R2PCIe Ingress Allocations event in order to calculate average queuing latency.; DRS Ingress Queue	0	0x0	null	0
R2PCIe	0x25	0x1	UNC_R2_TxR_CYCLES_FULL.AD	Counts the number of cycles when the R2PCIe Egress buffer is full.; AD Egress Queue	0	0x0	null	0
R2PCIe	0x25	0x2	UNC_R2_TxR_CYCLES_FULL.AK	Counts the number of cycles when the R2PCIe Egress buffer is full.; AK Egress Queue	0	0x0	null	0
R2PCIe	0x25	0x4	UNC_R2_TxR_CYCLES_FULL.BL	Counts the number of cycles when the R2PCIe Egress buffer is full.; BL Egress Queue	0	0x0	null	0
R2PCIe	0x23	0x1	UNC_R2_TxR_CYCLES_NE.AD	Counts the number of cycles when the R2PCIe Egress is not empty.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Egress Occupancy Accumulator event in order to calculate average queue occupancy.  Only a single Egress queue can be tracked at any given time.  It is not possible to filter based on direction or polarity.; AD Egress Queue	0	0x0	null	0
R2PCIe	0x23	0x2	UNC_R2_TxR_CYCLES_NE.AK	Counts the number of cycles when the R2PCIe Egress is not empty.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Egress Occupancy Accumulator event in order to calculate average queue occupancy.  Only a single Egress queue can be tracked at any given time.  It is not possible to filter based on direction or polarity.; AK Egress Queue	0	0x0	null	0
R2PCIe	0x23	0x4	UNC_R2_TxR_CYCLES_NE.BL	Counts the number of cycles when the R2PCIe Egress is not empty.  This tracks one of the three rings that are used by the R2PCIe agent.  This can be used in conjunction with the R2PCIe Egress Occupancy Accumulator event in order to calculate average queue occupancy.  Only a single Egress queue can be tracked at any given time.  It is not possible to filter based on direction or polarity.; BL Egress Queue	0	0x0	null	0
R2PCIe	0x7	0x33	UNC_R2_RING_AD_USED.CW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0x7	0xCC	UNC_R2_RING_AD_USED.CCW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0x8	0x33	UNC_R2_RING_AK_USED.CW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0x8	0xCC	UNC_R2_RING_AK_USED.CCW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0x9	0x33	UNC_R2_RING_BL_USED.CW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0x9	0xCC	UNC_R2_RING_BL_USED.CCW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2,3	0	null	0
R2PCIe	0xA	0xFF	UNC_R2_RING_IV_USED.ANY	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters any polarity	0,1,2,3	0	null	0
R2PCIe	0x12	0x1	UNC_R2_RxR_AK_BOUNCES.CW	Counts the number of times when a request destined for the AK ingress bounced.	0	0	null	0
R2PCIe	0x12	0x2	UNC_R2_RxR_AK_BOUNCES.CCW	Counts the number of times when a request destined for the AK ingress bounced.	0	0	null	0
R2PCIe	0x28	0x1	UNC_R2_TxR_NACK_CCW.AD	AD CounterClockwise Egress Queue	0,1	0	null	0
R2PCIe	0x28	0x2	UNC_R2_TxR_NACK_CCW.AK	AK CounterClockwise Egress Queue	0,1	0	null	0
R2PCIe	0x28	0x4	UNC_R2_TxR_NACK_CCW.BL	BL CounterClockwise Egress Queue	0,1	0	null	0
R2PCIe	0x26	0x1	UNC_R2_TxR_NACK_CW.AD	AD Clockwise Egress Queue	0,1	0	null	0
R2PCIe	0x26	0x2	UNC_R2_TxR_NACK_CW.AK	AK Clockwise Egress Queue	0,1	0	null	0
R2PCIe	0x26	0x4	UNC_R2_TxR_NACK_CW.BL	BL Clockwise Egress Queue	0,1	0	null	0
R3QPI	0x1	0x0	UNC_R3_CLOCKTICKS	Counts the number of uclks in the QPI uclk domain.  This could be slightly different than the count in the Ubox because of enable/freeze delays.  However, because the QPI Agent is close to the Ubox, they generally should not diverge by more than a handful of cycles.	0,1,2	0x0	null	0
R3QPI	0x2c	0x4	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO10	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 10	0,1	0x0	null	0
R3QPI	0x2c	0x8	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO11	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 11	0,1	0x0	null	0
R3QPI	0x2c	0x10	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO12	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 12	0,1	0x0	null	0
R3QPI	0x2c	0x20	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO13	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 13	0,1	0x0	null	0
R3QPI	0x2c	0x40	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO14	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 14&16	0,1	0x0	null	0
R3QPI	0x2c	0x1	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO8	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 8	0,1	0x0	null	0
R3QPI	0x2c	0x2	UNC_R3_C_HI_AD_CREDITS_EMPTY.CBO9	No credits available to send to Cbox on the AD Ring (covers higher CBoxes); Cbox 9	0,1	0x0	null	0
R3QPI	0x2b	0x1	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO0	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 0	0,1	0x0	null	0
R3QPI	0x2b	0x2	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO1	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 1	0,1	0x0	null	0
R3QPI	0x2b	0x4	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO2	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 2	0,1	0x0	null	0
R3QPI	0x2b	0x8	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO3	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 3	0,1	0x0	null	0
R3QPI	0x2b	0x10	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO4	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 4	0,1	0x0	null	0
R3QPI	0x2b	0x20	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO5	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 5	0,1	0x0	null	0
R3QPI	0x2b	0x40	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO6	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 6	0,1	0x0	null	0
R3QPI	0x2b	0x80	UNC_R3_C_LO_AD_CREDITS_EMPTY.CBO7	No credits available to send to Cbox on the AD Ring (covers lower CBoxes); Cbox 7	0,1	0x0	null	0
R3QPI	0x2f	0x1	UNC_R3_HA_R2_BL_CREDITS_EMPTY.HA0	No credits available to send to either HA or R2 on the BL Ring; HA0	0,1	0x0	null	0
R3QPI	0x2f	0x2	UNC_R3_HA_R2_BL_CREDITS_EMPTY.HA1	No credits available to send to either HA or R2 on the BL Ring; HA1	0,1	0x0	null	0
R3QPI	0x2f	0x4	UNC_R3_HA_R2_BL_CREDITS_EMPTY.R2_NCB	No credits available to send to either HA or R2 on the BL Ring; R2 NCB Messages	0,1	0x0	null	0
R3QPI	0x2f	0x8	UNC_R3_HA_R2_BL_CREDITS_EMPTY.R2_NCS	No credits available to send to either HA or R2 on the BL Ring; R2 NCS Messages	0,1	0x0	null	0
R3QPI	0x29	0x2	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN0_HOM	No credits available to send to QPI0 on the AD Ring; VN0 HOM Messages	0,1	0x0	null	0
R3QPI	0x29	0x8	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN0_NDR	No credits available to send to QPI0 on the AD Ring; VN0 NDR Messages	0,1	0x0	null	0
R3QPI	0x29	0x4	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN0_SNP	No credits available to send to QPI0 on the AD Ring; VN0 SNP Messages	0,1	0x0	null	0
R3QPI	0x29	0x10	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN1_HOM	No credits available to send to QPI0 on the AD Ring; VN1 HOM Messages	0,1	0x0	null	0
R3QPI	0x29	0x40	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN1_NDR	No credits available to send to QPI0 on the AD Ring; VN1 NDR Messages	0,1	0x0	null	0
R3QPI	0x29	0x20	UNC_R3_QPI0_AD_CREDITS_EMPTY.VN1_SNP	No credits available to send to QPI0 on the AD Ring; VN1 SNP Messages	0,1	0x0	null	0
R3QPI	0x29	0x1	UNC_R3_QPI0_AD_CREDITS_EMPTY.VNA	No credits available to send to QPI0 on the AD Ring; VNA	0,1	0x0	null	0
R3QPI	0x2d	0x2	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN0_HOM	No credits available to send to QPI0 on the BL Ring; VN0 HOM Messages	0,1	0x0	null	0
R3QPI	0x2d	0x8	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN0_NDR	No credits available to send to QPI0 on the BL Ring; VN0 NDR Messages	0,1	0x0	null	0
R3QPI	0x2d	0x4	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN0_SNP	No credits available to send to QPI0 on the BL Ring; VN0 SNP Messages	0,1	0x0	null	0
R3QPI	0x2d	0x10	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN1_HOM	No credits available to send to QPI0 on the BL Ring; VN1 HOM Messages	0,1	0x0	null	0
R3QPI	0x2d	0x40	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN1_NDR	No credits available to send to QPI0 on the BL Ring; VN1 NDR Messages	0,1	0x0	null	0
R3QPI	0x2d	0x20	UNC_R3_QPI0_BL_CREDITS_EMPTY.VN1_SNP	No credits available to send to QPI0 on the BL Ring; VN1 SNP Messages	0,1	0x0	null	0
R3QPI	0x2d	0x1	UNC_R3_QPI0_BL_CREDITS_EMPTY.VNA	No credits available to send to QPI0 on the BL Ring; VNA	0,1	0x0	null	0
R3QPI	0x2a	0x2	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN0_HOM	No credits available to send to QPI1 on the AD Ring; VN0 HOM Messages	0,1	0x0	null	0
R3QPI	0x2a	0x8	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN0_NDR	No credits available to send to QPI1 on the AD Ring; VN0 NDR Messages	0,1	0x0	null	0
R3QPI	0x2a	0x4	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN0_SNP	No credits available to send to QPI1 on the AD Ring; VN0 SNP Messages	0,1	0x0	null	0
R3QPI	0x2a	0x10	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN1_HOM	No credits available to send to QPI1 on the AD Ring; VN1 HOM Messages	0,1	0x0	null	0
R3QPI	0x2a	0x40	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN1_NDR	No credits available to send to QPI1 on the AD Ring; VN1 NDR Messages	0,1	0x0	null	0
R3QPI	0x2a	0x20	UNC_R3_QPI1_AD_CREDITS_EMPTY.VN1_SNP	No credits available to send to QPI1 on the AD Ring; VN1 SNP Messages	0,1	0x0	null	0
R3QPI	0x2a	0x1	UNC_R3_QPI1_AD_CREDITS_EMPTY.VNA	No credits available to send to QPI1 on the AD Ring; VNA	0,1	0x0	null	0
R3QPI	0x2e	0x2	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN0_HOM	No credits available to send to QPI1 on the BL Ring; VN0 HOM Messages	0,1	0x0	null	0
R3QPI	0x2e	0x8	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN0_NDR	No credits available to send to QPI1 on the BL Ring; VN0 NDR Messages	0,1	0x0	null	0
R3QPI	0x2e	0x4	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN0_SNP	No credits available to send to QPI1 on the BL Ring; VN0 SNP Messages	0,1	0x0	null	0
R3QPI	0x2e	0x10	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN1_HOM	No credits available to send to QPI1 on the BL Ring; VN1 HOM Messages	0,1	0x0	null	0
R3QPI	0x2e	0x40	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN1_NDR	No credits available to send to QPI1 on the BL Ring; VN1 NDR Messages	0,1	0x0	null	0
R3QPI	0x2e	0x20	UNC_R3_QPI1_BL_CREDITS_EMPTY.VN1_SNP	No credits available to send to QPI1 on the BL Ring; VN1 SNP Messages	0,1	0x0	null	0
R3QPI	0x2e	0x1	UNC_R3_QPI1_BL_CREDITS_EMPTY.VNA	No credits available to send to QPI1 on the BL Ring; VNA	0,1	0x0	null	0
R3QPI	0x7	0x4	UNC_R3_RING_AD_USED.CCW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x7	0x8	UNC_R3_RING_AD_USED.CCW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x7	0x1	UNC_R3_RING_AD_USED.CW_VR0_EVEN	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x7	0x2	UNC_R3_RING_AD_USED.CW_VR0_ODD	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x8	0x4	UNC_R3_RING_AK_USED.CCW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x8	0x8	UNC_R3_RING_AK_USED.CCW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x8	0x1	UNC_R3_RING_AK_USED.CW_VR0_EVEN	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x8	0x2	UNC_R3_RING_AK_USED.CW_VR0_ODD	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x9	0x4	UNC_R3_RING_BL_USED.CCW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x9	0x8	UNC_R3_RING_BL_USED.CCW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Counterclockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x9	0x1	UNC_R3_RING_BL_USED.CW_VR0_EVEN	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Even ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0x9	0x2	UNC_R3_RING_BL_USED.CW_VR0_ODD	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.; Filters for the Clockwise and Odd ring polarity on Virtual Ring 0.	0,1,2	0x0	null	0
R3QPI	0xa	0xCC	UNC_R3_RING_IV_USED.CCW	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters for Counterclockwise polarity	0,1,2	0x0	null	0
R3QPI	0xa	0x33	UNC_R3_RING_IV_USED.CW	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters for Clockwise polarity	0,1,2	0x0	null	0
R3QPI	0x12	0x1	UNC_R3_RxR_BYPASSED.AD	Counts the number of times when the Ingress was bypassed and an incoming transaction was bypassed directly across the BGF and into the qfclk domain.	0,1	0x0	null	0
R3QPI	0x10	0x1	UNC_R3_RxR_CYCLES_NE.HOM	Counts the number of cycles when the QPI Ingress is not empty.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue occupancy.  Multiple ingress buffers can be tracked at a given time using multiple counters.; HOM Ingress Queue	0,1	0x0	null	0
R3QPI	0x10	0x4	UNC_R3_RxR_CYCLES_NE.NDR	Counts the number of cycles when the QPI Ingress is not empty.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue occupancy.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NDR Ingress Queue	0,1	0x0	null	0
R3QPI	0x10	0x2	UNC_R3_RxR_CYCLES_NE.SNP	Counts the number of cycles when the QPI Ingress is not empty.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue occupancy.  Multiple ingress buffers can be tracked at a given time using multiple counters.; SNP Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x8	UNC_R3_RxR_INSERTS.DRS	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; DRS Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x1	UNC_R3_RxR_INSERTS.HOM	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; HOM Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x10	UNC_R3_RxR_INSERTS.NCB	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCB Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x20	UNC_R3_RxR_INSERTS.NCS	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NCS Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x4	UNC_R3_RxR_INSERTS.NDR	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; NDR Ingress Queue	0,1	0x0	null	0
R3QPI	0x11	0x2	UNC_R3_RxR_INSERTS.SNP	Counts the number of allocations into the QPI Ingress.  This tracks one of the three rings that are used by the QPI agent.  This can be used in conjunction with the QPI Ingress Occupancy Accumulator event in order to calculate average queue latency.  Multiple ingress buffers can be tracked at a given time using multiple counters.; SNP Ingress Queue	0,1	0x0	null	0
R3QPI	0x13	0x8	UNC_R3_RxR_OCCUPANCY.DRS	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; DRS Ingress Queue	0	0x0	null	0
R3QPI	0x13	0x1	UNC_R3_RxR_OCCUPANCY.HOM	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; HOM Ingress Queue	0	0x0	null	0
R3QPI	0x13	0x10	UNC_R3_RxR_OCCUPANCY.NCB	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; NCB Ingress Queue	0	0x0	null	0
R3QPI	0x13	0x20	UNC_R3_RxR_OCCUPANCY.NCS	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; NCS Ingress Queue	0	0x0	null	0
R3QPI	0x13	0x4	UNC_R3_RxR_OCCUPANCY.NDR	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; NDR Ingress Queue	0	0x0	null	0
R3QPI	0x13	0x2	UNC_R3_RxR_OCCUPANCY.SNP	Accumulates the occupancy of a given QPI Ingress queue in each cycles.  This tracks one of the three ring Ingress buffers.  This can be used with the QPI Ingress Not Empty event to calculate average occupancy or the QPI Ingress Allocations event in order to calculate average queuing latency.; SNP Ingress Queue	0	0x0	null	0
R3QPI	0x28	0x1	UNC_R3_TxR_NACK_CCW.AD	BL CounterClockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x28	0x2	UNC_R3_TxR_NACK_CCW.AK	AD Clockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x28	0x4	UNC_R3_TxR_NACK_CCW.BL	AD CounterClockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x26	0x1	UNC_R3_TxR_NACK_CW.AD	AD Clockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x26	0x2	UNC_R3_TxR_NACK_CW.AK	AD CounterClockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x26	0x4	UNC_R3_TxR_NACK_CW.BL	BL Clockwise Egress Queue	0,1	0x0	null	0
R3QPI	0x37	0x8	UNC_R3_VN0_CREDITS_REJECT.DRS	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; Filter for Data Response (DRS).  DRS is generally used to transmit data with coherency.  For example, remote reads and writes, or cache to cache transfers will transmit their data using DRS.	0,1	0x0	null	0
R3QPI	0x37	0x1	UNC_R3_VN0_CREDITS_REJECT.HOM	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0x0	null	0
R3QPI	0x37	0x10	UNC_R3_VN0_CREDITS_REJECT.NCB	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; Filter for Non-Coherent Broadcast (NCB).  NCB is generally used to transmit data without coherency.  For example, non-coherent read data returns.	0,1	0x0	null	0
R3QPI	0x37	0x20	UNC_R3_VN0_CREDITS_REJECT.NCS	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; Filter for Non-Coherent Standard (NCS).  NCS is commonly used for ?	0,1	0x0	null	0
R3QPI	0x37	0x4	UNC_R3_VN0_CREDITS_REJECT.NDR	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; NDR packets are used to transmit a variety of protocol flits including grants and completions (CMP).	0,1	0x0	null	0
R3QPI	0x37	0x2	UNC_R3_VN0_CREDITS_REJECT.SNP	Number of times a request failed to acquire a DRS VN0 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN0 credit and is delayed.  This should generally be a rare situation.; Filter for Snoop (SNP) message class.  SNP is used for outgoing snoops.  Note that snoop responses flow on the HOM message class.	0,1	0x0	null	0
R3QPI	0x36	0x8	UNC_R3_VN0_CREDITS_USED.DRS	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; Filter for Data Response (DRS).  DRS is generally used to transmit data with coherency.  For example, remote reads and writes, or cache to cache transfers will transmit their data using DRS.	0,1	0x0	null	0
R3QPI	0x36	0x1	UNC_R3_VN0_CREDITS_USED.HOM	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0x0	null	0
R3QPI	0x36	0x10	UNC_R3_VN0_CREDITS_USED.NCB	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; Filter for Non-Coherent Broadcast (NCB).  NCB is generally used to transmit data without coherency.  For example, non-coherent read data returns.	0,1	0x0	null	0
R3QPI	0x36	0x20	UNC_R3_VN0_CREDITS_USED.NCS	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; Filter for Non-Coherent Standard (NCS).  NCS is commonly used for ?	0,1	0x0	null	0
R3QPI	0x36	0x4	UNC_R3_VN0_CREDITS_USED.NDR	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; NDR packets are used to transmit a variety of protocol flits including grants and completions (CMP).	0,1	0x0	null	0
R3QPI	0x36	0x2	UNC_R3_VN0_CREDITS_USED.SNP	Number of times a VN0 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN0.  VNA is a shared pool used to achieve high performance.  The VN0 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN0 if they fail.  This counts the number of times a VN0 credit was used.  Note that a single VN0 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN0 will only count a single credit even though it may use multiple buffers.; Filter for Snoop (SNP) message class.  SNP is used for outgoing snoops.  Note that snoop responses flow on the HOM message class.	0,1	0x0	null	0
R3QPI	0x39	0x8	UNC_R3_VN1_CREDITS_REJECT.DRS	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; Filter for Data Response (DRS).  DRS is generally used to transmit data with coherency.  For example, remote reads and writes, or cache to cache transfers will transmit their data using DRS.	0,1	0x0	null	0
R3QPI	0x39	0x1	UNC_R3_VN1_CREDITS_REJECT.HOM	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0x0	null	0
R3QPI	0x39	0x10	UNC_R3_VN1_CREDITS_REJECT.NCB	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; Filter for Non-Coherent Broadcast (NCB).  NCB is generally used to transmit data without coherency.  For example, non-coherent read data returns.	0,1	0x0	null	0
R3QPI	0x39	0x20	UNC_R3_VN1_CREDITS_REJECT.NCS	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; Filter for Non-Coherent Standard (NCS).  NCS is commonly used for ?	0,1	0x0	null	0
R3QPI	0x39	0x4	UNC_R3_VN1_CREDITS_REJECT.NDR	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; NDR packets are used to transmit a variety of protocol flits including grants and completions (CMP).	0,1	0x0	null	0
R3QPI	0x39	0x2	UNC_R3_VN1_CREDITS_REJECT.SNP	Number of times a request failed to acquire a VN1 credit.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This therefore counts the number of times when a request failed to acquire either a VNA or VN1 credit and is delayed.  This should generally be a rare situation.; Filter for Snoop (SNP) message class.  SNP is used for outgoing snoops.  Note that snoop responses flow on the HOM message class.	0,1	0x0	null	0
R3QPI	0x38	0x8	UNC_R3_VN1_CREDITS_USED.DRS	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; Filter for Data Response (DRS).  DRS is generally used to transmit data with coherency.  For example, remote reads and writes, or cache to cache transfers will transmit their data using DRS.	0,1	0x0	null	0
R3QPI	0x38	0x1	UNC_R3_VN1_CREDITS_USED.HOM	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0x0	null	0
R3QPI	0x38	0x10	UNC_R3_VN1_CREDITS_USED.NCB	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; Filter for Non-Coherent Broadcast (NCB).  NCB is generally used to transmit data without coherency.  For example, non-coherent read data returns.	0,1	0x0	null	0
R3QPI	0x38	0x20	UNC_R3_VN1_CREDITS_USED.NCS	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; Filter for Non-Coherent Standard (NCS).  NCS is commonly used for ?	0,1	0x0	null	0
R3QPI	0x38	0x4	UNC_R3_VN1_CREDITS_USED.NDR	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; NDR packets are used to transmit a variety of protocol flits including grants and completions (CMP).	0,1	0x0	null	0
R3QPI	0x38	0x2	UNC_R3_VN1_CREDITS_USED.SNP	Number of times a VN1 credit was used on the DRS message channel.  In order for a request to be transferred across QPI, it must be guaranteed to have a flit buffer on the remote socket to sink into.  There are two credit pools, VNA and VN1.  VNA is a shared pool used to achieve high performance.  The VN1 pool has reserved entries for each message class and is used to prevent deadlock.  Requests first attempt to acquire a VNA credit, and then fall back to VN1 if they fail.  This counts the number of times a VN1 credit was used.  Note that a single VN1 credit holds access to potentially multiple flit buffers.  For example, a transfer that uses VNA could use 9 flit buffers and in that case uses 9 credits.  A transfer on VN1 will only count a single credit even though it may use multiple buffers.; Filter for Snoop (SNP) message class.  SNP is used for outgoing snoops.  Note that snoop responses flow on the HOM message class.	0,1	0x0	null	0
R3QPI	0x33	0x0	UNC_R3_VNA_CREDITS_ACQUIRED	Number of QPI VNA Credit acquisitions.  This event can be used in conjunction with the VNA In-Use Accumulator to calculate the average lifetime of a credit holder.  VNA credits are used by all message classes in order to communicate across QPI.  If a packet is unable to acquire credits, it will then attempt to use credts from the VN0 pool.  Note that a single packet may require multiple flit buffers (i.e. when data is being transfered).  Therefore, this event will increment by the number of credits acquired in each cycle.  Filtering based on message class is not provided.  One can count the number of packets transfered in a given message class using an qfclk event.	0,1	0x0	null	0
R3QPI	0x34	0x8	UNC_R3_VNA_CREDITS_REJECT.DRS	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; Filter for Data Response (DRS).  DRS is generally used to transmit data with coherency.  For example, remote reads and writes, or cache to cache transfers will transmit their data using DRS.	0,1	0x0	null	0
R3QPI	0x34	0x1	UNC_R3_VNA_CREDITS_REJECT.HOM	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0x0	null	0
R3QPI	0x34	0x10	UNC_R3_VNA_CREDITS_REJECT.NCB	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; Filter for Non-Coherent Broadcast (NCB).  NCB is generally used to transmit data without coherency.  For example, non-coherent read data returns.	0,1	0x0	null	0
R3QPI	0x34	0x20	UNC_R3_VNA_CREDITS_REJECT.NCS	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; Filter for Non-Coherent Standard (NCS).	0,1	0x0	null	0
R3QPI	0x34	0x4	UNC_R3_VNA_CREDITS_REJECT.NDR	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; NDR packets are used to transmit a variety of protocol flits including grants and completions (CMP).	0,1	0x0	null	0
R3QPI	0x34	0x2	UNC_R3_VNA_CREDITS_REJECT.SNP	Number of attempted VNA credit acquisitions that were rejected because the VNA credit pool was full (or almost full).  It is possible to filter this event by message class.  Some packets use more than one flit buffer, and therefore must acquire multiple credits.  Therefore, one could get a reject even if the VNA credits were not fully used up.  The VNA pool is generally used to provide the bulk of the QPI bandwidth (as opposed to the VN0 pool which is used to guarantee forward progress).  VNA credits can run out if the flit buffer on the receiving side starts to queue up substantially.  This can happen if the rest of the uncore is unable to drain the requests fast enough.; Filter for Snoop (SNP) message class.  SNP is used for outgoing snoops.  Note that snoop responses flow on the HOM message class.	0,1	0x0	null	0
R3QPI	0x31	0x0	UNC_R3_VNA_CREDIT_CYCLES_OUT	Number of QPI uclk cycles when the transmitted has no VNA credits available and therefore cannot send any requests on this channel.  Note that this does not mean that no flits can be transmitted, as those holding VN0 credits will still (potentially) be able to transmit.  Generally it is the goal of the uncore that VNA credits should not run out, as this can substantially throttle back useful QPI bandwidth.	0,1	0x0	null	0
R3QPI	0x32	0x0	UNC_R3_VNA_CREDIT_CYCLES_USED	Number of QPI uclk cycles with one or more VNA credits in use.  This event can be used in conjunction with the VNA In-Use Accumulator to calculate the average number of used VNA credits.	0,1	0x0	null	0
R3QPI	0x7	0x33	UNC_R3_RING_AD_USED.CW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0x7	0xCC	UNC_R3_RING_AD_USED.CCW	Counts the number of cycles that the AD ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0x8	0x33	UNC_R3_RING_AK_USED.CW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0x8	0xCC	UNC_R3_RING_AK_USED.CCW	Counts the number of cycles that the AK ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0x9	0x33	UNC_R3_RING_BL_USED.CW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0x9	0xCC	UNC_R3_RING_BL_USED.CCW	Counts the number of cycles that the BL ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sunk, but does not include when packets are being sent from the ring stop.	0,1,2	0	null	0
R3QPI	0xA	0xFF	UNC_R3_RING_IV_USED.ANY	Counts the number of cycles that the IV ring is being used at this ring stop.  This includes when packets are passing by and when packets are being sent, but does not include when packets are being sunk into the ring stop.  The IV ring is unidirectional.  Whether UP or DN is used is dependent on the system programming.  Thereofore, one should generally set both the UP and DN bits for a given polarity (or both) at a given time.; Filters any polarity	0,1,2	0	null	0
R3QPI	0x12	0x0	UNC_R3_RxR_AD_BYPASSED	Counts the number of times when the AD Ingress was bypassed and an incoming transaction was bypassed directly across the BGF and into the qfclk domain.	0,1	0	null	0
R3QPI	0x33	0x1	UNC_R3_VNA_CREDITS_ACQUIRED.AD	Number of QPI VNA Credit acquisitions.  This event can be used in conjunction with the VNA In-Use Accumulator to calculate the average lifetime of a credit holder.  VNA credits are used by all message classes in order to communicate across QPI.  If a packet is unable to acquire credits, it will then attempt to use credts from the VN0 pool.  Note that a single packet may require multiple flit buffers (i.e. when data is being transfered).  Therefore, this event will increment by the number of credits acquired in each cycle.  Filtering based on message class is not provided.  One can count the number of packets transfered in a given message class using an qfclk event.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0	null	0
R3QPI	0x33	0x4	UNC_R3_VNA_CREDITS_ACQUIRED.BL	Number of QPI VNA Credit acquisitions.  This event can be used in conjunction with the VNA In-Use Accumulator to calculate the average lifetime of a credit holder.  VNA credits are used by all message classes in order to communicate across QPI.  If a packet is unable to acquire credits, it will then attempt to use credts from the VN0 pool.  Note that a single packet may require multiple flit buffers (i.e. when data is being transfered).  Therefore, this event will increment by the number of credits acquired in each cycle.  Filtering based on message class is not provided.  One can count the number of packets transfered in a given message class using an qfclk event.; Filter for the Home (HOM) message class.  HOM is generally used to send requests, request responses, and snoop responses.	0,1	0	null	0
UBOX	0x42	0x8	UNC_U_EVENT_MSG.DOORBELL_RCVD	Virtual Logical Wire (legacy) message were received from Uncore.   Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x42	0x10	UNC_U_EVENT_MSG.INT_PRIO	Virtual Logical Wire (legacy) message were received from Uncore.   Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x42	0x4	UNC_U_EVENT_MSG.IPI_RCVD	Virtual Logical Wire (legacy) message were received from Uncore.   Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x42	0x2	UNC_U_EVENT_MSG.MSI_RCVD	Virtual Logical Wire (legacy) message were received from Uncore.   Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x42	0x1	UNC_U_EVENT_MSG.VLW_RCVD	Virtual Logical Wire (legacy) message were received from Uncore.   Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x41	0x2	UNC_U_FILTER_MATCH.DISABLE	Filter match per thread (w/ or w/o Filter Enable).  Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x41	0x1	UNC_U_FILTER_MATCH.ENABLE	Filter match per thread (w/ or w/o Filter Enable).  Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	UBoxFilter[3:0]	0
UBOX	0x41	0x8	UNC_U_FILTER_MATCH.U2C_DISABLE	Filter match per thread (w/ or w/o Filter Enable).  Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	null	0
UBOX	0x41	0x4	UNC_U_FILTER_MATCH.U2C_ENABLE	Filter match per thread (w/ or w/o Filter Enable).  Specify the thread to filter on using NCUPMONCTRLGLCTR.ThreadID.	0,1	0x0	UBoxFilter[3:0]	0
UBOX	0x44	0x0	UNC_U_LOCK_CYCLES	Number of times an IDI Lock/SplitLock sequence was started	0,1	0x0	null	0
UBOX	0x45	0x1	UNC_U_PHOLD_CYCLES.ASSERT_TO_ACK	PHOLD cycles.  Filter from source CoreID.	0,1	0x0	null	0
UBOX	0x43	0x10	UNC_U_U2C_EVENTS.CMC	Events coming from Uncore can be sent to one or all cores	0,1	0x0	null	0
UBOX	0x43	0x4	UNC_U_U2C_EVENTS.LIVELOCK	Events coming from Uncore can be sent to one or all cores; Filter by core	0,1	0x0	null	0
UBOX	0x43	0x8	UNC_U_U2C_EVENTS.LTERROR	Events coming from Uncore can be sent to one or all cores; Filter by core	0,1	0x0	null	0
UBOX	0x43	0x1	UNC_U_U2C_EVENTS.MONITOR_T0	Events coming from Uncore can be sent to one or all cores; Filter by core	0,1	0x0	null	0
UBOX	0x43	0x2	UNC_U_U2C_EVENTS.MONITOR_T1	Events coming from Uncore can be sent to one or all cores; Filter by core	0,1	0x0	null	0
UBOX	0x43	0x80	UNC_U_U2C_EVENTS.OTHER	Events coming from Uncore can be sent to one or all cores; PREQ, PSMI, P2U, Thermal, PCUSMI, PMI	0,1	0x0	null	0
UBOX	0x43	0x40	UNC_U_U2C_EVENTS.TRAP	Events coming from Uncore can be sent to one or all cores	0,1	0x0	null	0
UBOX	0x43	0x20	UNC_U_U2C_EVENTS.UMC	Events coming from Uncore can be sent to one or all cores	0,1	0x0	null	0
UBOX	0x46	0x0	UNC_U_RACU_REQUESTS	0	0,1	0	null	0
UBOX	0x0	0x0	UNC_U_CLOCKTICKS	0	0,1	0	null	0
