#![allow(dead_code)]
#![allow(unused_imports)]
use crate::tasks::current_user_task;

extern crate alloc;

use alloc::sync::Arc;

use alloc::{
    string::{String, ToString},
    vec::Vec,
};
use devices::utils::get_char;
use executor::{current_task, release_task, task::TaskType, tid2task, yield_now, TASK_MAP};
use fs::{file::File, FileType, OpenFlags};
use log::debug;
use polyhal::{debug_console::DebugConsole, instruction::shutdown};
use vfscore::INodeInterface;

use crate::tasks::add_user_task;
use executor::AsyncTask;
use sync::Mutex;

use crate::tasks::exec_with_process;
use crate::user::entry::user_entry;
use alloc::sync::Weak;
use executor::thread;
use fs::pathbuf::PathBuf;

use super::UserTask;

/*Add：全局配置libc.so的路径 */
static LIBC_PATH: Mutex<String> = Mutex::new(String::new());
static GLIBC_PATH: Mutex<String> = Mutex::new(String::new());
static DYN_PATH: Mutex<String> = Mutex::new(String::new());

pub fn set_libc_path(path: String) {
    *LIBC_PATH.lock() = path;
}

pub fn get_libc_path() -> String {
    LIBC_PATH.lock().clone()
}

pub fn set_glibc_path(path: String) {
    *GLIBC_PATH.lock() = path;
}

pub fn get_glibc_path() -> String {
    GLIBC_PATH.lock().clone()
}

pub fn set_dyn_path(path: String) {
    *DYN_PATH.lock() = path;
}

pub fn get_dyn_path() -> String {
    DYN_PATH.lock().clone()
}

fn clear() {
    DebugConsole::putchar(0x1b);
    DebugConsole::putchar(0x5b);
    DebugConsole::putchar(0x48);
    DebugConsole::putchar(0x1b);
    DebugConsole::putchar(0x5b);
    DebugConsole::putchar(0x32);
    DebugConsole::putchar(0x4a);
}

async fn kill_all_tasks() {
    TASK_MAP.lock().values().into_iter().for_each(|task| {
        task.upgrade().inspect(|x| {
            if x.get_task_type() == TaskType::MonolithicTask {
                x.exit(100)
            }
        });
    });
}

/*async fn command(cmd: &str) {
    let mut args: Vec<&str> = cmd.split(" ").filter(|x| *x != "").collect();
    debug!("cmd: {}  args: {:?}", cmd, args);
    let filename = args.drain(..1).last().unwrap();
    match File::open(filename.into(), OpenFlags::O_RDONLY) {
        Ok(_) => {
            info!("exec: {}", filename);
            let mut args_extend = vec![filename];
            args_extend.extend(args.into_iter());
            let task_id = add_user_task(&filename, args_extend, Vec::new()).await;
            let task = tid2task(task_id).unwrap();
            loop {
                if task.exit_code().is_some() {
                    release_task(task_id);
                    break;
                }
                yield_now().await;
            }
            // syscall(SYS_WAIT4, [0,0,0,0,0,0,0])
            //     .await
            //     .expect("can't wait a pid");
        }
        Err(_) => {
            println!("unknown command: {}", cmd);
        }
    }
}*/
async fn command(cmd: &str, work_dir: PathBuf) {
    // 改进的参数解析，支持引号
    let mut args = Vec::new();
    let mut current_arg = String::new();
    let mut in_quotes = false;
    let mut escape_next = false;

    for ch in cmd.chars() {
        if escape_next {
            current_arg.push(ch);
            escape_next = false;
        } else if ch == '\\' {
            escape_next = true;
        } else if ch == '\'' || ch == '"' {
            in_quotes = !in_quotes;
        } else if ch == ' ' && !in_quotes {
            if !current_arg.is_empty() {
                args.push(current_arg.clone());
                current_arg.clear();
            }
        } else {
            current_arg.push(ch);
        }
    }

    // 添加最后一个参数
    if !current_arg.is_empty() {
        args.push(current_arg);
    }

    // 转换为字符串切片
    let args_str: Vec<&str> = args.iter().map(|s| s.as_str()).collect();
    debug!("cmd: {}  args: {:?}", cmd, args_str);

    if args_str.is_empty() {
        println!("empty command");
        return;
    }

    let filename = &args_str[0];
    let remaining_args = &args_str[1..];
    match File::open((*filename).into(), OpenFlags::O_RDONLY) {
        Ok(_) => {
            info!("exec: {}", filename);
            let mut args_extend = vec![*filename];
            args_extend.extend(remaining_args.iter().copied());
            // 在这里克隆 work_dir，以便后续使用
            let work_dir_clone = work_dir.clone();
            // Use custom working directory to create task
            let curr_task = current_task();
            let task = UserTask::new(Weak::new(), work_dir);
            task.before_run();
            match exec_with_process(
                task.clone(),
                work_dir_clone, // 使用传入的工作目录，而不是空的PathBuf
                String::from(*filename),
                args_extend.into_iter().map(String::from).collect(),
                Vec::<&str>::new().into_iter().map(String::from).collect(),
            )
            .await
            {
                Ok(_) => {}
                Err(e) => {
                    println!("exec failed for {}: {:?}", filename, e);
                    return;
                }
            }
            curr_task.before_run();
            let task_id = task.get_task_id();
            thread::spawn(task.clone(), user_entry());

            let task = tid2task(task_id).unwrap();
            loop {
                if task.exit_code().is_some() {
                    release_task(task_id);
                    break;
                }
                yield_now().await;
            }

            // 额外等待确保进程完全清理
            for _ in 0..100000 {
                yield_now().await;
            }

            // 强制内存屏障确保状态一致性
            core::sync::atomic::fence(core::sync::atomic::Ordering::SeqCst);
        }
        Err(_) => {
            println!("unknown command: {}", cmd);
        }
    }
}

/// 专门用于iperf测试的命令执行，包含额外的网络隔离和清理
async fn command_iperf(cmd: &str, work_dir: PathBuf) {
    log::warn!("Starting iperf test with enhanced isolation: {}", cmd);

    // 第一步：强制清理所有现有任务
    kill_all_tasks().await;

    // 第二步：内存屏障确保所有状态同步
    core::sync::atomic::fence(core::sync::atomic::Ordering::SeqCst);
    for _ in 0..100000 {
        core::hint::spin_loop();
    }

    // 第三步：执行iperf测试
    command(cmd, work_dir.clone()).await;

    // 第四步：测试后强制清理
    kill_all_tasks().await;

    // 第五步：额外的清理等待
    core::sync::atomic::fence(core::sync::atomic::Ordering::SeqCst);
    for _ in 0..200000 {
        core::hint::spin_loop();
    }

    log::warn!("Completed iperf test with enhanced isolation");
}

pub async fn initproc() {
    #[cfg(not(target_arch = "loongarch64"))]
    {
        set_libc_path("/musl/lib/libc.so".to_string());
        set_dyn_path("/musl/lib/libc.so".to_string());
        //set_dyn_path("/glibc/lib/ld-linux-riscv64-lp64d.so.1".to_string());

        // 创建必要的链接以支持busybox测试
        let home_dir = PathBuf::from("/musl/basic");
        command("/musl/busybox mkdir -p /bin", home_dir.clone()).await;

        // 创建常用命令的链接，确保基本命令可用
        command("/musl/busybox cp /musl/busybox /sleep", home_dir.clone()).await;
        command(
            "/musl/busybox cp /musl/busybox /bin/sleep",
            home_dir.clone(),
        )
        .await;
        command("/musl/busybox cp /musl/busybox /bin/cp", home_dir.clone()).await;
        command("/musl/busybox cp /musl/busybox /bin/ls", home_dir.clone()).await;
        command(
            "/musl/busybox cp /musl/busybox /bin/mkdir",
            home_dir.clone(),
        )
        .await;
        command("/musl/busybox cp /musl/busybox /bin/mv", home_dir.clone()).await;
        command("/musl/busybox cp /musl/busybox /bin/rm", home_dir.clone()).await;
        command("/musl/busybox cp /musl/busybox /bin/cat", home_dir.clone()).await;
        command("/musl/busybox cp /musl/busybox /bin/echo", home_dir.clone()).await;
        command(
            "/musl/busybox cp /musl/busybox /bin/chmod",
            home_dir.clone(),
        )
        .await;

        command("/musl/busybox chmod +x /sleep", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/sleep", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/cp", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/ls", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/mkdir", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/mv", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/rm", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/cat", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/echo", home_dir.clone()).await;
        command("/musl/busybox chmod +x /bin/chmod", home_dir.clone()).await;
        command(
            "/musl/busybox cp /glibc/lib/libm.so /glibc/lib/libm.so.6",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/busybox cp /glibc/lib/libc.so /glibc/lib/libc.so.6",
            home_dir.clone(),
        )
        .await;

        command(
            "/musl/busybox echo #### OS COMP TEST GROUP START basic-musl ####",
            home_dir.clone(),
        )
        .await;
        command("/musl/busybox sh /musl/basic/run-all.sh", home_dir.clone()).await;
        command(
            "/musl/busybox echo #### OS COMP TEST GROUP END basic-musl ####",
            home_dir.clone(),
        )
        .await;
        let home_dir = PathBuf::from("/musl");

        // // 为LTP测试准备环境
        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP START ltp-musl ####",
        //     home_dir.clone(),
        // )
        // .await;

        // // 确保LTP测试需要的目录存在并有正确权限
        // command("/musl/busybox mkdir -p /tmp", home_dir.clone()).await;
        // command("/musl/busybox mkdir -p /var/tmp", home_dir.clone()).await;
        // command("/musl/busybox chmod 1777 /tmp", home_dir.clone()).await;
        // command("/musl/busybox chmod 1777 /var/tmp", home_dir.clone()).await;

        // // 创建一些LTP测试可能需要的基础文件
        // command("/musl/busybox touch /tmp/.test_marker", home_dir.clone()).await;
        // command("/musl/busybox rm -f /tmp/.test_marker", home_dir.clone()).await;

        // //command("/musl/busybox sh ", home_dir.clone()).await;
        // // Run all LTP tests
        // run_ltp_tests(home_dir.clone()).await;

        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP END ltp-musl ####",
        //     home_dir.clone(),
        // )
        // .await;

        //使用专门的iperf测试函数，包含增强的隔离机制
        command_iperf("/musl/busybox sh iperf_testcode.sh", home_dir.clone()).await;
        command(
            "/musl/busybox echo #### OS COMP TEST GROUP START lmbench-musl ####",
            home_dir.clone(),
        )
        .await;

        command("/musl/busybox echo latency measurements", home_dir.clone()).await;
        command("/musl/lmbench_all lat_syscall -P 1 null", home_dir.clone()).await;
        command("/musl/lmbench_all lat_syscall -P 1 read", home_dir.clone()).await;
        command("/musl/lmbench_all lat_syscall -P 1 write", home_dir.clone()).await;

        command("/musl/busybox mkdir -p /var/tmp", home_dir.clone()).await;
        command("/musl/busybox touch /var/tmp/lmbench", home_dir.clone()).await;

        command(
            "/musl/lmbench_all lat_syscall -P 1 stat /var/tmp/lmbench",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all lat_syscall -P 1 fstat /var/tmp/lmbench",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all lat_syscall -P 1 open /var/tmp/lmbench",
            home_dir.clone(),
        )
        .await;

        command(
            "/musl/lmbench_all lat_select -n 100 -P 1 file",
            home_dir.clone(),
        )
        .await;

        command("/musl/lmbench_all lat_sig -P 1 install", home_dir.clone()).await;
        command("/musl/lmbench_all lat_sig -P 1 catch", home_dir.clone()).await;
        command(
            "/musl/lmbench_all lat_sig -P 1 prot lat_sig",
            home_dir.clone(),
        )
        .await;

        command("/musl/lmbench_all lat_pipe -P 1", home_dir.clone()).await;

        command("/musl/lmbench_all lat_proc -P 1 fork", home_dir.clone()).await;
        //command("/musl/lmbench_all lat_proc -P 1 exec", home_dir.clone()).await;

        command("/musl/busybox cp hello /tmp", home_dir.clone()).await;
        //command("/musl/lmbench_all lat_proc -P 1 shell", home_dir.clone()).await;

        // 先创建目录
        command("/musl/busybox mkdir -p /var/tmp", home_dir.clone()).await;

        // 现在使用改进的command函数，可以正确处理引号
        command("/musl/lmbench_all lmdd label=\"File /var/tmp/XXX write bandwidth:\" of=/var/tmp/XXX move=1m fsync=1 print=3", home_dir.clone()).await;
        command(
            "/musl/lmbench_all lat_pagefault -P 1 /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all lat_mmap -P 1 512k /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;

        command("/musl/busybox echo file system latency", home_dir.clone()).await;
        command("/musl/lmbench_all lat_fs /var/tmp", home_dir.clone()).await;

        command(
            "/musl/busybox echo Bandwidth measurements",
            home_dir.clone(),
        )
        .await;
        command("/musl/lmbench_all bw_pipe -P 1", home_dir.clone()).await;
        command(
            "/musl/lmbench_all bw_file_rd -P 1 512k io_only /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all bw_file_rd -P 1 512k open2close /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all bw_mmap_rd -P 1 512k mmap_only /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all bw_mmap_rd -P 1 512k open2close /var/tmp/XXX",
            home_dir.clone(),
        )
        .await;

        command(
            "/musl/busybox echo context switch overhead",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/lmbench_all lat_ctx -P 1 -s 32 2 4 8 16 24 32 64 96",
            home_dir.clone(),
        )
        .await;

        command(
            "/musl/busybox echo #### OS COMP TEST GROUP END lmbench-musl ####",
            home_dir.clone(),
        )
        .await;
        command(
            "/musl/busybox sh /musl/iozone_testcode.sh",
            home_dir.clone(),
        )
        .await;
        //command("/musl/busybox sh ", home_dir.clone()).await;
        command(
            "/musl/busybox sh /musl/libcbench_testcode.sh",
            home_dir.clone(),
        )
        .await;
        command("/musl/busybox sh busybox_testcode.sh", home_dir.clone()).await;

        command("/musl/busybox sh lua_testcode.sh", home_dir.clone()).await;
        //command("/musl/busybox sh libctest_testcode.sh", home_dir.clone()).await;
        command(
            "/musl/busybox echo #### OS COMP TEST GROUP START libctest-musl ####",
            home_dir.clone(),
        )
        .await;
        let musl_exclude = vec![(
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_robust_detach",
            home_dir.clone(),
        )];
        run_musl_tests(home_dir.clone(), musl_exclude).await;
        command(
            "/musl/busybox echo #### OS COMP TEST GROUP END libctest-musl ####",
            home_dir.clone().clone(),
        )
        .await;

        command(
            "/musl/busybox echo #### OS COMP TEST GROUP END libctest-musl ####",
            home_dir.clone().clone(),
        )
        .await;

        set_libc_path("/glibc/lib".to_string());
        set_dyn_path("/glibc/lib/ld-linux-riscv64-lp64d.so.1".to_string());
        let glibc_home_dir = PathBuf::from("/glibc/basic");
        // 为glibc环境创建链接
        command("/glibc/busybox mkdir -p /bin", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox cp /glibc/busybox /sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox cp /glibc/busybox /bin/sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/busybox chmod +x /sleep", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod +x /bin/sleep", glibc_home_dir.clone()).await;
        //command("/musl/busybox sh", glibc_home_dir.clone()).await;
        // command(
        //     "/glibc/busybox sh /glibc/iozone_testcode.sh",
        //     glibc_home_dir.clone(),
        // )
        // .await;
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP START basic-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox sh /glibc/basic/run-all.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP END basic-glibc ####",
            glibc_home_dir.clone().clone(),
        )
        .await;

        let glibc_home_dir = PathBuf::from("/glibc");
        // 确保glibc目录也有正确的链接
        command("/glibc/busybox mkdir -p /bin", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox cp /glibc/busybox /sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox cp /glibc/busybox /bin/sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/busybox chmod +x /sleep", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod +x /bin/sleep", glibc_home_dir.clone()).await;

        //command("/musl/busybox sh ", glibc_home_dir.clone()).await;

        // 为LTP测试准备环境
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP START ltp-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;

        // 确保LTP测试需要的目录存在并有正确权限
        command("/glibc/busybox mkdir -p /tmp", glibc_home_dir.clone()).await;
        command("/glibc/busybox mkdir -p /var/tmp", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod 1777 /tmp", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod 1777 /var/tmp", glibc_home_dir.clone()).await;

        // 创建一些LTP测试可能需要的基础文件
        command(
            "/glibc/busybox touch /tmp/.test_marker",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox rm -f /tmp/.test_marker",
            glibc_home_dir.clone(),
        )
        .await;

        //command("/glibc/busybox sh ", glibc_home_dir.clone()).await;
        //command("/glibc/busybox sh ltp_testcode.sh", glibc_home_dir.clone()).await;
        run_ltp_tests_glibc(glibc_home_dir.clone()).await;

        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP END ltp-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;

        // 使用专门的iperf测试函数，包含增强的隔离机制 (glibc)
        // command_iperf(
        //     "/glibc/busybox sh iperf_testcode.sh",
        //     glibc_home_dir.clone(),
        // )
        // .await;
        command("/glibc/busybox sh lua_testcode.sh", glibc_home_dir.clone()).await;
                command(
            "/glibc/busybox sh /glibc/libcbench_testcode.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox sh busybox_testcode.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP START lmbench-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/busybox echo latency measurements",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_syscall -P 1 null",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_syscall -P 1 read",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_syscall -P 1 write",
            glibc_home_dir.clone(),
        )
        .await;

        command("/glibc/busybox mkdir -p /var/tmp", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox touch /var/tmp/lmbench",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/lmbench_all lat_syscall -P 1 stat /var/tmp/lmbench",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_syscall -P 1 fstat /var/tmp/lmbench",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_syscall -P 1 open /var/tmp/lmbench",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/lmbench_all lat_select -n 100 -P 1 file",
            glibc_home_dir.clone(),
        )
        .await;

        // command(
        //     "/glibc/lmbench_all lat_sig -P 1 install",
        //     glibc_home_dir.clone(),
        // )
        // .await;
        // // command(
        // //     "/glibc/lmbench_all lat_sig -P 1 catch",
        // //     glibc_home_dir.clone(),
        // // )
        // // .await;
        // command(
        //     "/glibc/lmbench_all lat_sig -P 1 prot lat_sig",
        //     glibc_home_dir.clone(),
        // )
        // .await;

        command("/glibc/lmbench_all lat_pipe -P 1", glibc_home_dir.clone()).await;

        command(
            "/glibc/lmbench_all lat_proc -P 1 fork",
            glibc_home_dir.clone(),
        )
        .await;
     command(
            "/glibc/busybox echo Bandwidth measurements",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/lmbench_all bw_pipe -P 1", glibc_home_dir.clone()).await;
        command(
            "/glibc/lmbench_all bw_file_rd -P 1 512k io_only /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all bw_file_rd -P 1 512k open2close /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all bw_mmap_rd -P 1 512k mmap_only /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all bw_mmap_rd -P 1 512k open2close /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/busybox echo context switch overhead",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_ctx -P 1 -s 32 2 4 8 16 24 32 64 96",
            glibc_home_dir.clone(),
        )
        .await;

        // //command("/glibc/lmbench_all lat_proc -P 1 exec", glibc_home_dir.clone()).await;

        command("/glibc/busybox cp hello /tmp", glibc_home_dir.clone()).await;
        //command("/glibc/lmbench_all lat_proc -P 1 shell", glibc_home_dir.clone()).await;

        // 先创建目录
        command("/glibc/busybox mkdir -p /var/tmp", glibc_home_dir.clone()).await;

        // 现在使用改进的command函数，可以正确处理引号
        command("/glibc/lmbench_all lmdd label=\"File /var/tmp/XXX write bandwidth:\" of=/var/tmp/XXX move=1m fsync=1 print=3", glibc_home_dir.clone()).await;
        command(
            "/glibc/lmbench_all lat_pagefault -P 1 /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/lmbench_all lat_mmap -P 1 512k /var/tmp/XXX",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/busybox echo file system latency",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/lmbench_all lat_fs /var/tmp", glibc_home_dir.clone()).await;

       
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP END lmbench-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;
        //command("/glibc/busybox sh lmbench_testcode.sh", glibc_home_dir.clone()).await;


        //command("/musl/busybox sh run-dynamic-all.sh", home_dir.clone()).await;
        //command("/musl/busybox sh run-static-all.sh", home_dir.clone()).await;
        // command("/musl/busybox sh run-dynamic.sh", home_dir.clone()).await;
        // command("/musl/busybox sh run-static.sh", home_dir.clone()).await;
        // command("/musl/busybox sh cyclictest_testcode.sh", home_dir.clone()).await;

        // command("/musl/busybox sh unixbench_testcode.sh", home_dir.clone()).await;
        //command("/musl/busybox sh lmbench_testcode.sh", home_dir.clone()).await;
        // command("/musl/busybox sh iperf_testcode.sh", home_dir.clone()).await;
        // command("/musl/busybox sh multi.sh", home_dir.clone()).await;
        // command("/musl/busybox sh iozone_testcode.sh", home_dir.clone()).await;

        // command(
        //     "/glibc/busybox sh cyclictest_testcode.sh",
        //     glibc_home_dir.clone(),
        // )
        // .await;
    }

    #[cfg(target_arch = "loongarch64")]
    {
        // set_libc_path("/musl/lib/libc.so".to_string());
        // set_dyn_path("/musl/lib/libc.so".to_string());
        // println!("start kernel tasks");

        // // 创建必要的链接以支持busybox测试
        // let home_dir = PathBuf::from("/musl/basic");
        // command("/musl/busybox mkdir -p /bin", home_dir.clone()).await;

        // // 创建常用命令的链接，确保基本命令可用
        // command("/musl/busybox cp /musl/busybox /sleep", home_dir.clone()).await;
        // command(
        //     "/musl/busybox cp /musl/busybox /bin/sleep",
        //     home_dir.clone(),
        // )
        // .await;
        // command("/musl/busybox cp /musl/busybox /bin/cp", home_dir.clone()).await;
        // command("/musl/busybox cp /musl/busybox /bin/ls", home_dir.clone()).await;
        // command(
        //     "/musl/busybox cp /musl/busybox /bin/mkdir",
        //     home_dir.clone(),
        // )
        // .await;
        // command("/musl/busybox cp /musl/busybox /bin/mv", home_dir.clone()).await;
        // command("/musl/busybox cp /musl/busybox /bin/rm", home_dir.clone()).await;
        // command("/musl/busybox cp /musl/busybox /bin/cat", home_dir.clone()).await;
        // command("/musl/busybox cp /musl/busybox /bin/echo", home_dir.clone()).await;
        // command(
        //     "/musl/busybox cp /musl/busybox /bin/chmod",
        //     home_dir.clone(),
        // )
        // .await;

        // command("/musl/busybox chmod +x /sleep", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/sleep", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/cp", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/ls", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/mkdir", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/mv", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/rm", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/cat", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/echo", home_dir.clone()).await;
        // command("/musl/busybox chmod +x /bin/chmod", home_dir.clone()).await;
        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP START basic-musl ####",
        //     home_dir.clone(),
        // )
        // .await;
        // command("/musl/busybox sh /musl/basic/run-all.sh", home_dir.clone()).await;
        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP END basic-musl ####",
        //     home_dir.clone(),
        // )
        // .await;
        // //let home_dir = PathBuf::from("/musl");
        // let home_dir = PathBuf::from("/musl");

        // // 为LTP测试准备环境 (LoongArch64)
        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP START ltp-musl ####",
        //     home_dir.clone(),
        // )
        // .await;

        // // 确保LTP测试需要的目录存在并有正确权限
        // command("/musl/busybox mkdir -p /tmp", home_dir.clone()).await;
        // command("/musl/busybox mkdir -p /var/tmp", home_dir.clone()).await;
        // command("/musl/busybox chmod 1777 /tmp", home_dir.clone()).await;
        // command("/musl/busybox chmod 1777 /var/tmp", home_dir.clone()).await;

        // // 创建一些LTP测试可能需要的基础文件
        // command("/musl/busybox touch /tmp/.test_marker", home_dir.clone()).await;
        // command("/musl/busybox rm -f /tmp/.test_marker", home_dir.clone()).await;

        // // Run all LTP tests
        // run_ltp_tests(home_dir.clone()).await;

        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP END ltp-musl ####",
        //     home_dir.clone(),
        // )
        // .await;

        // // 使用专门的iperf测试函数，包含增强的隔离机制 (loongarch64)
        // command_iperf("/musl/busybox sh iperf_testcode.sh", home_dir.clone()).await;
        // command("/musl/busybox sh lmbench_testcode.sh", home_dir.clone()).await;

        // command(
        //     "/musl/busybox sh /musl/iozone_testcode.sh",
        //     home_dir.clone(),
        // )
        // .await;
        // command("/musl/busybox sh libcbench_testcode.sh", home_dir.clone()).await;
        // command("/musl/busybox sh busybox_testcode.sh", home_dir.clone()).await;
        // command("/musl/busybox sh lua_testcode.sh", home_dir.clone()).await;

        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP START libctest-musl ####",
        //     home_dir.clone(),
        // )
        // .await;
        // let musl_exclude = vec![
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_robust_detach",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "setvbuf_unget",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_cancel_points",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_cancel",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_cond",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_tsd",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_cancel_sem_wait",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_cond_smasher",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_condattr_setclock",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_exit_cancel",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_once_deadlock",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-static.exe",
        //         "pthread_rwlock_ebusy",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_robust_detach",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "setvbuf_unget",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_cancel_points",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_cancel",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_cond",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_tsd",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_cancel_sem_wait",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_cond_smasher",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_condattr_setclock",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_exit_cancel",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_once_deadlock",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "pthread_rwlock_ebusy",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "sem_init",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "tls_init",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "tls_local_exec",
        //         home_dir.clone(),
        //     ),
        //     (
        //         "/musl/runtest.exe",
        //         "entry-dynamic.exe",
        //         "tls_get_new_dtv",
        //         home_dir.clone(),
        //     ),
        // ];
        // run_musl_tests(home_dir.clone(), musl_exclude).await;
        // command(
        //     "/musl/busybox echo #### OS COMP TEST GROUP END libctest-musl ####",
        //     home_dir.clone().clone(),
        // )
        // .await;

        // //command("/musl/busybox sh run-dynamic-all.sh", home_dir.clone()).await;
        // //command("/musl/busybox sh run-static-all.sh", home_dir.clone()).await;
        // // command("/musl/busybox sh run-dynamic.sh", home_dir.clone()).await;
        // // command("/musl/busybox sh run-static.sh", home_dir.clone()).await;
        // //command("/musl/busybox sh cyclictest_testcode.sh", home_dir.clone()).await;

        // // command("/musl/busybox sh unixbench_testcode.sh", home_dir.clone()).await;
        // //command("/musl/busybox sh lmbench_testcode.sh", home_dir.clone()).await;
        // // command("/musl/busybox sh iperf_testcode.sh", home_dir.clone()).await;
        // // command("/musl/busybox sh multi.sh", home_dir.clone()).await;
        // // command("/musl/busybox sh iozone_testcode.sh", home_dir.clone()).await;
        set_libc_path("/glibc/lib".to_string());
        set_dyn_path("/glibc/lib/ld-linux-loongarch-lp64d.so.1".to_string());
        let glibc_home_dir = PathBuf::from("/glibc/basic");
        // 为glibc环境创建链接
        command("/glibc/busybox mkdir -p /bin", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox cp /glibc/busybox /sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox cp /glibc/busybox /bin/sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/busybox chmod +x /sleep", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod +x /bin/sleep", glibc_home_dir.clone()).await;
        //command("/glibc/busybox sh", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP START basic-glibc ####",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox sh /glibc/basic/run-all.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox echo #### OS COMP TEST GROUP END basic-glibc ####",
            glibc_home_dir.clone().clone(),
        )
        .await;

        let glibc_home_dir = PathBuf::from("/glibc");
        // 确保glibc目录也有正确的链接
        command("/glibc/busybox mkdir -p /bin", glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox cp /glibc/busybox /sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox cp /glibc/busybox /bin/sleep",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/busybox chmod +x /sleep", glibc_home_dir.clone()).await;
        command("/glibc/busybox chmod +x /bin/sleep", glibc_home_dir.clone()).await;

        //command("/musl/busybox sh ", glibc_home_dir.clone()).await;
        run_ltp_tests_glibc(glibc_home_dir.clone()).await;
        command(
            "/glibc/busybox sh lmbench_testcode.sh",
            glibc_home_dir.clone(),
        )
        .await;

        command(
            "/glibc/busybox sh libcbench_testcode.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command(
            "/glibc/busybox sh busybox_testcode.sh",
            glibc_home_dir.clone(),
        )
        .await;
        command("/glibc/busybox sh lua_testcode.sh", glibc_home_dir.clone()).await;

        // command(
        //     "/glibc/busybox sh cyclictest_testcode.sh",
        //     glibc_home_dir.clone(),
        // )
        // .await;
    }
    println!("!TEST FINISH!");
    shutdown();

    // Shutdown if there just have blankkernel task.
    if TASK_MAP
        .lock()
        .values()
        .find(|x| {
            x.upgrade()
                .map(|x| x.get_task_type() != TaskType::BlankKernel)
                .unwrap_or(false)
        })
        .is_none()
    {
        shutdown();
    }
}
async fn run_ltp_tests(home_dir: PathBuf) {
    // Complete list of LTP tests (excluding .sh files)
    let ltp_tests = vec![
        //"fork_exec_loop",
        "abort01",
        // "abs01",
        // "accept01",
        // "accept02",
        // "accept03",
        // "accept4_01",
        //"access01",
        //"access02",
        // "access03",
        // "access04",
        // "acct01",
        // "acct02",
        // "acct02_helper",
        // "acl1",
        //"add_ipv6addr",
        // "add_key01",
        // "add_key02",
        // "add_key03",
        // "add_key04",
        // "add_key05",
        // "adjtimex01",
        // "adjtimex02",
        // "adjtimex03",
        // "af_alg01",
        // "af_alg02",
        // "af_alg03",
        // "af_alg04",
        // "af_alg05",
        // "af_alg06",
        // "af_alg07",
        //"aio-stress",
        //"aio01",
        //"aio02",
        //"aiocp",
        //"aiodio_append",
        //"aiodio_sparse",
        "alarm02",
        "alarm03",
        "alarm05",
        "alarm06",
        "alarm07",
        "arch_prctl01",
        "asapi_01",
        "asapi_02",
        "asapi_03",
        "aslr01",
        "atof01",
        "autogroup01",
        "bind01",
        "bind02",
        "bind03",
        "bind04",
        "bind05",
        "bind06",
        "block_dev",
        "bpf_map01",
        "bpf_prog01",
        "bpf_prog02",
        "bpf_prog03",
        "bpf_prog04",
        "bpf_prog05",
        "bpf_prog06",
        "bpf_prog07",
        "brk01",
        "brk02",
        // "cacheflush01",
        // "can_bcm01",
        // "can_filter",
        // "can_rcv_own_msgs",
        // "cap_bounds_r",
        // "cap_bounds_rw",
        // "cap_bset_inh_bounds",
        // "capget01",
        // "capget02",
        // "capset01",
        // "capset02",
        // "capset03",
        // "capset04",
        // "cfs_bandwidth01",
        // "cgroup_core01",
        // "cgroup_core02",
        // "cgroup_core03",
        // "cgroup_fj_proc",
        // "cgroup_regression_fork_processes",
        // "cgroup_regression_getdelays",
        // "cgroup_xattr",
        "chdir01",
        //"chdir04",
        //"check_envval",
        //"check_icmpv4_connectivity",
        //"check_icmpv6_connectivity",
        //"check_keepcaps",
        //"check_netem",
        //"check_pe",
        //"check_simple_capset",
        "chmod01",
        "chmod03",
        "chmod05",
        //"chmod06",
        //"chmod07",
        "chown01",
        "chown01_16",
        "chown02",
        "chown02_16",
        "chown03",
        "chown03_16",
        //"chown04",
        //"chown04_16",
        "chown05",
        "chown05_16",
        // "chroot01",
        // "chroot02",
        // "chroot03",
        // "chroot04",
        // "clock_adjtime01",
        // "clock_adjtime02",
        "clock_getres01",
        "clock_gettime01",
        "clock_gettime02",
        "clock_gettime03",
        "clock_gettime04",
        "clock_nanosleep01",
        "clock_nanosleep02",
        "clock_nanosleep03",
        "clock_nanosleep04",
        "clock_settime01",
        "clock_settime02",
        "clock_settime03",
        "clone01",
        "clone02",
        "clone03",
        //"clone04",
        //"clone05",
        "clone06",
        "clone07",
        "clone08",
        "clone09",
        "clone301",
        "clone302",
        "clone303",
        "close01",
        "close02",
        "close_range01",
        "close_range02",
        "confstr01",
        // "connect01",
        // "connect02",
        // "copy_file_range01",
        // "copy_file_range02",
        // "copy_file_range03",
        // "cpuacct_task",
        // "cpuctl_def_task01",
        // "cpuctl_def_task02",
        // "cpuctl_def_task03",
        // "cpuctl_def_task04",
        // "cpuctl_fj_cpu-hog",
        // "cpuctl_fj_simple_echo",
        // "cpuctl_latency_check_task",
        // "cpuctl_latency_test",
        // "cpuctl_test01",
        // "cpuctl_test02",
        // "cpuctl_test03",
        // "cpuctl_test04",
        // "cpufreq_boost",
        // "cpuhotplug_do_disk_write_loop",
        // "cpuhotplug_do_kcompile_loop",
        // "cpuhotplug_do_spin_loop",
        // "cpuhotplug_report_proc_interrupts",
        // "cpuset01",
        // "crash01",
        // "crash02",
        "creat01",
        "creat03",
        "creat04",
        //"creat05",
        //"creat06",
        //"creat07",
        //"creat07_child",
        // "creat08",
        // "creat09",
        // "create_datafile",
        //"create_file",
        // "crypto_user01",
        // "crypto_user02",
        // "cve-2014-0196",
        // "cve-2015-3290",
        // "cve-2016-10044",
        // "cve-2016-7042",
        // "cve-2016-7117",
        // "cve-2017-16939",
        // "cve-2017-17052",
        // "cve-2017-17053",
        // "cve-2017-2618",
        // "cve-2017-2671",
        // "cve-2022-4378",
        // "data",
        //"data_space",
        //"datafiles",
        // "delete_module01",
        // "delete_module02",
        // "delete_module03",
        // "dio_append",
        // "dio_read",
        // "dio_sparse",
        // "dio_truncate",
        // "diotest1",
        // "diotest2",
        // "diotest3",
        // "diotest4",
        // "diotest5",
        // "diotest6",
        // "dirty",
        // "dirtyc0w",
        // "dirtyc0w_child",
        // "dirtyc0w_shmem",
        // "dirtyc0w_shmem_child",
        // "dirtypipe",
        // "dma_thread_diotest",
        // "doio",
        "dup01",
        "dup02",
        "dup03",
        "dup04",
        "dup05",
        "dup06",
        "dup07",
        "dup201",
        "dup202",
        "dup203",
        "dup204",
        "dup205",
        "dup206",
        "dup207",
        "dup3_01",
        "dup3_02",
        "ebizzy",
        "eject_check_tray",
        "endian_switch01",
        // "epoll-ltp",
        "epoll_create01",
        "epoll_create02",
        "epoll_create1_01",
        "epoll_create1_02",
        "epoll_ctl01",
        //"epoll_ctl02",
        "epoll_ctl03",
        // "epoll_ctl04",
        // "epoll_ctl05",
        /*monkey */
        // "epoll_pwait01",
        // "epoll_pwait02",
        // "epoll_pwait03",
        // "epoll_pwait04",
        // "epoll_pwait05",
        // "epoll_wait01",
        // "epoll_wait02",
        // "epoll_wait03",
        // "epoll_wait04",
        // "epoll_wait05",
        // "epoll_wait06",
        // "epoll_wait07",
        // "event_generator",
        // "eventfd01",
        // "eventfd02",
        // "eventdio03",
        // "eventfd04",
        // "eventfd05",
        // "eventfd06",
        // "eventfd2_01",
        // "eventfd2_02",
        // "eventfd2_03",
        // "exec_with_inh",
        // "exec_without_inh",
        // "execl01",
        // "execl01_child",
        // "execle01",
        // "execle01_child",
        // "execlp01",
        // "execlp01_child",
        // "execv01",
        // "execv01_child",
        // "execve01",
        // "execve01_child",
        // "execve02",
        // "execve03",
        // "execve04",
        // "execve05",
        // "execve06",
        // "execve06_child",
        // "execve_child",
        // "execveat01",
        // "execveat02",
        // "execveat03",
        // "execveat_child",
        // "execveat_errno",
        // "execvp01",
        "execvp01_child",
        "exit01",
        "exit02",
        "exit_group01",
        "f00f",
        "faccessat01",
        "faccessat02",
        // "faccessat201",
        // "faccessat202",
        // "fallocate01",
        // "fallocate02",
        // "fallocate03",
        // "fallocate04",
        // "fallocate05",
        // "fallocate06",
        // "fanotify01",
        // "fanotify02",
        // "fanotify03",
        // "fanotify04",
        // "fanotify05",
        // "fanotify06",
        // "fanotify07",
        // "fanotify08",
        // "fanotify09",
        // "fanotify10",
        // "fanotify11",
        // "fanotify12",
        // "fanotify13",
        // "fanotify14",
        // "fanotify15",
        // "fanotify16",
        // "fanotify17",
        // "fanotify18",
        // "fanotify19",
        // "fanotify20",
        // "fanotify21",
        // "fanotify22",
        // "fanotify23",
        // "fanotify_child",
        // "fanout01",
        // "fchdir01",
        // "fchdir02",
        // "fchdir03",
        // "fchmod01",
        // "fchmod02",
        // "fchmod03",
        // "fchmod04",
        // "fchmod05",
        // "fchmod06",
        // "fchmodat01",
        // "fchmodat02",
        // "fchown01",
        // "fchown01_16",
        // "fchown02",
        // "fchown02_16",
        // "fchown03",
        // "fchown03_16",
        // "fchown04",
        // "fchown04_16",
        // "fchown05",
        // "fchown05_16",
        // "fchownat01",
        // "fchownat02",
        // "fcntl01",
        // "fcntl01_64",
        // "fcntl02",
        // "fcntl02_64",
        // "fcntl03",
        // "fcntl03_64",
        // "fcntl04",
        // "fcntl04_64",
        // "fcntl05",
        // "fcntl05_64",
        // "fcntl07",
        // "fcntl07_64",
        // "fcntl08",
        // "fcntl08_64",
        // "fcntl09",
        // "fcntl09_64",
        // "fcntl10",
        // "fcntl10_64",
        // "fcntl11",
        // "fcntl11_64",
        // "fcntl12",
        // "fcntl12_64",
        // "fcntl13",
        // "fcntl13_64",
        // "fcntl14",
        // "fcntl14_64",
        // "fcntl15",
        // "fcntl15_64",
        // "fcntl16",
        // "fcntl16_64",
        // "fcntl17",
        // "fcntl17_64",
        // "fcntl18",
        // "fcntl18_64",
        // "fcntl19",
        // "fcntl19_64",
        // "fcntl20",
        // "fcntl20_64",
        // "fcntl21",
        // "fcntl21_64",
        // "fcntl22",
        // "fcntl22_64",
        // "fcntl23",
        // "fcntl23_64",
        // "fcntl24",
        // "fcntl24_64",
        // "fcntl25",
        // "fcntl25_64",
        // "fcntl26",
        // "fcntl26_64",
        // "fcntl27",
        // "fcntl27_64",
        // "fcntl29",
        // "fcntl29_64",
        // "fcntl30",
        // "fcntl30_64",
        // "fcntl31",
        // "fcntl31_64",
        // "fcntl32",
        // "fcntl32_64",
        // "fcntl33",
        // "fcntl33_64",
        // "fcntl34",
        // "fcntl34_64",
        // "fcntl35",
        // "fcntl35_64",
        // "fcntl36",
        // "fcntl36_64",
        // "fcntl37",
        // "fcntl37_64",
        // "fcntl38",
        // "fcntl38_64",
        // "fcntl39",
        // "fcntl39_64",
        // "fdatasync01",
        // "fdatasync02",
        // "fdatasync03",
        // "fgetxattr01",
        // "fgetxattr02",
        // "fgetxattr03",
        // "finit_module01",
        // "finit_module02",
        // "flistxattr01",
        // "flistxattr02",
        // "flistxattr03",
        // "float_bessel",
        // "float_exp_log",
        // "float_iperb",
        // "float_power",
        // "float_trigo",
        "flock01",
        "flock02",
        "flock03",
        "flock04",
        "flock06",
        "fork01",
        "fork03",
        "fork04",
        "fork05",
        "fork07",
        "fork08",
        "fork09",
        "fork10",
        //"fork13",
        //"fork14",
        "fork_procs",
        "fpathconf01",
        // "fptest01",
        // "fptest02",
        // "frag",
        // "fremovexattr01",
        // "fremovexattr02",
        // "fs_di",
        //"fs_fill",
        //"fs_inod",
        //"fs_perms",
        // "fsconfig01",
        // "fsconfig02",
        // "fsconfig03",
        // "fsmount01",
        // "fsmount02",
        // "fsopen01",
        // "fsopen02",
        // "fspick01",
        // "fspick02",
        // "fsstress",
        "fstat02",
        "fstat02_64",
        "fstat03",
        "fstat03_64",
        "fstatat01",
        // "fstatfs01",
        // "fstatfs01_64",
        // "fstatfs02",
        // "fstatfs02_64",
        // "fsync01",
        // "fsync02",
        // "fsync03",
        // "fsync04",
        // "fsetxattr01",
        // "fsetxattr02",
        // "ftest01",
        // "ftest02",
        // "ftest03",
        // "ftest04",
        // "ftest05",
        // "ftest06",
        // "ftest07",
        // "ftest08",
        // "futex_cmp_requeue01",
        // "futex_cmp_requeue02",
        "futex_wait01",
        // "futex_wait02",
        // "futex_wait03",
        "futex_wait04",
        "futex_wait05",
        //"futex_wait_bitset01",
        // "futex_waitv01",
        // "futex_waitv02",
        // "futex_waitv03",
        "futex_wake01",
        "futex_wake02",
        //"futex_wake03",
        // "futex_wake04",
        // "futimesat01",
        // "fw_load",
        // "genacos",
        // "genasin",
        // "genatan",
        // "genatan2",
        // "genbessel",
        // "genceil",
        // "gencos",
        // "gencosh",
        // "genexp",
        // "genexp_log",
        // "genfabs",
        // "genfloor",
        // "genfmod",
        // "genfrexp",
        // "genhypot",
        // "geniperb",
        // "genj0",
        // "genj1",
        // "genldexp",
        // "genlgamma",
        // "genload",
        // "genlog",
        // "genlog10",
        // "genmodf",
        // "genpow",
        // "genpower",
        // "gensin",
        // "gensinh",
        // "gensqrt",
        // "gentan",
        // "gentanh",
        // "gentrigo",
        // "geny0",
        // "geny1",
        // "get_ifname",
        // "get_mempolicy01",
        // "get_mempolicy02",
        // "get_robust_list01",
        // "getaddrinfo_01",
        // "getcontext01",
        // "getcpu01",
        // "getcwd01",
        // "getcwd02",
        // "getcwd03",
        // // "getcwd04",
        // "getdents01",
        // "getdents02",
        "getdomainname01",
        //"getegid01",
        // "getegid01_16",
        "getegid02",
        "getegid02_16",
        //"geteuid01",
        "geteuid01_16",
        "geteuid02",
        "geteuid02_16",
        "getgid01",
        "getgid01_16",
        "getgid03",
        //"getgid03_16",
        // "getgroups01",
        // "getgroups01_16",
        // "getgroups03",
        // "getgroups03_16",
        "gethostbyname_r01",
        "gethostid01",
        "gethostname01",
        //"gethostname02",
        "getitimer01",
        //"getitimer02",
        "getpagesize01",
        //"getpeername01",
        "getpgid01",
        "getpgid02",
        "getpgrp01",
        "getpid01",
        "getpid02",
        "getppid01",
        "getppid02",
        //"getpriority01",
        //"getpriority02",
        // "getrandom01",
        "getrandom02",
        "getrandom03",
        "getrandom04",
        // "getrandom05",
        // "getresgid01",
        // "getresgid01_16",
        // "getresgid02",
        // "getresgid02_16",
        // "getresgid03",
        // "getresgid03_16",
        // "getresuid01",
        // "getresuid01_16",
        // "getresuid02",
        // "getresuid02_16",
        // "getresuid03",
        // "getresuid03_16",
        "getrlimit01",
        //"getrlimit02",
        //"getrlimit03",
        "getrusage01",
        //"getrusage02",
        //"getrusage03",
        //"getrusage03_child",
        //"getrusage04",
        //"getsid01",
        //"getsid02",
        // "getsockname01",
        // "getsockopt01",
        // "getsockopt02",
        // "gettid01",
        // "gettid02",
        // "gettimeofday01",
        // "gettimeofday02",
        "getuid01",
        //"getuid01_16",
        "getuid03",
        "getuid03_16",
        // "getxattr01",
        // "getxattr02",
        // "getxattr03",
        // "getxattr04",
        // "getxattr05",
        //"growfiles",
        // "hackbench",
        // "hangup01",
        // "ht_affinity",
        // "ht_enabled",
        // "hugefallocate01",
        // "hugefallocate02",
        // "hugefork01",
        //"hugefork02",
        // "hugemmap01",
        // "hugemmap02",
        // "hugemmap04",
        // "hugemmap05",
        // "hugemmap06",
        // "hugemmap07",
        // "hugemmap08",
        // "hugemmap09",
        // "hugemmap10",
        // "hugemmap11",
        // "hugemmap12",
        // "hugemmap13",
        // "hugemmap14",
        // "hugemmap15",
        // "hugemmap16",
        // "hugemmap17",
        // "hugemmap18",
        // "hugemmap19",
        // "hugemmap20",
        // "hugemmap21",
        // "hugemmap22",
        // "hugemmap23",
        // "hugemmap24",
        // "hugemmap25",
        // "hugemmap26",
        // "hugemmap27",
        // "hugemmap28",
        // "hugemmap29",
        // "hugemmap30",
        // "hugemmap31",
        // "hugemmap32",
        // "hugeshmat01",
        // "hugeshmat02",
        // "hugeshmat03",
        // "hugeshmat04",
        // "hugeshmat05",
        // "hugeshmctl01",
        // "hugeshmctl02",
        // "hugeshmctl03",
        // "hugeshmdt01",
        // "hugeshmget01",
        // "hugeshmget02",
        // "hugeshmget03",
        // "hugeshmget05",
        // "icmp4-multi-diffip01",
        // "icmp4-multi-diffip02",
        // "icmp4-multi-diffip03",
        // "icmp4-multi-diffip04",
        // "icmp4-multi-diffip05",
        // "icmp4-multi-diffip06",
        // "icmp4-multi-diffip07",
        // "icmp4-multi-diffnic01",
        // "icmp4-multi-diffnic02",
        // "icmp4-multi-diffnic03",
        // "icmp4-multi-diffnic04",
        // "icmp4-multi-diffnic05",
        // "icmp4-multi-diffnic06",
        // "icmp4-multi-diffnic07",
        // "icmp6-multi-diffip01",
        // "icmp6-multi-diffip02",
        // "icmp6-multi-diffip03",
        // "icmp6-multi-diffip04",
        // "icmp6-multi-diffip05",
        // "icmp6-multi-diffip06",
        // "icmp6-multi-diffip07",
        // "icmp6-multi-diffnic01",
        // "icmp6-multi-diffnic02",
        // "icmp6-multi-diffnic03",
        // "icmp6-multi-diffnic04",
        // "icmp6-multi-diffnic05",
        // "icmp6-multi-diffnic06",
        // "icmp6-multi-diffnic07",
        //"icmp_rate_limit01",
        // "init_module01",
        // "init_module02",
        //"initialize_if",
        "inode01",
        //"inode02",
        // "inotify01",
        // "inotify02",
        // "inotify03",
        // "inotify04",
        // "inotify05",
        // "inotify06",
        // "inotify07",
        // "inotify08",
        // "inotify09",
        // "inotify10",
        // "inotify11",
        // "inotify12",
        // "inotify_init1_01",
        // "inotify_init1_02",
        // "input01",
        // "input02",
        // "input03",
        // "input04",
        // "input05",
        // "input06",
        // "io_cancel01",
        // "io_cancel02",
        // "io_control01",
        // "io_destroy01",
        // "io_destroy02",
        // "io_getevents01",
        // "io_getevents02",
        // "io_pgetevents01",
        // "io_pgetevents02",
        // "io_setup01",
        // "io_setup02",
        // "io_submit01",
        // "io_submit02",
        // "io_submit03",
        // "io_uring01",
        // "io_uring02",
        // "ioctl01",
        // "ioctl02",
        // "ioctl03",
        // "ioctl04",
        // "ioctl05",
        // "ioctl06",
        // "ioctl07",
        // "ioctl08",
        // "ioctl09",
        // "ioctl_loop01",
        // "ioctl_loop02",
        // "ioctl_loop03",
        // "ioctl_loop04",
        // "ioctl_loop05",
        // "ioctl_loop06",
        // "ioctl_loop07",
        // "ioctl_ns01",
        // "ioctl_ns02",
        // "ioctl_ns03",
        // "ioctl_ns04",
        // "ioctl_ns05",
        // "ioctl_ns06",
        // "ioctl_ns07",
        // "ioctl_sg01",
        // "iogen",
        // "ioperm01",
        // "ioperm02",
        // "iopl01",
        // "iopl02",
        // "ioprio_get01",
        // "ioprio_set01",
        // "ioprio_set02",
        // "ioprio_set03",
        // "irqbalance01",
        // "kallsyms",
        // "kcmp01",
        // "kcmp02",
        // "kcmp03",
        // "kernbench",
        // "keyctl01",
        // "keyctl02",
        // "keyctl03",
        // "keyctl04",
        // "keyctl05",
        // "keyctl06",
        // "keyctl07",
        // "keyctl08",
        // "keyctl09",
        // "kill02",
        // "kill03",
        // "kill05",
        // "kill06",
        // "kill07",
        // "kill08",
        // "kill09",
        // "kill10",
        // "kill11",
        // "kill12",
        // "kill13",
        // "killall_icmp_traffic",
        // "killall_tcp_traffic",
        // "killall_udp_traffic",
        //"kmsg01",
        // "ksm01",
        // "ksm02",
        // "ksm03",
        // "ksm04",
        // "ksm05",
        // "ksm06",
        // "ksm07",
        // "lchown01",
        // "lchown01_16",
        // "lchown02",
        // "lchown02_16",
        // "lchown03",
        // "lchown03_16",
        // "leapsec01",
        "lftest",
        // "libcgroup_freezer",
        "link02",
        "link04",
        // "link05",
        // "link08",
        // "linkat01",
        // "linkat02",
        // "listen01",
        // "listxattr01",
        // "listxattr02",
        // "listxattr03",
        // "llistxattr01",
        // "llistxattr02",
        // "llistxattr03",
        "llseek01",
        "llseek02",
        "llseek03",
        "lseek01",
        "lseek02",
        "lseek07",
        // "lseek11",
        // "lstat01",
        // "lstat01_64",
        // "lstat02",
        // "lstat02_64",
        // "ltpClient",
        // "ltpServer",
        // "ltp_acpi",
        // "madvise01",
        // "madvise02",
        // "madvise03",
        // "madvise05",
        // "madvise06",
        // "madvise07",
        // "madvise08",
        // "madvise09",
        // "madvise10",
        // "madvise11",
        // "mallinfo01",
        // "mallinfo02",
        // "mallinfo2_01",
        // "mallocstress",
        // "mallopt01",
        // "max_map_count",
        // "mbind01",
        // "mbind02",
        // "mbind03",
        // "mbind04",
        // "mc_member_test",
        // "mc_recv",
        // "mc_send",
        // "mc_verify_opts",
        // "mc_verify_opts_error",
        // "meltdown",
        // "mem02",
        // "mem_process",
        // "membarrier01",
        // "memcg_control_test",
        // "memcg_failcnt",
        // "memcg_force_empty",
        // "memcg_limit_in_bytes",
        // "memcg_max_usage_in_bytes_test",
        // "memcg_memsw_limit_in_bytes_test",
        // "memcg_move_charge_at_immigrate_test",
        // "memcg_process",
        // "memcg_process_stress",
        // "memcg_stat_rss",
        // "memcg_stat_test",
        // "memcg_stress_test",
        // "memcg_subgroup_charge",
        // "memcg_test_1",
        // "memcg_test_2",
        // "memcg_test_3",
        // "memcg_test_4",
        // "memcg_usage_in_bytes_test",
        // "memcg_use_hierarchy_test",
        "memcmp01",
        // "memcontrol01",
        // "memcontrol02",
        // "memcontrol03",
        // "memcontrol04",
        // "memcpy01",
        // "memctl_test01",
        // "memfd_create01",
        // "memfd_create02",
        // "memfd_create03",
        // "memfd_create04",
        "memset01",
        "memtoy",
        "mesgq_nstest",
        // "migrate_pages01",
        // "migrate_pages02",
        // "migrate_pages03",
        // "min_free_kbytes",
        // "mincore01",
        // "mincore02",
        // "mincore03",
        // "mincore04",
        // "mkdir02",
        // "mkdir03",
        // "mkdir04",
        // "mkdir05",
        // "mkdir09",
        // "mkdirat01",
        // "mkdirat02",
        // "mknod01",
        // "mknod02",
        // "mknod03",
        // "mknod04",
        // "mknod05",
        // "mknod06",
        // "mknod07",
        // "mknod08",
        // "mknod09",
        // "mknodat01",
        // "mknodat02",
        // "mlock01",
        // "mlock02",
        // "mlock03",
        // "mlock04",
        // "mlock05",
        // "mlock201",
        // "mlock202",
        // "mlock203",
        // "mlockall01",
        // "mlockall02",
        // "mlockall03",
        // "mmap-corruption01",
        // "mmap001",
        // "mmap01",
        // "mmap02",
        // "mmap03",
        // "mmap04",
        // "mmap05",
        // "mmap06",
        // "mmap08",
        "mmap09",
        // "mmap1",
        // "mmap10",
        // "mmap11",
        // "mmap12",
        // "mmap13",
        // "mmap14",
        // "mmap15",
        // "mmap16",
        // "mmap17",
        "mmap18",
        // "mmap19",
        // "mmap2",
        // "mmap20",
        // "mmap3",
        // "mmapstress01",
        // "mmapstress02",
        // "mmapstress03",
        // "mmapstress04",
        // "mmapstress05",
        // "mmapstress06",
        // "mmapstress07",
        // "mmapstress08",
        // "mmapstress09",
        // "mmapstress10",
        // "mmstress",
        // "mmstress_dummy",
        // "modify_ldt01",
        // "modify_ldt02",
        // "modify_ldt03",
        // "mount01",
        // "mount02",
        // "mount03",
        // "mount03_suid_child",
        // "mount04",
        // "mount05",
        // "mount06",
        // "mount07",
        // "mount_setattr01",
        // "mountns01",
        // "mountns02",
        // "mountns03",
        // "mountns04",
        // "move_mount01",
        // "move_mount02",
        // "move_pages01",
        // "move_pages02",
        // "move_pages03",
        // "move_pages04",
        // "move_pages05",
        // "move_pages06",
        // "move_pages07",
        // "move_pages09",
        // "move_pages10",
        // "move_pages11",
        // "move_pages12",
        // "mq_notify01",
        // "mq_notify02",
        // "mq_notify03",
        // "mq_open01",
        // "mq_timedreceive01",
        // "mq_timedsend01",
        // "mq_unlink01",
        // "mqns_01",
        // "mqns_02",
        // "mqns_03",
        // "mqns_04",
        // "mremap01",
        // "mremap02",
        // "mremap03",
        // "mremap04",
        // "mremap05",
        // "mremap06",
        //"msg_comm",
        "msgctl01",
        "msgctl02",
        //"msgctl03",
        // "msgctl04",
        // "msgctl05",
        // "msgctl06",
        "msgctl12",
        "msgget01",
        "msgget02",
        "msgget03",
        "msgget04",
        "msgget05",
        "msgrcv01",
        "msgrcv02",
        // "msgrcv03",
        // "msgrcv05",
        // "msgrcv06",
        "msgrcv07",
        "msgrcv08",
        // "msgsnd01",
        // "msgsnd02",
        // "msgsnd05",
        // "msgsnd06",
        // "msgstress01",
        // "msync01",
        // "msync02",
        // "msync03",
        // "msync04",
        // "mtest01",
        // "munlock01",
        // "munlock02",
        // "munlockall01",
        // "munmap01",
        // "munmap02",
        // "munmap03",
        // "name_to_handle_at01",
        // "name_to_handle_at02",
        "nanosleep01",
        // "nanosleep02",
        // "nanosleep04",
        "netns_netlink",
        "netstress",
        "newuname01",
        // "nextafter01",
        // "nfs01_open_files",
        // "nfs04_create_file",
        // "nfs05_make_tree",
        // "nfs_flock",
        // "nfs_flock_dgen",
        // "nft01",
        // "nft02",
        // "nftw01",
        // "nftw6401",
        // "nice01",
        // "nice02",
        // "nice03",
        "nice04",
        "nice05",
        // "nptl01",
        // "ns-echoclient",
        // "ns-icmp_redirector",
        // "ns-icmpv4_sender",
        // "ns-icmpv6_sender",
        // "ns-igmp_querier",
        // "ns-mcast_join",
        // "ns-mcast_receiver",
        // "ns-tcpclient",
        // "ns-tcpserver",
        // "ns-udpclient",
        // "ns-udpsender",
        // "ns-udpserver",
        "open01",
        "open02",
        "open03",
        "open04",
        "open06",
        // "open07",
        // "open08",
        // "open09",
        // "open10",
        // "open11",
        // "open12",
        // "open12_child",
        // "open13",
        // "open14",
        // "open_by_handle_at01",
        // "open_by_handle_at02",
        // "open_tree01",
        // "open_tree02",
        "openat01",
        // "openat02",
        // "openat02_child",
        // "openat03",
        // "openat04",
        // "openat201",
        // "openat202",
        // "openat203",
        "openfile",
        "overcommit_memory",
        "page01",
        "page02",
        "pathconf01",
        "pathconf02",
        // "pause01",
        // "pause02",
        // "pause03",
        // "pcrypt_aead01",
        // "pec_listener",
        // "perf_event_open01",
        // "perf_event_open02",
        // "perf_event_open03",
        // "personality01",
        // "personality02",
        // "pidfd_getfd01",
        // "pidfd_getfd02",
        // "pidfd_open01",
        // "pidfd_open02",
        // "pidfd_open03",
        // "pidfd_open04",
        // "pidfd_send_signal01",
        // "pidfd_send_signal02",
        // "pidfd_send_signal03",
        // "pidns01",
        // "pidns02",
        // "pidns03",
        // "pidns04",
        // "pidns05",
        // "pidns06",
        // "pidns10",
        // "pidns12",
        // "pidns13",
        // "pidns16",
        // "pidns17",
        // "pidns20",
        // "pidns30",
        // "pidns31",
        // "pidns32",
        // "pids_task1",
        // "pids_task2",
        "pipe01",
        // "pipe02",
        // "pipe03",
        // "pipe04",
        // "pipe05",
        // "pipe06",
        // "pipe07",
        // "pipe08",
        // "pipe09",
        "pipe10",
        "pipe11",
        "pipe12",
        //"pipe13",
        "pipe14",
        "pipe15",
        "pipe2_01",
        //"pipe2_02",
        //"pipe2_02_child",
        //"pipe2_04",
        //"pipeio",
        //"pivot_root01",
        //"pkey01",
        "poll01",
        "poll02",
        "posix_fadvise01",
        "posix_fadvise01_64",
        "posix_fadvise02",
        "posix_fadvise02_64",
        "posix_fadvise03",
        "posix_fadvise03_64",
        // "posix_fadvise04",
        // "posix_fadvise04_64",
        //"ppoll01",
        "prctl01",
        "prctl02",
        //"prctl03",
        "prctl04",
        "prctl05",
        "prctl06",
        "prctl06_execve",
        "prctl07",
        "prctl08",
        "prctl09",
        "prctl10",
        "pread01",
        // "pread01_64",
        // "pread02",
        // "pread02_64",
        // "preadv01",
        // "preadv01_64",
        // "preadv02",
        // "preadv02_64",
        // "preadv03",
        // "preadv03_64",
        // "preadv201",
        // "preadv201_64",
        // "preadv202",
        // "preadv202_64",
        // "preadv203",
        // "preadv203_64",
        // "print_caps",
        "proc01",
        "proc_sched_rt01",
        "process_madvise01",
        "process_vm01",
        // "process_vm_readv02",
        // "process_vm_readv03",
        // "process_vm_writev02",
        // "profil01",
        // "prot_hsymlinks",
        "pselect01",
        "pselect01_64",
        // "pselect02",
        // "pselect02_64",
        // "tcp4-multi-diffip01",
        // "tcp4-multi-diffip02",
        // "tcp4-multi-diffip03",
        // "tcp4-multi-diffip04",
        // "tcp4-multi-diffip05",
        // "tcp4-multi-diffip06",
        // "tcp4-multi-diffip07",
        // "tcp4-multi-diffip08",
        // "tcp4-multi-diffip09",
        // "tcp4-multi-diffip10",
        // "tcp4-multi-diffip11",
        // "tcp4-multi-diffip12",
        // "tcp4-multi-diffip13",
        // "tcp4-multi-diffip14",
        // "tcp4-multi-diffnic01",
        // "tcp4-multi-diffnic02",
        // "tcp4-multi-diffnic03",
        // "tcp4-multi-diffnic04",
        // "tcp4-multi-diffnic05",
        // "tcp4-multi-diffnic06",
        // "tcp4-multi-diffnic07",
        // "tcp4-multi-diffnic08",
        // "tcp4-multi-diffnic09",
        // "tcp4-multi-diffnic10",
        // "tcp4-multi-diffnic11",
        // "tcp4-multi-diffnic12",
        // "tcp4-multi-diffnic13",
        // "tcp4-multi-diffnic14",
        // "tcp4-multi-diffport01",
        // "tcp4-multi-diffport02",
        // "tcp4-multi-diffport03",
        // "tcp4-multi-diffport04",
        // "tcp4-multi-diffport05",
        // "tcp4-multi-diffport06",
        // "tcp4-multi-diffport07",
        // "tcp4-multi-diffport08",
        // "tcp4-multi-diffport09",
        // "tcp4-multi-diffport10",
        // "tcp4-multi-diffport11",
        // "tcp4-multi-diffport12",
        // "tcp4-multi-diffport13",
        // "tcp4-multi-diffport14",
        // "tcp4-multi-sameport01",
        // "tcp4-multi-sameport02",
        // "tcp4-multi-sameport03",
        // "tcp4-multi-sameport04",
        // "tcp4-multi-sameport05",
        // "tcp4-multi-sameport06",
        // "tcp4-multi-sameport07",
        // "tcp4-multi-sameport08",
        // "tcp4-multi-sameport09",
        // "tcp4-multi-sameport10",
        // "tcp4-multi-sameport11",
        // "tcp4-multi-sameport12",
        // "tcp4-multi-sameport13",
        // "tcp4-multi-sameport14",
        // "tcp4-uni-basic01",
        // "tcp4-uni-basic02",
        // "tcp4-uni-basic03",
        // "tcp4-uni-basic04",
        // "tcp4-uni-basic05",
        // "tcp4-uni-basic06",
        // "tcp4-uni-basic07",
        // "tcp4-uni-basic08",
        // "tcp4-uni-basic09",
        // "tcp4-uni-basic10",
        // "tcp4-uni-basic11",
        // "tcp4-uni-basic12",
        // "tcp4-uni-basic13",
        // "tcp4-uni-basic14",
        // "tcp4-uni-dsackoff01",
        // "tcp4-uni-dsackoff02",
        // "tcp4-uni-dsackoff03",
        // "tcp4-uni-dsackoff04",
        // "tcp4-uni-dsackoff05",
        // "tcp4-uni-dsackoff06",
        // "tcp4-uni-dsackoff07",
        // "tcp4-uni-dsackoff08",
        // "tcp4-uni-dsackoff09",
        // "tcp4-uni-dsackoff10",
        // "tcp4-uni-dsackoff11",
        // "tcp4-uni-dsackoff12",
        // "tcp4-uni-dsackoff13",
        // "tcp4-uni-dsackoff14",
        // "tcp4-uni-pktlossdup01",
        // "tcp4-uni-pktlossdup02",
        // "tcp4-uni-pktlossdup03",
        // "tcp4-uni-pktlossdup04",
        // "tcp4-uni-pktlossdup05",
        // "tcp4-uni-pktlossdup06",
        // "tcp4-uni-pktlossdup07",
        // "tcp4-uni-pktlossdup08",
        // "tcp4-uni-pktlossdup09",
        // "tcp4-uni-pktlossdup10",
        // "tcp4-uni-pktlossdup11",
        // "tcp4-uni-pktlossdup12",
        // "tcp4-uni-pktlossdup13",
        // "tcp4-uni-pktlossdup14",
        // "tcp4-uni-sackoff01",
        // "tcp4-uni-sackoff02",
        // "tcp4-uni-sackoff03",
        // "tcp4-uni-sackoff04",
        // "tcp4-uni-sackoff05",
        // "tcp4-uni-sackoff06",
        // "tcp4-uni-sackoff07",
        // "tcp4-uni-sackoff08",
        // "tcp4-uni-sackoff09",
        // "tcp4-uni-sackoff10",
        // "tcp4-uni-sackoff11",
        // "tcp4-uni-sackoff12",
        // "tcp4-uni-sackoff13",
        // "tcp4-uni-sackoff14",
        // "tcp4-uni-smallsend01",
        // "tcp4-uni-smallsend02",
        // "tcp4-uni-smallsend03",
        // "tcp4-uni-smallsend04",
        // "tcp4-uni-smallsend05",
        // "tcp4-uni-smallsend06",
        // "tcp4-uni-smallsend07",
        // "tcp4-uni-smallsend08",
        // "tcp4-uni-smallsend09",
        // "tcp4-uni-smallsend10",
        // "tcp4-uni-smallsend11",
        // "tcp4-uni-smallsend12",
        // "tcp4-uni-smallsend13",
        // "tcp4-uni-smallsend14",
        // "tcp4-uni-tso01",
        // "tcp4-uni-tso02",
        // "tcp4-uni-tso03",
        // "tcp4-uni-tso04",
        // "tcp4-uni-tso05",
        // "tcp4-uni-tso06",
        // "tcp4-uni-tso07",
        // "tcp4-uni-tso08",
        // "tcp4-uni-tso09",
        // "tcp4-uni-tso10",
        // "tcp4-uni-tso11",
        // "tcp4-uni-tso12",
        // "tcp4-uni-tso13",
        // "tcp4-uni-tso14",
        // "tcp4-uni-winscale01",
        // "tcp4-uni-winscale02",
        // "tcp4-uni-winscale03",
        // "tcp4-uni-winscale04",
        // "tcp4-uni-winscale05",
        // "tcp4-uni-winscale06",
        // "tcp4-uni-winscale07",
        // "tcp4-uni-winscale08",
        // "tcp4-uni-winscale09",
        // "tcp4-uni-winscale10",
        // "tcp4-uni-winscale11",
        // "tcp4-uni-winscale12",
        // "tcp4-uni-winscale13",
        // "tcp4-uni-winscale14",
        // "tcp6-multi-diffip01",
        // "tcp6-multi-diffip02",
        // "tcp6-multi-diffip03",
        // "tcp6-multi-diffip04",
        // "tcp6-multi-diffip05",
        // "tcp6-multi-diffip06",
        // "tcp6-multi-diffip07",
        // "tcp6-multi-diffip08",
        // "tcp6-multi-diffip09",
        // "tcp6-multi-diffip10",
        // "tcp6-multi-diffip11",
        // "tcp6-multi-diffip12",
        // "tcp6-multi-diffip13",
        // "tcp6-multi-diffip14",
        // "tcp6-multi-diffnic01",
        // "tcp6-multi-diffnic02",
        // "tcp6-multi-diffnic03",
        // "tcp6-multi-diffnic04",
        // "tcp6-multi-diffnic05",
        // "tcp6-multi-diffnic06",
        // "tcp6-multi-diffnic07",
        // "tcp6-multi-diffnic08",
        // "tcp6-multi-diffnic09",
        // "tcp6-multi-diffnic10",
        // "tcp6-multi-diffnic11",
        // "tcp6-multi-diffnic12",
        // "tcp6-multi-diffnic13",
        // "tcp6-multi-diffnic14",
        // "tcp6-multi-diffport01",
        // "tcp6-multi-diffport02",
        // "tcp6-multi-diffport03",
        // "tcp6-multi-diffport04",
        // "tcp6-multi-diffport05",
        // "tcp6-multi-diffport06",
        // "tcp6-multi-diffport07",
        // "tcp6-multi-diffport08",
        // "tcp6-multi-diffport09",
        // "tcp6-multi-diffport10",
        // "tcp6-multi-diffport11",
        // "tcp6-multi-diffport12",
        // "tcp6-multi-diffport13",
        // "tcp6-multi-diffport14",
        // "tcp6-multi-sameport01",
        // "tcp6-multi-sameport02",
        // "tcp6-multi-sameport03",
        // "tcp6-multi-sameport04",
        // "tcp6-multi-sameport05",
        // "tcp6-multi-sameport06",
        // "tcp6-multi-sameport07",
        // "tcp6-multi-sameport08",
        // "tcp6-multi-sameport09",
        // "tcp6-multi-sameport10",
        // "tcp6-multi-sameport11",
        // "tcp6-multi-sameport12",
        // "tcp6-multi-sameport13",
        // "tcp6-multi-sameport14",
        // "tcp6-uni-basic01",
        // "tcp6-uni-basic02",
        // "tcp6-uni-basic03",
        // "tcp6-uni-basic04",
        // "tcp6-uni-basic05",
        // "tcp6-uni-basic06",
        // "tcp6-uni-basic07",
        // "tcp6-uni-basic08",
        // "tcp6-uni-basic09",
        // "tcp6-uni-basic10",
        // "tcp6-uni-basic11",
        // "tcp6-uni-basic12",
        // "tcp6-uni-basic13",
        // "tcp6-uni-basic14",
        // "tcp6-uni-dsackoff01",
        // "tcp6-uni-dsackoff02",
        // "tcp6-uni-dsackoff03",
        // "tcp6-uni-dsackoff04",
        // "tcp6-uni-dsackoff05",
        // "tcp6-uni-dsackoff06",
        // "tcp6-uni-dsackoff07",
        // "tcp6-uni-dsackoff08",
        // "tcp6-uni-dsackoff09",
        // "tcp6-uni-dsackoff10",
        // "tcp6-uni-dsackoff11",
        // "tcp6-uni-dsackoff12",
        // "tcp6-uni-dsackoff13",
        // "tcp6-uni-dsackoff14",
        // "tcp6-uni-pktlossdup01",
        // "tcp6-uni-pktlossdup02",
        // "tcp6-uni-pktlossdup03",
        // "tcp6-uni-pktlossdup04",
        // "tcp6-uni-pktlossdup05",
        // "tcp6-uni-pktlossdup06",
        // "tcp6-uni-pktlossdup07",
        // "tcp6-uni-pktlossdup08",
        // "tcp6-uni-pktlossdup09",
        // "tcp6-uni-pktlossdup10",
        // "tcp6-uni-pktlossdup11",
        // "tcp6-uni-pktlossdup12",
        // "tcp6-uni-pktlossdup13",
        // "tcp6-uni-pktlossdup14",
        // "tcp6-uni-sackoff01",
        // "tcp6-uni-sackoff02",
        // "tcp6-uni-sackoff03",
        // "tcp6-uni-sackoff04",
        // "tcp6-uni-sackoff05",
        // "tcp6-uni-sackoff06",
        // "tcp6-uni-sackoff07",
        // "tcp6-uni-sackoff08",
        // "tcp6-uni-sackoff09",
        // "tcp6-uni-sackoff10",
        // "tcp6-uni-sackoff11",
        // "tcp6-uni-sackoff12",
        // "tcp6-uni-sackoff13",
        // "tcp6-uni-sackoff14",
        // "tcp6-uni-smallsend01",
        // "tcp6-uni-smallsend02",
        // "tcp6-uni-smallsend03",
        // "tcp6-uni-smallsend04",
        // "tcp6-uni-smallsend05",
        // "tcp6-uni-smallsend06",
        // "tcp6-uni-smallsend07",
        // "tcp6-uni-smallsend08",
        // "tcp6-uni-smallsend09",
        // "tcp6-uni-smallsend10",
        // "tcp6-uni-smallsend11",
        // "tcp6-uni-smallsend12",
        // "tcp6-uni-smallsend13",
        // "tcp6-uni-smallsend14",
        // "tcp6-uni-tso01",
        // "tcp6-uni-tso02",
        // "tcp6-uni-tso03",
        // "tcp6-uni-tso04",
        // "tcp6-uni-tso05",
        // "tcp6-uni-tso06",
        // "tcp6-uni-tso07",
        // "tcp6-uni-tso08",
        // "tcp6-uni-tso09",
        // "tcp6-uni-tso10",
        // "tcp6-uni-tso11",
        // "tcp6-uni-tso12",
        // "tcp6-uni-tso13",
        // "tcp6-uni-tso14",
        // "tcp6-uni-winscale01",
        // "tcp6-uni-winscale02",
        // "tcp6-uni-winscale03",
        // "tcp6-uni-winscale04",
        // "tcp6-uni-winscale05",
        // "tcp6-uni-winscale06",
        // "tcp6-uni-winscale07",
        // "tcp6-uni-winscale08",
        // "tcp6-uni-winscale09",
        // "tcp6-uni-winscale10",
        // "tcp6-uni-winscale11",
        // "tcp6-uni-winscale12",
        // "tcp6-uni-winscale13",
        // "tcp6-uni-winscale14",
        "pselect03",
        "pselect03_64",
        "pt_test",
        //"ptem01",
        "pth_str01",
        "pth_str02",
        "pth_str03",
        // "pthcli",
        // "pthserv",
        "ptrace01",
        //"ptrace02",
        "ptrace03",
        "ptrace04",
        //"ptrace05",
        // "ptrace06",
        // "ptrace07",
        // "ptrace08",
        // "ptrace09",
        // "ptrace10",
        // "ptrace11",
        // "pty01",
        // "pty02",
        // "pty03",
        // "pty04",
        // "pty05",
        // "pty06",
        // "pty07",
        "pwrite01",
        "pwrite01_64",
        // "pwrite02",
        // "pwrite02_64",
        "pwrite03",
        "pwrite03_64",
        "pwrite04",
        "pwrite04_64",
        // "pwritev01",
        // "pwritev01_64",
        // "pwritev02",
        // "pwritev02_64",
        // "pwritev03",
        // "pwritev03_64",
        // "pwritev201",
        // "pwritev201_64",
        // "pwritev202",
        // "pwritev202_64",
        // "quotactl01",
        // "quotactl02",
        // "quotactl03",
        // "quotactl04",
        // "quotactl05",
        // "quotactl06",
        // "quotactl07",
        // "quotactl08",
        // "quotactl09",
        "read01",
        "read02",
        //"read03",
        "read04",
        //"read_all",
        "readahead01",
        "readahead02",
        "readdir01",
        "readdir21",
        "readlink01",
        "readlink03",
        "readlinkat01",
        "readlinkat02",
        "readv01",
        //"readv02",
        "realpath01",
        // "reboot01",
        // "reboot02",
        // "recv01",
        //"recvfrom01",
        // "recvmmsg01",
        // "recvmsg01",
        // "recvmsg02",
        // "recvmsg03",
        // "remap_file_pages01",
        // "remap_file_pages02",
        // "removexattr01",
        // "removexattr02",
        // "rename01",
        // "rename03",
        // "rename04",
        // "rename05",
        // "rename06",
        // "rename07",
        // "rename08",
        // "rename09",
        // "rename10",
        // "rename11",
        // "rename12",
        // "rename13",
        // "rename14",
        // "renameat01",
        // "renameat201",
        // "renameat202",
        // "request_key01",
        // "request_key02",
        // "request_key03",
        // "request_key04",
        // "request_key05",
        // "rmdir01",
        // "rmdir02",
        // "rmdir03",
        // "route-change-netlink",
        // "rt_sigaction01",
        // "rt_sigaction02",
        // "rt_sigaction03",
        // "rt_sigprocmask01",
        // "rt_sigprocmask02",
        // "rt_sigqueueinfo01",
        // "rt_sigsuspend01",
        // "rtc01",
        // "rtc02",
        //"rwtest",
        "sbrk01",
        "sbrk02",
        //"sbrk03",
        //"sched_datafile",
        // "sched_driver",
        // "sched_get_priority_max01",
        // "sched_get_priority_max02",
        // "sched_get_priority_min01",
        // "sched_get_priority_min02",
        // "sched_getaffinity01",
        // "sched_getattr01",
        // "sched_getattr02",
        // "sched_getparam01",
        // "sched_getparam03",
        // "sched_getscheduler01",
        // "sched_getscheduler02",
        // "sched_rr_get_interval01",
        // "sched_rr_get_interval02",
        // "sched_rr_get_interval03",
        // "sched_setaffinity01",
        // "sched_setattr01",
        // "sched_setparam01",
        // "sched_setparam02",
        // "sched_setparam03",
        // "sched_setparam04",
        // "sched_setparam05",
        // "sched_setscheduler01",
        // "sched_setscheduler02",
        // "sched_setscheduler03",
        // "sched_setscheduler04",
        // "sched_tc0",
        // "sched_tc1",
        // "sched_tc2",
        // "sched_tc3",
        // "sched_tc4",
        // "sched_tc5",
        // "sched_tc6",
        // "sched_yield01",
        // "sctp_big_chunk",
        // "select01",
        "select02",
        // "select03",
        // "select04",
        "sem_comm",
        "sem_nstest",
        "semctl01",
        "semctl02",
        "semctl03",
        // "semctl04",
        // "semctl05",
        // "semctl06",
        "semctl07",
        "semctl08",
        // "semctl09",
        "semget01",
        // "semget02",
        // "semget05",
        // "semop01",
        // "semop02",
        // "semop03",
        // "semop04",
        // "semop05",
        //"semtest_2ns",
        // "send01",
        // "send02",
        // "sendfile02",
        // "sendfile02_64",
        // "sendfile03",
        // "sendfile03_64",
        // "sendfile04",
        // "sendfile04_64",
        // "sendfile05",
        // "sendfile05_64",
        // "sendfile06",
        // "sendfile06_64",
        // "sendfile07",
        // "sendfile07_64",
        // "sendfile08",
        // "sendfile08_64",
        // "sendfile09",
        // "sendfile09_64",
        // "sendmmsg01",
        // "sendmmsg02",
        // "sendmsg01",
        // "sendmsg02",
        // "sendmsg03",
        // "sendto01",
        // "sendto02",
        // "sendto03",
        //"set_ipv4addr",
        // "set_mempolicy01",
        // "set_mempolicy02",
        // "set_mempolicy03",
        // "set_mempolicy04",
        // "set_mempolicy05",
        // "set_robust_list01",
        // "set_thread_area01",
        // "set_tid_address01",
        // "setdomainname01",
        // "setdomainname02",
        // "setdomainname03",
        // "setegid01",
        // "setegid02",
        // "setfsgid01",
        // "setfsgid01_16",
        // "setfsgid02",
        // "setfsgid02_16",
        // "setfsgid03",
        // "setfsgid03_16",
        // "setfsuid01",
        // "setfsuid01_16",
        // "setfsuid02",
        // "setfsuid02_16",
        // "setfsuid03",
        // "setfsuid03_16",
        // "setfsuid04",
        // "setfsuid04_16",
        "setgid01",
        "setgid01_16",
        "setgid02",
        "setgid02_16",
        "setgid03",
        "setgid03_16",
        "setgroups01",
        "setgroups01_16",
        // "setgroups02",
        // "setgroups02_16",
        // "setgroups03",
        // "setgroups03_16",
        // "setgroups04",
        // "setgroups04_16",
        // "sethostname01",
        // "sethostname02",
        // "sethostname03",
        // "setitimer01",
        //"setitimer02",
        "setns01",
        "setns02",
        "setpgid01",
        "setpgid02",
        // "setpgid03",
        // "setpgid03_child",
        "setpgrp01",
        // "setpgrp02",
        // "setpriority01",
        // "setpriority02",
        // "setregid01",
        // "setregid01_16",
        "setregid02",
        // "setregid02_16",
        // "setregid03",
        // "setregid03_16",
        // "setregid04",
        // "setregid04_16",
        // "setresgid01",
        // "setresgid01_16",
        // "setresgid02",
        // "setresgid02_16",
        // "setresgid03",
        // "setresgid03_16",
        // "setresgid04",
        // "setresgid04_16",
        // "setresuid01",
        // "setresuid01_16",
        // "setresuid02",
        // "setresuid02_16",
        // "setresuid03",
        // "setresuid03_16",
        "setresuid04",
        // "setresuid04_16",
        // "setresuid05",
        // "setresuid05_16",
        // "setreuid01",
        // "setreuid01_16",
        // "setreuid02",
        // "setreuid02_16",
        "setreuid03",
        // "setreuid03_16",
        // "setreuid04",
        // "setreuid04_16",
        "setreuid05",
        "setreuid05_16",
        "setreuid06",
        "setreuid06_16",
        "setreuid07",
        "setreuid07_16",
        "setrlimit01",
        "setrlimit02",
        "setrlimit03",
        // "setrlimit04",
        // "setrlimit05",
        // "setrlimit06",
        // "setsid01",
        // "setsockopt01",
        // "setsockopt02",
        "setsockopt03",
        "setsockopt04",
        "setsockopt05",
        // "setsockopt06",
        // "setsockopt07",
        // "setsockopt08",
        // "setsockopt09",
        // "setsockopt10",
        "settimeofday01",
        "settimeofday02",
        // "setuid01",
        // "setuid01_16",
        // "setuid03",
        // "setuid03_16",
        // "setuid04",
        // "setuid04_16",
        // "setxattr01",
        // "setxattr03",
        // "sgetmask01",
        // "shm_comm",
        // "shm_test",
        // "shmat01",
        // "shmat02",
        // "shmat03",
        // "shmat04",
        // "shmat1",
        // "shmctl01",
        // "shmctl02",
        // "shmctl03",
        // "shmctl04",
        // "shmctl05",
        // "shmctl06",
        // "shmctl07",
        // "shmctl08",
        // "shmdt01",
        // "shmdt02",
        // "shmem_2nstest",
        // "shmget02",
        // "shmget03",
        // "shmget04",
        // "shmget05",
        "shmget06",
        "shmnstest",
        // "shmt02",
        // "shmt03",
        // "shmt04",
        // "shmt05",
        // "shmt06",
        // "shmt07",
        // "shmt08",
        // "shmt09",
        // "shmt10",
        // "sigaction01",
        // "sigaction02",
        "sigaltstack01",
        "sigaltstack02",
        "sighold02",
        "signal01",
        "signal02",
        "signal03",
        "signal04",
        "signal05",
        "signal06",
        // "signalfd01",
        // "signalfd4_01",
        // "signalfd4_02",
        // "sigpending02",
        // "sigprocmask01",
        // "sigrelse01",
        // "sigsuspend01",
        //"sigtimedwait01",
        "sigwait01",
        // "sigwaitinfo01",
        //"smack_notroot",
        // "snd_seq01",
        // "snd_timer01",
        "socket01",
        "socket02",
        // "socketcall01",
        // "socketcall02",
        // "socketcall03",
        // "socketpair01",
        // "socketpair02",
        // "sockioctl01",
        // "splice01",
        // "splice02",
        // "splice03",
        // "splice04",
        // "splice05",
        // "splice06",
        // "splice07",
        // "splice08",
        // "splice09",
        // "squashfs01",
        // "ssetmask01",
        // "stack_clash",
        "stack_space",
        "starvation",
        "stat01",
        "stat01_64",
        "stat02",
        "stat02_64",
        // "stat03",
        // "stat03_64",
        // "statfs01",
        // "statfs01_64",
        // "statfs02",
        // "statfs02_64",
        // "statfs03",
        // "statfs03_64",
        // "statvfs01",
        // "statvfs02",
        "statx01",
        "statx02",
        "statx03",
        // "statx04",
        // "statx05",
        // "statx06",
        // "statx07",
        // "statx08",
        // "statx09",
        // "statx10",
        // "statx11",
        // "statx12",
        // "stime01",
        "stime02",
        "stream01",
        "stream02",
        "stream03",
        "stream04",
        "stream05",
        "stress",
        "support_numa",
        // "swapoff01",
        // "swapoff02",
        // "swapon01",
        // "swapon02",
        // "swapon03",
        // "swapping01",
        // "symlink01",
        // "symlink02",
        // "symlink03",
        // "symlink04",
        // "symlinkat01",
        // "sync01",
        // "sync_file_range01",
        // "sync_file_range02",
        // "syncfs01",
        "syscall01",
        "sysconf01",
        // "sysctl01",
        // "sysctl03",
        // "sysctl04",
        // "sysfs01",
        // "sysfs02",
        // "sysfs03",
        // "sysfs04",
        // "sysfs05",
        // "sysinfo01",
        // "sysinfo02",
        // "sysinfo03",
        // "syslog11",
        // "syslog12",
        // "tbi",
        // "tcindex01",
        // "tee01",
        // "tee02",
        // "test_1_to_1_accept_close",
        // "test_1_to_1_addrs",
        // "test_1_to_1_connect",
        // "test_1_to_1_connectx",
        // "test_1_to_1_events",
        // "test_1_to_1_initmsg_connect",
        // "test_1_to_1_nonblock",
        // "test_1_to_1_recvfrom",
        // "test_1_to_1_recvmsg",
        // "test_1_to_1_rtoinfo",
        // "test_1_to_1_send",
        // "test_1_to_1_sendmsg",
        // "test_1_to_1_sendto",
        // "test_1_to_1_shutdown",
        // "test_1_to_1_socket_bind_listen",
        // "test_1_to_1_sockopt",
        // "test_1_to_1_threads",
        // "test_assoc_abort",
        // "test_assoc_shutdown",
        // "test_autoclose",
        // "test_basic",
        // "test_basic_v6",
        // "test_connect",
        // "test_connectx",
        // "test_fragments",
        // "test_fragments_v6",
        // "test_getname",
        // "test_getname_v6",
        // "test_inaddr_any",
        // "test_inaddr_any_v6",
        // "test_ioctl",
        // "test_peeloff",
        // "test_peeloff_v6",
        // "test_recvmsg",
        // "test_sctp_sendrecvmsg",
        // "test_sctp_sendrecvmsg_v6",
        // "test_sockopt",
        // "test_sockopt_v6",
        // "test_tcp_style",
        // "test_tcp_style_v6",
        // "test_timetolive",
        // "test_timetolive_v6",
        // "testsf_c",
        // "testsf_c6",
        // "testsf_s",
        // "testsf_s6",
        "tgkill01",
        //"tgkill02",
        //"tgkill03",
        // "thp01",
        // "thp02",
        // "thp03",
        // "thp04",
        "time-schedule",
        // "time01",
        // "timed_forkbomb",
        // "timens01",
        // "timer_delete01",
        // "timer_delete02",
        // "timer_getoverrun01",
        // "timer_gettime01",
        // "timer_settime01",
        // "timer_settime02",
        // "timer_settime03",
        // "timerfd01",
        // "timerfd02",
        // "timerfd04",
        // "timerfd_create01",
        // "timerfd_gettime01",
        // "timerfd_settime01",
        // "timerfd_settime02",
        "times01",
        "times03",
        "tkill01",
        //"tkill02",
        // "tpci",
        // "trace_sched",
        // "truncate02",
        // "truncate02_64",
        // "truncate03",
        // "truncate03_64",
        // "tst_brk",
        // "tst_brkm",
        // "tst_cgctl",
        // "tst_checkpoint",
        // "tst_device",
        // "tst_exit",
        // "tst_fs_has_free",
        // "tst_fsfreeze",
        // "tst_get_free_pids",
        // "tst_get_median",
        // "tst_get_unused_port",
        // "tst_getconf",
        // "tst_hexdump",
        // "tst_kvcmp",
        // "tst_lockdown_enabled",
        // "tst_ncpus",
        // "tst_ncpus_conf",
        // "tst_ncpus_max",
        // "tst_random",
        // "tst_res",
        // "tst_resm",
        // "tst_rod",
        // "tst_secureboot_enabled",
        // "tst_supported_fs",
        // "uaccess",
        // "udp4-multi-diffip01",
        // "udp4-multi-diffip02",
        // "udp4-multi-diffip03",
        // "udp4-multi-diffip04",
        // "udp4-multi-diffip05",
        // "udp4-multi-diffip06",
        // "udp4-multi-diffip07",
        // "udp4-multi-diffnic01",
        // "udp4-multi-diffnic02",
        // "udp4-multi-diffnic03",
        // "udp4-multi-diffnic04",
        // "udp4-multi-diffnic05",
        // "udp4-multi-diffnic06",
        // "udp4-multi-diffnic07",
        // "udp4-multi-diffport01",
        // "udp4-multi-diffport02",
        // "udp4-multi-diffport03",
        // "udp4-multi-diffport04",
        // "udp4-multi-diffport05",
        // "udp4-multi-diffport06",
        // "udp4-multi-diffport07",
        // "udp4-uni-basic01",
        // "udp4-uni-basic02",
        // "udp4-uni-basic03",
        // "udp4-uni-basic04",
        // "udp4-uni-basic05",
        // "udp4-uni-basic06",
        // "udp4-uni-basic07",
        // "udp6-multi-diffip01",
        // "udp6-multi-diffip02",
        // "udp6-multi-diffip03",
        // "udp6-multi-diffip04",
        // "udp6-multi-diffip05",
        // "udp6-multi-diffip06",
        // "udp6-multi-diffip07",
        // "udp6-multi-diffnic01",
        // "udp6-multi-diffnic02",
        // "udp6-multi-diffnic03",
        // "udp6-multi-diffnic04",
        // "udp6-multi-diffnic05",
        // "udp6-multi-diffnic06",
        // "udp6-multi-diffnic07",
        // "udp6-multi-diffport01",
        // "udp6-multi-diffport02",
        // "udp6-multi-diffport03",
        // "udp6-multi-diffport04",
        // "udp6-multi-diffport05",
        // "udp6-multi-diffport06",
        // "udp6-multi-diffport07",
        // "udp6-uni-basic01",
        // "udp6-uni-basic02",
        // "udp6-uni-basic03",
        // "udp6-uni-basic04",
        // "udp6-uni-basic05",
        // "udp6-uni-basic06",
        // "udp6-uni-basic07",
        "uevent01",
        "uevent02",
        "uevent03",
        "ulimit01",
        "umask01",
        "umip_basic_test",
        "umount01",
        "umount02",
        "umount03",
        "umount2_01",
        "umount2_02",
        "uname01",
        "uname02",
        "uname04",
        "unlink05",
        "unlink07",
        "unlink08",
        // "unlink09",
        "unlinkat01",
        "unshare01",
        "unshare02",
        "userfaultfd01",
        // "userns01",
        // "userns02",
        // "userns03",
        // "userns04",
        // "userns05",
        // "userns06",
        // "userns06_capcheck",
        // "userns07",
        // "userns08",
        // "ustat01",
        // "ustat02",
        // "utime01",
        // "utime02",
        // "utime03",
        // "utime04",
        // "utime05",
        // "utime06",
        // "utime07",
        // "utimensat01",
        // "utimes01",
        // "utsname01",
        // "utsname02",
        // "utsname03",
        // "utsname04",
        // "verify_caps_exec",
        // "vfork",
        // "vfork01",
        // "vfork02",
        // "vhangup01",
        // "vhangup02",
        // "vma01",
        // "vma02",
        // "vma03",
        // "vma04",
        // "vma05_vdso",
        // "vmsplice01",
        // "vmsplice02",
        // "vmsplice03",
        // "vmsplice04",
        //"vsock01",
        "wait01",
        "wait02",
        "wait401",
        "wait402",
        "wait403",
        "waitid01",
        "waitid02",
        "waitid03",
        "waitid04",
        "waitid05",
        "waitid06",
        // "waitid07",
        // "waitid08",
        // "waitid09",
        // "waitid10",
        // "waitid11",
        "waitpid01",
        "waitpid03",
        "waitpid04",
        // "waitpid06",
        // "waitpid07",
        // "waitpid08",
        // "waitpid09",
        // "waitpid10",
        // "waitpid11",
        // "waitpid12",
        // "waitpid13",
        "write01",
        "write02",
        "write03",
        "write04",
        "write05",
        "write06",
        "writetest",
        // "writev01",
        // "writev02",
        // "writev03",
        // "writev05",
        // "writev06",
        // "writev07",
        //"zram03",
    ];

    for test_name in &ltp_tests {
        command(
            &format!("/musl/busybox echo RUN LTP CASE {}", test_name),
            home_dir.clone(),
        )
        .await;
        command(
            &format!("/musl/ltp/testcases/bin/{}", test_name),
            home_dir.clone(),
        )
        .await;
        command(
            &format!("/musl/busybox echo FAIL LTP CASE {} : 0", test_name),
            home_dir.clone(),
        )
        .await;
    }
}
async fn run_ltp_tests_glibc(glibc_home_dir: PathBuf) {
    // Complete list of LTP tests (excluding .sh files)
    let ltp_tests = vec![
        //"fork_exec_loop",
        //"abort01",
        // "abs01",
        // "accept01",
        // "accept02",
        // "accept03",
        // "accept4_01",
        //"access01",
        //"access02",
        // "access03",
        // "access04",
        // "acct01",
        // "acct02",
        // "acct02_helper",
        // "acl1",
        //"add_ipv6addr",
        // "add_key01",
        // "add_key02",
        // "add_key03",
        // "add_key04",
        // "add_key05",
        // "adjtimex01",
        // "adjtimex02",
        // "adjtimex03",
        // "af_alg01",
        // "af_alg02",
        // "af_alg03",
        // "af_alg04",
        // "af_alg05",
        // "af_alg06",
        // "af_alg07",
        //"aio-stress",
        //"aio01",
        //"aio02",
        //"aiocp",
        //"aiodio_append",
        //"aiodio_sparse",
        "alarm02",
        "alarm03",
        "alarm05",
        "alarm06",
        "alarm07",
        "arch_prctl01",
        "asapi_01",
        "asapi_02",
        "asapi_03",
        "aslr01",
        "atof01",
        "autogroup01",
        "bind01",
        "bind02",
        "bind03",
        "bind04",
        "bind05",
        "bind06",
        "block_dev",
        "bpf_map01",
        "bpf_prog01",
        "bpf_prog02",
        "bpf_prog03",
        "bpf_prog04",
        "bpf_prog05",
        "bpf_prog06",
        "bpf_prog07",
        "brk01",
        "brk02",
        // "cacheflush01",
        // "can_bcm01",
        // "can_filter",
        // "can_rcv_own_msgs",
        // "cap_bounds_r",
        // "cap_bounds_rw",
        // "cap_bset_inh_bounds",
        // "capget01",
        // "capget02",
        // "capset01",
        // "capset02",
        // "capset03",
        // "capset04",
        // "cfs_bandwidth01",
        // "cgroup_core01",
        // "cgroup_core02",
        // "cgroup_core03",
        // "cgroup_fj_proc",
        // "cgroup_regression_fork_processes",
        // "cgroup_regression_getdelays",
        // "cgroup_xattr",
        "chdir01",
        //"chdir04",
        //"check_envval",
        //"check_icmpv4_connectivity",
        //"check_icmpv6_connectivity",
        //"check_keepcaps",
        //"check_netem",
        //"check_pe",
        //"check_simple_capset",
        "chmod01",
        "chmod03",
        "chmod05",
        //"chmod06",
        //"chmod07",
        "chown01",
        "chown01_16",
        "chown02",
        "chown02_16",
        "chown03",
        "chown03_16",
        //"chown04",
        //"chown04_16",
        "chown05",
        "chown05_16",
        // "chroot01",
        // "chroot02",
        // "chroot03",
        // "chroot04",
        // "clock_adjtime01",
        // "clock_adjtime02",
        "clock_getres01",
        "clock_gettime01",
        "clock_gettime02",
        "clock_gettime03",
        "clock_gettime04",
        "clock_nanosleep01",
        "clock_nanosleep02",
        "clock_nanosleep03",
        "clock_nanosleep04",
        "clock_settime01",
        "clock_settime02",
        "clock_settime03",
        "clone01",
        "clone02",
        "clone03",
        //"clone04",
        //"clone05",
        "clone06",
        "clone07",
        "clone08",
        "clone09",
        "clone301",
        "clone302",
        "clone303",
        "close01",
        "close02",
        "close_range01",
        "close_range02",
        "confstr01",
        // "connect01",
        // "connect02",
        // "copy_file_range01",
        // "copy_file_range02",
        // "copy_file_range03",
        // "cpuacct_task",
        // "cpuctl_def_task01",
        // "cpuctl_def_task02",
        // "cpuctl_def_task03",
        // "cpuctl_def_task04",
        // "cpuctl_fj_cpu-hog",
        // "cpuctl_fj_simple_echo",
        // "cpuctl_latency_check_task",
        // "cpuctl_latency_test",
        // "cpuctl_test01",
        // "cpuctl_test02",
        // "cpuctl_test03",
        // "cpuctl_test04",
        // "cpufreq_boost",
        // "cpuhotplug_do_disk_write_loop",
        // "cpuhotplug_do_kcompile_loop",
        // "cpuhotplug_do_spin_loop",
        // "cpuhotplug_report_proc_interrupts",
        // "cpuset01",
        // "crash01",
        // "crash02",
        "creat01",
        "creat03",
        "creat04",
        //"creat05",
        //"creat06",
        //"creat07",
        //"creat07_child",
        // "creat08",
        // "creat09",
        // "create_datafile",
        //"create_file",
        // "crypto_user01",
        // "crypto_user02",
        // "cve-2014-0196",
        // "cve-2015-3290",
        // "cve-2016-10044",
        // "cve-2016-7042",
        // "cve-2016-7117",
        // "cve-2017-16939",
        // "cve-2017-17052",
        // "cve-2017-17053",
        // "cve-2017-2618",
        // "cve-2017-2671",
        // "cve-2022-4378",
        // "data",
        //"data_space",
        //"datafiles",
        // "delete_module01",
        // "delete_module02",
        // "delete_module03",
        // "dio_append",
        // "dio_read",
        // "dio_sparse",
        // "dio_truncate",
        // "diotest1",
        // "diotest2",
        // "diotest3",
        // "diotest4",
        // "diotest5",
        // "diotest6",
        // "dirty",
        // "dirtyc0w",
        // "dirtyc0w_child",
        // "dirtyc0w_shmem",
        // "dirtyc0w_shmem_child",
        // "dirtypipe",
        // "dma_thread_diotest",
        // "doio",
        "dup01",
        "dup02",
        "dup03",
        "dup04",
        "dup05",
        "dup06",
        "dup07",
        "dup201",
        "dup202",
        "dup203",
        "dup204",
        "dup205",
        "dup206",
        "dup207",
        "dup3_01",
        "dup3_02",
        "ebizzy",
        "eject_check_tray",
        "endian_switch01",
        // "epoll-ltp",
        "epoll_create01",
        "epoll_create02",
        "epoll_create1_01",
        "epoll_create1_02",
        "epoll_ctl01",
        //"epoll_ctl02",
        "epoll_ctl03",
        // "epoll_ctl04",
        // "epoll_ctl05",
        /*monkey */
        // "epoll_pwait01",
        // "epoll_pwait02",
        // "epoll_pwait03",
        // "epoll_pwait04",
        // "epoll_pwait05",
        // "epoll_wait01",
        // "epoll_wait02",
        // "epoll_wait03",
        // "epoll_wait04",
        // "epoll_wait05",
        // "epoll_wait06",
        // "epoll_wait07",
        // "event_generator",
        // "eventfd01",
        // "eventfd02",
        // "eventdio03",
        // "eventfd04",
        // "eventfd05",
        // "eventfd06",
        // "eventfd2_01",
        // "eventfd2_02",
        // "eventfd2_03",
        // "exec_with_inh",
        // "exec_without_inh",
        // "execl01",
        // "execl01_child",
        // "execle01",
        // "execle01_child",
        // "execlp01",
        // "execlp01_child",
        // "execv01",
        // "execv01_child",
        // "execve01",
        // "execve01_child",
        // "execve02",
        // "execve03",
        // "execve04",
        // "execve05",
        // "execve06",
        // "execve06_child",
        // "execve_child",
        // "execveat01",
        // "execveat02",
        // "execveat03",
        // "execveat_child",
        // "execveat_errno",
        // "execvp01",
        "execvp01_child",
        "exit01",
        "exit02",
        "exit_group01",
        "f00f",
        "faccessat01",
        "faccessat02",
        // "faccessat201",
        // "faccessat202",
        // "fallocate01",
        // "fallocate02",
        // "fallocate03",
        // "fallocate04",
        // "fallocate05",
        // "fallocate06",
        // "fanotify01",
        // "fanotify02",
        // "fanotify03",
        // "fanotify04",
        // "fanotify05",
        // "fanotify06",
        // "fanotify07",
        // "fanotify08",
        // "fanotify09",
        // "fanotify10",
        // "fanotify11",
        // "fanotify12",
        // "fanotify13",
        // "fanotify14",
        // "fanotify15",
        // "fanotify16",
        // "fanotify17",
        // "fanotify18",
        // "fanotify19",
        // "fanotify20",
        // "fanotify21",
        // "fanotify22",
        // "fanotify23",
        // "fanotify_child",
        // "fanout01",
        // "fchdir01",
        // "fchdir02",
        // "fchdir03",
        // "fchmod01",
        // "fchmod02",
        // "fchmod03",
        // "fchmod04",
        // "fchmod05",
        // "fchmod06",
        // "fchmodat01",
        // "fchmodat02",
        // "fchown01",
        // "fchown01_16",
        // "fchown02",
        // "fchown02_16",
        // "fchown03",
        // "fchown03_16",
        // "fchown04",
        // "fchown04_16",
        // "fchown05",
        // "fchown05_16",
        // "fchownat01",
        // "fchownat02",
        // "fcntl01",
        // "fcntl01_64",
        // "fcntl02",
        // "fcntl02_64",
        // "fcntl03",
        // "fcntl03_64",
        // "fcntl04",
        // "fcntl04_64",
        // "fcntl05",
        // "fcntl05_64",
        // "fcntl07",
        // "fcntl07_64",
        // "fcntl08",
        // "fcntl08_64",
        // "fcntl09",
        // "fcntl09_64",
        // "fcntl10",
        // "fcntl10_64",
        // "fcntl11",
        // "fcntl11_64",
        // "fcntl12",
        // "fcntl12_64",
        // "fcntl13",
        // "fcntl13_64",
        // "fcntl14",
        // "fcntl14_64",
        // "fcntl15",
        // "fcntl15_64",
        // "fcntl16",
        // "fcntl16_64",
        // "fcntl17",
        // "fcntl17_64",
        // "fcntl18",
        // "fcntl18_64",
        // "fcntl19",
        // "fcntl19_64",
        // "fcntl20",
        // "fcntl20_64",
        // "fcntl21",
        // "fcntl21_64",
        // "fcntl22",
        // "fcntl22_64",
        // "fcntl23",
        // "fcntl23_64",
        // "fcntl24",
        // "fcntl24_64",
        // "fcntl25",
        // "fcntl25_64",
        // "fcntl26",
        // "fcntl26_64",
        // "fcntl27",
        // "fcntl27_64",
        // "fcntl29",
        // "fcntl29_64",
        // "fcntl30",
        // "fcntl30_64",
        // "fcntl31",
        // "fcntl31_64",
        // "fcntl32",
        // "fcntl32_64",
        // "fcntl33",
        // "fcntl33_64",
        // "fcntl34",
        // "fcntl34_64",
        // "fcntl35",
        // "fcntl35_64",
        // "fcntl36",
        // "fcntl36_64",
        // "fcntl37",
        // "fcntl37_64",
        // "fcntl38",
        // "fcntl38_64",
        // "fcntl39",
        // "fcntl39_64",
        // "fdatasync01",
        // "fdatasync02",
        // "fdatasync03",
        // "fgetxattr01",
        // "fgetxattr02",
        // "fgetxattr03",
        // "finit_module01",
        // "finit_module02",
        // "flistxattr01",
        // "flistxattr02",
        // "flistxattr03",
        // "float_bessel",
        // "float_exp_log",
        // "float_iperb",
        // "float_power",
        // "float_trigo",
        "flock01",
        "flock02",
        "flock03",
        "flock04",
        "flock06",
        "fork01",
        "fork03",
        "fork04",
        "fork05",
        "fork07",
        "fork08",
        "fork09",
        "fork10",
        //"fork13",
        //"fork14",
        "fork_procs",
        "fpathconf01",
        // "fptest01",
        // "fptest02",
        // "frag",
        // "fremovexattr01",
        // "fremovexattr02",
        // "fs_di",
        //"fs_fill",
        //"fs_inod",
        //"fs_perms",
        // "fsconfig01",
        // "fsconfig02",
        // "fsconfig03",
        // "fsmount01",
        // "fsmount02",
        // "fsopen01",
        // "fsopen02",
        // "fspick01",
        // "fspick02",
        // "fsstress",
        "fstat02",
        "fstat02_64",
        "fstat03",
        "fstat03_64",
        "fstatat01",
        // "fstatfs01",
        // "fstatfs01_64",
        // "fstatfs02",
        // "fstatfs02_64",
        // "fsync01",
        // "fsync02",
        // "fsync03",
        // "fsync04",
        // "fsetxattr01",
        // "fsetxattr02",
        // "ftest01",
        // "ftest02",
        // "ftest03",
        // "ftest04",
        // "ftest05",
        // "ftest06",
        // "ftest07",
        // "ftest08",
        // "futex_cmp_requeue01",
        // "futex_cmp_requeue02",
        "futex_wait01",
        // "futex_wait02",
        // "futex_wait03",
        "futex_wait04",
        "futex_wait05",
        //"futex_wait_bitset01",
        // "futex_waitv01",
        // "futex_waitv02",
        // "futex_waitv03",
        "futex_wake01",
        "futex_wake02",
        //"futex_wake03",
        // "futex_wake04",
        // "futimesat01",
        // "fw_load",
        // "genacos",
        // "genasin",
        // "genatan",
        // "genatan2",
        // "genbessel",
        // "genceil",
        // "gencos",
        // "gencosh",
        // "genexp",
        // "genexp_log",
        // "genfabs",
        // "genfloor",
        // "genfmod",
        // "genfrexp",
        // "genhypot",
        // "geniperb",
        // "genj0",
        // "genj1",
        // "genldexp",
        // "genlgamma",
        // "genload",
        // "genlog",
        // "genlog10",
        // "genmodf",
        // "genpow",
        // "genpower",
        // "gensin",
        // "gensinh",
        // "gensqrt",
        // "gentan",
        // "gentanh",
        // "gentrigo",
        // "geny0",
        // "geny1",
        // "get_ifname",
        // "get_mempolicy01",
        // "get_mempolicy02",
        // "get_robust_list01",
        // "getaddrinfo_01",
        // "getcontext01",
        // "getcpu01",
        // "getcwd01",
        // "getcwd02",
        // "getcwd03",
        // // "getcwd04",
        // "getdents01",
        // "getdents02",
        "getdomainname01",
        //"getegid01",
        // "getegid01_16",
        "getegid02",
        "getegid02_16",
        //"geteuid01",
        "geteuid01_16",
        "geteuid02",
        "geteuid02_16",
        "getgid01",
        "getgid01_16",
        "getgid03",
        //"getgid03_16",
        // "getgroups01",
        // "getgroups01_16",
        // "getgroups03",
        // "getgroups03_16",
        "gethostbyname_r01",
        "gethostid01",
        "gethostname01",
        //"gethostname02",
        "getitimer01",
        //"getitimer02",
        "getpagesize01",
        //"getpeername01",
        "getpgid01",
        "getpgid02",
        "getpgrp01",
        "getpid01",
        "getpid02",
        "getppid01",
        "getppid02",
        //"getpriority01",
        //"getpriority02",
        // "getrandom01",
        "getrandom02",
        "getrandom03",
        "getrandom04",
        // "getrandom05",
        // "getresgid01",
        // "getresgid01_16",
        // "getresgid02",
        // "getresgid02_16",
        // "getresgid03",
        // "getresgid03_16",
        // "getresuid01",
        // "getresuid01_16",
        // "getresuid02",
        // "getresuid02_16",
        // "getresuid03",
        // "getresuid03_16",
        "getrlimit01",
        //"getrlimit02",
        //"getrlimit03",
        "getrusage01",
        //"getrusage02",
        //"getrusage03",
        //"getrusage03_child",
        //"getrusage04",
        //"getsid01",
        //"getsid02",
        // "getsockname01",
        // "getsockopt01",
        // "getsockopt02",
        // "gettid01",
        // "gettid02",
        // "gettimeofday01",
        // "gettimeofday02",
        "getuid01",
        //"getuid01_16",
        "getuid03",
        "getuid03_16",
        // "getxattr01",
        // "getxattr02",
        // "getxattr03",
        // "getxattr04",
        // "getxattr05",
        //"growfiles",
        // "hackbench",
        // "hangup01",
        // "ht_affinity",
        // "ht_enabled",
        // "hugefallocate01",
        // "hugefallocate02",
        // "hugefork01",
        //"hugefork02",
        // "hugemmap01",
        // "hugemmap02",
        // "hugemmap04",
        // "hugemmap05",
        // "hugemmap06",
        // "hugemmap07",
        // "hugemmap08",
        // "hugemmap09",
        // "hugemmap10",
        // "hugemmap11",
        // "hugemmap12",
        // "hugemmap13",
        // "hugemmap14",
        // "hugemmap15",
        // "hugemmap16",
        // "hugemmap17",
        // "hugemmap18",
        // "hugemmap19",
        // "hugemmap20",
        // "hugemmap21",
        // "hugemmap22",
        // "hugemmap23",
        // "hugemmap24",
        // "hugemmap25",
        // "hugemmap26",
        // "hugemmap27",
        // "hugemmap28",
        // "hugemmap29",
        // "hugemmap30",
        // "hugemmap31",
        // "hugemmap32",
        // "hugeshmat01",
        // "hugeshmat02",
        // "hugeshmat03",
        // "hugeshmat04",
        // "hugeshmat05",
        // "hugeshmctl01",
        // "hugeshmctl02",
        // "hugeshmctl03",
        // "hugeshmdt01",
        // "hugeshmget01",
        // "hugeshmget02",
        // "hugeshmget03",
        // "hugeshmget05",
        // "icmp4-multi-diffip01",
        // "icmp4-multi-diffip02",
        // "icmp4-multi-diffip03",
        // "icmp4-multi-diffip04",
        // "icmp4-multi-diffip05",
        // "icmp4-multi-diffip06",
        // "icmp4-multi-diffip07",
        // "icmp4-multi-diffnic01",
        // "icmp4-multi-diffnic02",
        // "icmp4-multi-diffnic03",
        // "icmp4-multi-diffnic04",
        // "icmp4-multi-diffnic05",
        // "icmp4-multi-diffnic06",
        // "icmp4-multi-diffnic07",
        // "icmp6-multi-diffip01",
        // "icmp6-multi-diffip02",
        // "icmp6-multi-diffip03",
        // "icmp6-multi-diffip04",
        // "icmp6-multi-diffip05",
        // "icmp6-multi-diffip06",
        // "icmp6-multi-diffip07",
        // "icmp6-multi-diffnic01",
        // "icmp6-multi-diffnic02",
        // "icmp6-multi-diffnic03",
        // "icmp6-multi-diffnic04",
        // "icmp6-multi-diffnic05",
        // "icmp6-multi-diffnic06",
        // "icmp6-multi-diffnic07",
        //"icmp_rate_limit01",
        // "init_module01",
        // "init_module02",
        //"initialize_if",
        "inode01",
        //"inode02",
        // "inotify01",
        // "inotify02",
        // "inotify03",
        // "inotify04",
        // "inotify05",
        // "inotify06",
        // "inotify07",
        // "inotify08",
        // "inotify09",
        // "inotify10",
        // "inotify11",
        // "inotify12",
        // "inotify_init1_01",
        // "inotify_init1_02",
        // "input01",
        // "input02",
        // "input03",
        // "input04",
        // "input05",
        // "input06",
        // "io_cancel01",
        // "io_cancel02",
        // "io_control01",
        // "io_destroy01",
        // "io_destroy02",
        // "io_getevents01",
        // "io_getevents02",
        // "io_pgetevents01",
        // "io_pgetevents02",
        // "io_setup01",
        // "io_setup02",
        // "io_submit01",
        // "io_submit02",
        // "io_submit03",
        // "io_uring01",
        // "io_uring02",
        // "ioctl01",
        // "ioctl02",
        // "ioctl03",
        // "ioctl04",
        // "ioctl05",
        // "ioctl06",
        // "ioctl07",
        // "ioctl08",
        // "ioctl09",
        // "ioctl_loop01",
        // "ioctl_loop02",
        // "ioctl_loop03",
        // "ioctl_loop04",
        // "ioctl_loop05",
        // "ioctl_loop06",
        // "ioctl_loop07",
        // "ioctl_ns01",
        // "ioctl_ns02",
        // "ioctl_ns03",
        // "ioctl_ns04",
        // "ioctl_ns05",
        // "ioctl_ns06",
        // "ioctl_ns07",
        // "ioctl_sg01",
        // "iogen",
        // "ioperm01",
        // "ioperm02",
        // "iopl01",
        // "iopl02",
        // "ioprio_get01",
        // "ioprio_set01",
        // "ioprio_set02",
        // "ioprio_set03",
        // "irqbalance01",
        // "kallsyms",
        // "kcmp01",
        // "kcmp02",
        // "kcmp03",
        // "kernbench",
        // "keyctl01",
        // "keyctl02",
        // "keyctl03",
        // "keyctl04",
        // "keyctl05",
        // "keyctl06",
        // "keyctl07",
        // "keyctl08",
        // "keyctl09",
        // "kill02",
        // "kill03",
        // "kill05",
        // "kill06",
        // "kill07",
        // "kill08",
        // "kill09",
        // "kill10",
        // "kill11",
        // "kill12",
        // "kill13",
        // "killall_icmp_traffic",
        // "killall_tcp_traffic",
        // "killall_udp_traffic",
        //"kmsg01",
        // "ksm01",
        // "ksm02",
        // "ksm03",
        // "ksm04",
        // "ksm05",
        // "ksm06",
        // "ksm07",
        // "lchown01",
        // "lchown01_16",
        // "lchown02",
        // "lchown02_16",
        // "lchown03",
        // "lchown03_16",
        // "leapsec01",
        "lftest",
        // "libcgroup_freezer",
        "link02",
        "link04",
        // "link05",
        // "link08",
        // "linkat01",
        // "linkat02",
        // "listen01",
        // "listxattr01",
        // "listxattr02",
        // "listxattr03",
        // "llistxattr01",
        // "llistxattr02",
        // "llistxattr03",
        "llseek01",
        "llseek02",
        "llseek03",
        "lseek01",
        "lseek02",
        "lseek07",
        // "lseek11",
        // "lstat01",
        // "lstat01_64",
        // "lstat02",
        // "lstat02_64",
        // "ltpClient",
        // "ltpServer",
        // "ltp_acpi",
        // "madvise01",
        // "madvise02",
        // "madvise03",
        // "madvise05",
        // "madvise06",
        // "madvise07",
        // "madvise08",
        // "madvise09",
        // "madvise10",
        // "madvise11",
        // "mallinfo01",
        // "mallinfo02",
        // "mallinfo2_01",
        // "mallocstress",
        // "mallopt01",
        // "max_map_count",
        // "mbind01",
        // "mbind02",
        // "mbind03",
        // "mbind04",
        // "mc_member_test",
        // "mc_recv",
        // "mc_send",
        // "mc_verify_opts",
        // "mc_verify_opts_error",
        // "meltdown",
        // "mem02",
        // "mem_process",
        // "membarrier01",
        // "memcg_control_test",
        // "memcg_failcnt",
        // "memcg_force_empty",
        // "memcg_limit_in_bytes",
        // "memcg_max_usage_in_bytes_test",
        // "memcg_memsw_limit_in_bytes_test",
        // "memcg_move_charge_at_immigrate_test",
        // "memcg_process",
        // "memcg_process_stress",
        // "memcg_stat_rss",
        // "memcg_stat_test",
        // "memcg_stress_test",
        // "memcg_subgroup_charge",
        // "memcg_test_1",
        // "memcg_test_2",
        // "memcg_test_3",
        // "memcg_test_4",
        // "memcg_usage_in_bytes_test",
        // "memcg_use_hierarchy_test",
        "memcmp01",
        // "memcontrol01",
        // "memcontrol02",
        // "memcontrol03",
        // "memcontrol04",
        // "memcpy01",
        // "memctl_test01",
        // "memfd_create01",
        // "memfd_create02",
        // "memfd_create03",
        // "memfd_create04",
        "memset01",
        "memtoy",
        "mesgq_nstest",
        // "migrate_pages01",
        // "migrate_pages02",
        // "migrate_pages03",
        // "min_free_kbytes",
        // "mincore01",
        // "mincore02",
        // "mincore03",
        // "mincore04",
        // "mkdir02",
        // "mkdir03",
        // "mkdir04",
        // "mkdir05",
        // "mkdir09",
        // "mkdirat01",
        // "mkdirat02",
        // "mknod01",
        // "mknod02",
        // "mknod03",
        // "mknod04",
        // "mknod05",
        // "mknod06",
        // "mknod07",
        // "mknod08",
        // "mknod09",
        // "mknodat01",
        // "mknodat02",
        // "mlock01",
        // "mlock02",
        // "mlock03",
        // "mlock04",
        // "mlock05",
        // "mlock201",
        // "mlock202",
        // "mlock203",
        // "mlockall01",
        // "mlockall02",
        // "mlockall03",
        // "mmap-corruption01",
        // "mmap001",
        // "mmap01",
        // "mmap02",
        // "mmap03",
        // "mmap04",
        // "mmap05",
        // "mmap06",
        // "mmap08",
        "mmap09",
        // "mmap1",
        // "mmap10",
        // "mmap11",
        // "mmap12",
        // "mmap13",
        // "mmap14",
        // "mmap15",
        // "mmap16",
        // "mmap17",
        "mmap18",
        // "mmap19",
        // "mmap2",
        // "mmap20",
        // "mmap3",
        // "mmapstress01",
        // "mmapstress02",
        // "mmapstress03",
        // "mmapstress04",
        // "mmapstress05",
        // "mmapstress06",
        // "mmapstress07",
        // "mmapstress08",
        // "mmapstress09",
        // "mmapstress10",
        // "mmstress",
        // "mmstress_dummy",
        // "modify_ldt01",
        // "modify_ldt02",
        // "modify_ldt03",
        // "mount01",
        // "mount02",
        // "mount03",
        // "mount03_suid_child",
        // "mount04",
        // "mount05",
        // "mount06",
        // "mount07",
        // "mount_setattr01",
        // "mountns01",
        // "mountns02",
        // "mountns03",
        // "mountns04",
        // "move_mount01",
        // "move_mount02",
        // "move_pages01",
        // "move_pages02",
        // "move_pages03",
        // "move_pages04",
        // "move_pages05",
        // "move_pages06",
        // "move_pages07",
        // "move_pages09",
        // "move_pages10",
        // "move_pages11",
        // "move_pages12",
        // "mq_notify01",
        // "mq_notify02",
        // "mq_notify03",
        // "mq_open01",
        // "mq_timedreceive01",
        // "mq_timedsend01",
        // "mq_unlink01",
        // "mqns_01",
        // "mqns_02",
        // "mqns_03",
        // "mqns_04",
        // "mremap01",
        // "mremap02",
        // "mremap03",
        // "mremap04",
        // "mremap05",
        // "mremap06",
        //"msg_comm",
        "msgctl01",
        "msgctl02",
        //"msgctl03",
        // "msgctl04",
        // "msgctl05",
        // "msgctl06",
        "msgctl12",
        "msgget01",
        "msgget02",
        "msgget03",
        "msgget04",
        "msgget05",
        "msgrcv01",
        "msgrcv02",
        // "msgrcv03",
        // "msgrcv05",
        // "msgrcv06",
        "msgrcv07",
        "msgrcv08",
        // "msgsnd01",
        // "msgsnd02",
        // "msgsnd05",
        // "msgsnd06",
        // "msgstress01",
        // "msync01",
        // "msync02",
        // "msync03",
        // "msync04",
        // "mtest01",
        // "munlock01",
        // "munlock02",
        // "munlockall01",
        // "munmap01",
        // "munmap02",
        // "munmap03",
        // "name_to_handle_at01",
        // "name_to_handle_at02",
        "nanosleep01",
        // "nanosleep02",
        // "nanosleep04",
        "netns_netlink",
        "netstress",
        "newuname01",
        // "nextafter01",
        // "nfs01_open_files",
        // "nfs04_create_file",
        // "nfs05_make_tree",
        // "nfs_flock",
        // "nfs_flock_dgen",
        // "nft01",
        // "nft02",
        // "nftw01",
        // "nftw6401",
        // "nice01",
        // "nice02",
        // "nice03",
        "nice04",
        "nice05",
        // "nptl01",
        // "ns-echoclient",
        // "ns-icmp_redirector",
        // "ns-icmpv4_sender",
        // "ns-icmpv6_sender",
        // "ns-igmp_querier",
        // "ns-mcast_join",
        // "ns-mcast_receiver",
        // "ns-tcpclient",
        // "ns-tcpserver",
        // "ns-udpclient",
        // "ns-udpsender",
        // "ns-udpserver",
        "open01",
        "open02",
        "open03",
        "open04",
        "open06",
        // "open07",
        // "open08",
        // "open09",
        // "open10",
        // "open11",
        // "open12",
        // "open12_child",
        // "open13",
        // "open14",
        // "open_by_handle_at01",
        // "open_by_handle_at02",
        // "open_tree01",
        // "open_tree02",
        "openat01",
        // "openat02",
        // "openat02_child",
        // "openat03",
        // "openat04",
        // "openat201",
        // "openat202",
        // "openat203",
        "openfile",
        "overcommit_memory",
        "page01",
        "page02",
        "pathconf01",
        "pathconf02",
        // "pause01",
        // "pause02",
        // "pause03",
        // "pcrypt_aead01",
        // "pec_listener",
        // "perf_event_open01",
        // "perf_event_open02",
        // "perf_event_open03",
        // "personality01",
        // "personality02",
        // "pidfd_getfd01",
        // "pidfd_getfd02",
        // "pidfd_open01",
        // "pidfd_open02",
        // "pidfd_open03",
        // "pidfd_open04",
        // "pidfd_send_signal01",
        // "pidfd_send_signal02",
        // "pidfd_send_signal03",
        // "pidns01",
        // "pidns02",
        // "pidns03",
        // "pidns04",
        // "pidns05",
        // "pidns06",
        // "pidns10",
        // "pidns12",
        // "pidns13",
        // "pidns16",
        // "pidns17",
        // "pidns20",
        // "pidns30",
        // "pidns31",
        // "pidns32",
        // "pids_task1",
        // "pids_task2",
        "pipe01",
        // "pipe02",
        // "pipe03",
        // "pipe04",
        // "pipe05",
        // "pipe06",
        // "pipe07",
        // "pipe08",
        // "pipe09",
        "pipe10",
        "pipe11",
        "pipe12",
        //"pipe13",
        "pipe14",
        "pipe15",
        "pipe2_01",
        //"pipe2_02",
        //"pipe2_02_child",
        //"pipe2_04",
        //"pipeio",
        //"pivot_root01",
        //"pkey01",
        "poll01",
        "poll02",
        "posix_fadvise01",
        "posix_fadvise01_64",
        "posix_fadvise02",
        "posix_fadvise02_64",
        "posix_fadvise03",
        "posix_fadvise03_64",
        // "posix_fadvise04",
        // "posix_fadvise04_64",
        //"ppoll01",
        "prctl01",
        "prctl02",
        //"prctl03",
        "prctl04",
        "prctl05",
        "prctl06",
        "prctl06_execve",
        "prctl07",
        "prctl08",
        "prctl09",
        "prctl10",
        "pread01",
        // "pread01_64",
        // "pread02",
        // "pread02_64",
        // "preadv01",
        // "preadv01_64",
        // "preadv02",
        // "preadv02_64",
        // "preadv03",
        // "preadv03_64",
        // "preadv201",
        // "preadv201_64",
        // "preadv202",
        // "preadv202_64",
        // "preadv203",
        // "preadv203_64",
        // "print_caps",
        "proc01",
        "proc_sched_rt01",
        "process_madvise01",
        "process_vm01",
        // "process_vm_readv02",
        // "process_vm_readv03",
        // "process_vm_writev02",
        // "profil01",
        // "prot_hsymlinks",
        "pselect01",
        "pselect01_64",
        // "pselect02",
        // "pselect02_64",
        // "tcp4-multi-diffip01",
        // "tcp4-multi-diffip02",
        // "tcp4-multi-diffip03",
        // "tcp4-multi-diffip04",
        // "tcp4-multi-diffip05",
        // "tcp4-multi-diffip06",
        // "tcp4-multi-diffip07",
        // "tcp4-multi-diffip08",
        // "tcp4-multi-diffip09",
        // "tcp4-multi-diffip10",
        // "tcp4-multi-diffip11",
        // "tcp4-multi-diffip12",
        // "tcp4-multi-diffip13",
        // "tcp4-multi-diffip14",
        // "tcp4-multi-diffnic01",
        // "tcp4-multi-diffnic02",
        // "tcp4-multi-diffnic03",
        // "tcp4-multi-diffnic04",
        // "tcp4-multi-diffnic05",
        // "tcp4-multi-diffnic06",
        // "tcp4-multi-diffnic07",
        // "tcp4-multi-diffnic08",
        // "tcp4-multi-diffnic09",
        // "tcp4-multi-diffnic10",
        // "tcp4-multi-diffnic11",
        // "tcp4-multi-diffnic12",
        // "tcp4-multi-diffnic13",
        // "tcp4-multi-diffnic14",
        // "tcp4-multi-diffport01",
        // "tcp4-multi-diffport02",
        // "tcp4-multi-diffport03",
        // "tcp4-multi-diffport04",
        // "tcp4-multi-diffport05",
        // "tcp4-multi-diffport06",
        // "tcp4-multi-diffport07",
        // "tcp4-multi-diffport08",
        // "tcp4-multi-diffport09",
        // "tcp4-multi-diffport10",
        // "tcp4-multi-diffport11",
        // "tcp4-multi-diffport12",
        // "tcp4-multi-diffport13",
        // "tcp4-multi-diffport14",
        // "tcp4-multi-sameport01",
        // "tcp4-multi-sameport02",
        // "tcp4-multi-sameport03",
        // "tcp4-multi-sameport04",
        // "tcp4-multi-sameport05",
        // "tcp4-multi-sameport06",
        // "tcp4-multi-sameport07",
        // "tcp4-multi-sameport08",
        // "tcp4-multi-sameport09",
        // "tcp4-multi-sameport10",
        // "tcp4-multi-sameport11",
        // "tcp4-multi-sameport12",
        // "tcp4-multi-sameport13",
        // "tcp4-multi-sameport14",
        // "tcp4-uni-basic01",
        // "tcp4-uni-basic02",
        // "tcp4-uni-basic03",
        // "tcp4-uni-basic04",
        // "tcp4-uni-basic05",
        // "tcp4-uni-basic06",
        // "tcp4-uni-basic07",
        // "tcp4-uni-basic08",
        // "tcp4-uni-basic09",
        // "tcp4-uni-basic10",
        // "tcp4-uni-basic11",
        // "tcp4-uni-basic12",
        // "tcp4-uni-basic13",
        // "tcp4-uni-basic14",
        // "tcp4-uni-dsackoff01",
        // "tcp4-uni-dsackoff02",
        // "tcp4-uni-dsackoff03",
        // "tcp4-uni-dsackoff04",
        // "tcp4-uni-dsackoff05",
        // "tcp4-uni-dsackoff06",
        // "tcp4-uni-dsackoff07",
        // "tcp4-uni-dsackoff08",
        // "tcp4-uni-dsackoff09",
        // "tcp4-uni-dsackoff10",
        // "tcp4-uni-dsackoff11",
        // "tcp4-uni-dsackoff12",
        // "tcp4-uni-dsackoff13",
        // "tcp4-uni-dsackoff14",
        // "tcp4-uni-pktlossdup01",
        // "tcp4-uni-pktlossdup02",
        // "tcp4-uni-pktlossdup03",
        // "tcp4-uni-pktlossdup04",
        // "tcp4-uni-pktlossdup05",
        // "tcp4-uni-pktlossdup06",
        // "tcp4-uni-pktlossdup07",
        // "tcp4-uni-pktlossdup08",
        // "tcp4-uni-pktlossdup09",
        // "tcp4-uni-pktlossdup10",
        // "tcp4-uni-pktlossdup11",
        // "tcp4-uni-pktlossdup12",
        // "tcp4-uni-pktlossdup13",
        // "tcp4-uni-pktlossdup14",
        // "tcp4-uni-sackoff01",
        // "tcp4-uni-sackoff02",
        // "tcp4-uni-sackoff03",
        // "tcp4-uni-sackoff04",
        // "tcp4-uni-sackoff05",
        // "tcp4-uni-sackoff06",
        // "tcp4-uni-sackoff07",
        // "tcp4-uni-sackoff08",
        // "tcp4-uni-sackoff09",
        // "tcp4-uni-sackoff10",
        // "tcp4-uni-sackoff11",
        // "tcp4-uni-sackoff12",
        // "tcp4-uni-sackoff13",
        // "tcp4-uni-sackoff14",
        // "tcp4-uni-smallsend01",
        // "tcp4-uni-smallsend02",
        // "tcp4-uni-smallsend03",
        // "tcp4-uni-smallsend04",
        // "tcp4-uni-smallsend05",
        // "tcp4-uni-smallsend06",
        // "tcp4-uni-smallsend07",
        // "tcp4-uni-smallsend08",
        // "tcp4-uni-smallsend09",
        // "tcp4-uni-smallsend10",
        // "tcp4-uni-smallsend11",
        // "tcp4-uni-smallsend12",
        // "tcp4-uni-smallsend13",
        // "tcp4-uni-smallsend14",
        // "tcp4-uni-tso01",
        // "tcp4-uni-tso02",
        // "tcp4-uni-tso03",
        // "tcp4-uni-tso04",
        // "tcp4-uni-tso05",
        // "tcp4-uni-tso06",
        // "tcp4-uni-tso07",
        // "tcp4-uni-tso08",
        // "tcp4-uni-tso09",
        // "tcp4-uni-tso10",
        // "tcp4-uni-tso11",
        // "tcp4-uni-tso12",
        // "tcp4-uni-tso13",
        // "tcp4-uni-tso14",
        // "tcp4-uni-winscale01",
        // "tcp4-uni-winscale02",
        // "tcp4-uni-winscale03",
        // "tcp4-uni-winscale04",
        // "tcp4-uni-winscale05",
        // "tcp4-uni-winscale06",
        // "tcp4-uni-winscale07",
        // "tcp4-uni-winscale08",
        // "tcp4-uni-winscale09",
        // "tcp4-uni-winscale10",
        // "tcp4-uni-winscale11",
        // "tcp4-uni-winscale12",
        // "tcp4-uni-winscale13",
        // "tcp4-uni-winscale14",
        // "tcp6-multi-diffip01",
        // "tcp6-multi-diffip02",
        // "tcp6-multi-diffip03",
        // "tcp6-multi-diffip04",
        // "tcp6-multi-diffip05",
        // "tcp6-multi-diffip06",
        // "tcp6-multi-diffip07",
        // "tcp6-multi-diffip08",
        // "tcp6-multi-diffip09",
        // "tcp6-multi-diffip10",
        // "tcp6-multi-diffip11",
        // "tcp6-multi-diffip12",
        // "tcp6-multi-diffip13",
        // "tcp6-multi-diffip14",
        // "tcp6-multi-diffnic01",
        // "tcp6-multi-diffnic02",
        // "tcp6-multi-diffnic03",
        // "tcp6-multi-diffnic04",
        // "tcp6-multi-diffnic05",
        // "tcp6-multi-diffnic06",
        // "tcp6-multi-diffnic07",
        // "tcp6-multi-diffnic08",
        // "tcp6-multi-diffnic09",
        // "tcp6-multi-diffnic10",
        // "tcp6-multi-diffnic11",
        // "tcp6-multi-diffnic12",
        // "tcp6-multi-diffnic13",
        // "tcp6-multi-diffnic14",
        // "tcp6-multi-diffport01",
        // "tcp6-multi-diffport02",
        // "tcp6-multi-diffport03",
        // "tcp6-multi-diffport04",
        // "tcp6-multi-diffport05",
        // "tcp6-multi-diffport06",
        // "tcp6-multi-diffport07",
        // "tcp6-multi-diffport08",
        // "tcp6-multi-diffport09",
        // "tcp6-multi-diffport10",
        // "tcp6-multi-diffport11",
        // "tcp6-multi-diffport12",
        // "tcp6-multi-diffport13",
        // "tcp6-multi-diffport14",
        // "tcp6-multi-sameport01",
        // "tcp6-multi-sameport02",
        // "tcp6-multi-sameport03",
        // "tcp6-multi-sameport04",
        // "tcp6-multi-sameport05",
        // "tcp6-multi-sameport06",
        // "tcp6-multi-sameport07",
        // "tcp6-multi-sameport08",
        // "tcp6-multi-sameport09",
        // "tcp6-multi-sameport10",
        // "tcp6-multi-sameport11",
        // "tcp6-multi-sameport12",
        // "tcp6-multi-sameport13",
        // "tcp6-multi-sameport14",
        // "tcp6-uni-basic01",
        // "tcp6-uni-basic02",
        // "tcp6-uni-basic03",
        // "tcp6-uni-basic04",
        // "tcp6-uni-basic05",
        // "tcp6-uni-basic06",
        // "tcp6-uni-basic07",
        // "tcp6-uni-basic08",
        // "tcp6-uni-basic09",
        // "tcp6-uni-basic10",
        // "tcp6-uni-basic11",
        // "tcp6-uni-basic12",
        // "tcp6-uni-basic13",
        // "tcp6-uni-basic14",
        // "tcp6-uni-dsackoff01",
        // "tcp6-uni-dsackoff02",
        // "tcp6-uni-dsackoff03",
        // "tcp6-uni-dsackoff04",
        // "tcp6-uni-dsackoff05",
        // "tcp6-uni-dsackoff06",
        // "tcp6-uni-dsackoff07",
        // "tcp6-uni-dsackoff08",
        // "tcp6-uni-dsackoff09",
        // "tcp6-uni-dsackoff10",
        // "tcp6-uni-dsackoff11",
        // "tcp6-uni-dsackoff12",
        // "tcp6-uni-dsackoff13",
        // "tcp6-uni-dsackoff14",
        // "tcp6-uni-pktlossdup01",
        // "tcp6-uni-pktlossdup02",
        // "tcp6-uni-pktlossdup03",
        // "tcp6-uni-pktlossdup04",
        // "tcp6-uni-pktlossdup05",
        // "tcp6-uni-pktlossdup06",
        // "tcp6-uni-pktlossdup07",
        // "tcp6-uni-pktlossdup08",
        // "tcp6-uni-pktlossdup09",
        // "tcp6-uni-pktlossdup10",
        // "tcp6-uni-pktlossdup11",
        // "tcp6-uni-pktlossdup12",
        // "tcp6-uni-pktlossdup13",
        // "tcp6-uni-pktlossdup14",
        // "tcp6-uni-sackoff01",
        // "tcp6-uni-sackoff02",
        // "tcp6-uni-sackoff03",
        // "tcp6-uni-sackoff04",
        // "tcp6-uni-sackoff05",
        // "tcp6-uni-sackoff06",
        // "tcp6-uni-sackoff07",
        // "tcp6-uni-sackoff08",
        // "tcp6-uni-sackoff09",
        // "tcp6-uni-sackoff10",
        // "tcp6-uni-sackoff11",
        // "tcp6-uni-sackoff12",
        // "tcp6-uni-sackoff13",
        // "tcp6-uni-sackoff14",
        // "tcp6-uni-smallsend01",
        // "tcp6-uni-smallsend02",
        // "tcp6-uni-smallsend03",
        // "tcp6-uni-smallsend04",
        // "tcp6-uni-smallsend05",
        // "tcp6-uni-smallsend06",
        // "tcp6-uni-smallsend07",
        // "tcp6-uni-smallsend08",
        // "tcp6-uni-smallsend09",
        // "tcp6-uni-smallsend10",
        // "tcp6-uni-smallsend11",
        // "tcp6-uni-smallsend12",
        // "tcp6-uni-smallsend13",
        // "tcp6-uni-smallsend14",
        // "tcp6-uni-tso01",
        // "tcp6-uni-tso02",
        // "tcp6-uni-tso03",
        // "tcp6-uni-tso04",
        // "tcp6-uni-tso05",
        // "tcp6-uni-tso06",
        // "tcp6-uni-tso07",
        // "tcp6-uni-tso08",
        // "tcp6-uni-tso09",
        // "tcp6-uni-tso10",
        // "tcp6-uni-tso11",
        // "tcp6-uni-tso12",
        // "tcp6-uni-tso13",
        // "tcp6-uni-tso14",
        // "tcp6-uni-winscale01",
        // "tcp6-uni-winscale02",
        // "tcp6-uni-winscale03",
        // "tcp6-uni-winscale04",
        // "tcp6-uni-winscale05",
        // "tcp6-uni-winscale06",
        // "tcp6-uni-winscale07",
        // "tcp6-uni-winscale08",
        // "tcp6-uni-winscale09",
        // "tcp6-uni-winscale10",
        // "tcp6-uni-winscale11",
        // "tcp6-uni-winscale12",
        // "tcp6-uni-winscale13",
        // "tcp6-uni-winscale14",
        "pselect03",
        "pselect03_64",
        "pt_test",
        //"ptem01",
        "pth_str01",
        "pth_str02",
        "pth_str03",
        // "pthcli",
        // "pthserv",
        "ptrace01",
        //"ptrace02",
        "ptrace03",
        "ptrace04",
        //"ptrace05",
        // "ptrace06",
        // "ptrace07",
        // "ptrace08",
        // "ptrace09",
        // "ptrace10",
        // "ptrace11",
        // "pty01",
        // "pty02",
        // "pty03",
        // "pty04",
        // "pty05",
        // "pty06",
        // "pty07",
        "pwrite01",
        "pwrite01_64",
        // "pwrite02",
        // "pwrite02_64",
        "pwrite03",
        "pwrite03_64",
        "pwrite04",
        "pwrite04_64",
        // "pwritev01",
        // "pwritev01_64",
        // "pwritev02",
        // "pwritev02_64",
        // "pwritev03",
        // "pwritev03_64",
        // "pwritev201",
        // "pwritev201_64",
        // "pwritev202",
        // "pwritev202_64",
        // "quotactl01",
        // "quotactl02",
        // "quotactl03",
        // "quotactl04",
        // "quotactl05",
        // "quotactl06",
        // "quotactl07",
        // "quotactl08",
        // "quotactl09",
        "read01",
        "read02",
        //"read03",
        "read04",
        //"read_all",
        "readahead01",
        "readahead02",
        "readdir01",
        "readdir21",
        "readlink01",
        "readlink03",
        "readlinkat01",
        "readlinkat02",
        "readv01",
        //"readv02",
        "realpath01",
        // "reboot01",
        // "reboot02",
        // "recv01",
        //"recvfrom01",
        // "recvmmsg01",
        // "recvmsg01",
        // "recvmsg02",
        // "recvmsg03",
        // "remap_file_pages01",
        // "remap_file_pages02",
        // "removexattr01",
        // "removexattr02",
        // "rename01",
        // "rename03",
        // "rename04",
        // "rename05",
        // "rename06",
        // "rename07",
        // "rename08",
        // "rename09",
        // "rename10",
        // "rename11",
        // "rename12",
        // "rename13",
        // "rename14",
        // "renameat01",
        // "renameat201",
        // "renameat202",
        // "request_key01",
        // "request_key02",
        // "request_key03",
        // "request_key04",
        // "request_key05",
        // "rmdir01",
        // "rmdir02",
        // "rmdir03",
        // "route-change-netlink",
        // "rt_sigaction01",
        // "rt_sigaction02",
        // "rt_sigaction03",
        // "rt_sigprocmask01",
        // "rt_sigprocmask02",
        // "rt_sigqueueinfo01",
        // "rt_sigsuspend01",
        // "rtc01",
        // "rtc02",
        //"rwtest",
        "sbrk01",
        "sbrk02",
        //"sbrk03",
        //"sched_datafile",
        // "sched_driver",
        // "sched_get_priority_max01",
        // "sched_get_priority_max02",
        // "sched_get_priority_min01",
        // "sched_get_priority_min02",
        // "sched_getaffinity01",
        // "sched_getattr01",
        // "sched_getattr02",
        // "sched_getparam01",
        // "sched_getparam03",
        // "sched_getscheduler01",
        // "sched_getscheduler02",
        // "sched_rr_get_interval01",
        // "sched_rr_get_interval02",
        // "sched_rr_get_interval03",
        // "sched_setaffinity01",
        // "sched_setattr01",
        // "sched_setparam01",
        // "sched_setparam02",
        // "sched_setparam03",
        // "sched_setparam04",
        // "sched_setparam05",
        // "sched_setscheduler01",
        // "sched_setscheduler02",
        // "sched_setscheduler03",
        // "sched_setscheduler04",
        // "sched_tc0",
        // "sched_tc1",
        // "sched_tc2",
        // "sched_tc3",
        // "sched_tc4",
        // "sched_tc5",
        // "sched_tc6",
        // "sched_yield01",
        // "sctp_big_chunk",
        // "select01",
        "select02",
        // "select03",
        // "select04",
        "sem_comm",
        "sem_nstest",
        "semctl01",
        "semctl02",
        "semctl03",
        // "semctl04",
        // "semctl05",
        // "semctl06",
        "semctl07",
        "semctl08",
        // "semctl09",
        "semget01",
        // "semget02",
        // "semget05",
        // "semop01",
        // "semop02",
        // "semop03",
        // "semop04",
        // "semop05",
        //"semtest_2ns",
        // "send01",
        // "send02",
        // "sendfile02",
        // "sendfile02_64",
        // "sendfile03",
        // "sendfile03_64",
        // "sendfile04",
        // "sendfile04_64",
        // "sendfile05",
        // "sendfile05_64",
        // "sendfile06",
        // "sendfile06_64",
        // "sendfile07",
        // "sendfile07_64",
        // "sendfile08",
        // "sendfile08_64",
        // "sendfile09",
        // "sendfile09_64",
        // "sendmmsg01",
        // "sendmmsg02",
        // "sendmsg01",
        // "sendmsg02",
        // "sendmsg03",
        // "sendto01",
        // "sendto02",
        // "sendto03",
        //"set_ipv4addr",
        // "set_mempolicy01",
        // "set_mempolicy02",
        // "set_mempolicy03",
        // "set_mempolicy04",
        // "set_mempolicy05",
        // "set_robust_list01",
        // "set_thread_area01",
        // "set_tid_address01",
        // "setdomainname01",
        // "setdomainname02",
        // "setdomainname03",
        // "setegid01",
        // "setegid02",
        // "setfsgid01",
        // "setfsgid01_16",
        // "setfsgid02",
        // "setfsgid02_16",
        // "setfsgid03",
        // "setfsgid03_16",
        // "setfsuid01",
        // "setfsuid01_16",
        // "setfsuid02",
        // "setfsuid02_16",
        // "setfsuid03",
        // "setfsuid03_16",
        // "setfsuid04",
        // "setfsuid04_16",
        "setgid01",
        "setgid01_16",
        "setgid02",
        "setgid02_16",
        "setgid03",
        "setgid03_16",
        "setgroups01",
        "setgroups01_16",
        // "setgroups02",
        // "setgroups02_16",
        // "setgroups03",
        // "setgroups03_16",
        // "setgroups04",
        // "setgroups04_16",
        // "sethostname01",
        // "sethostname02",
        // "sethostname03",
        // "setitimer01",
        //"setitimer02",
        "setns01",
        "setns02",
        "setpgid01",
        "setpgid02",
        // "setpgid03",
        // "setpgid03_child",
        "setpgrp01",
        // "setpgrp02",
        // "setpriority01",
        // "setpriority02",
        // "setregid01",
        // "setregid01_16",
        "setregid02",
        // "setregid02_16",
        // "setregid03",
        // "setregid03_16",
        // "setregid04",
        // "setregid04_16",
        // "setresgid01",
        // "setresgid01_16",
        // "setresgid02",
        // "setresgid02_16",
        // "setresgid03",
        // "setresgid03_16",
        // "setresgid04",
        // "setresgid04_16",
        // "setresuid01",
        // "setresuid01_16",
        // "setresuid02",
        // "setresuid02_16",
        // "setresuid03",
        // "setresuid03_16",
        "setresuid04",
        // "setresuid04_16",
        // "setresuid05",
        // "setresuid05_16",
        // "setreuid01",
        // "setreuid01_16",
        // "setreuid02",
        // "setreuid02_16",
        "setreuid03",
        // "setreuid03_16",
        // "setreuid04",
        // "setreuid04_16",
        "setreuid05",
        "setreuid05_16",
        "setreuid06",
        "setreuid06_16",
        "setreuid07",
        "setreuid07_16",
        "setrlimit01",
        "setrlimit02",
        "setrlimit03",
        // "setrlimit04",
        // "setrlimit05",
        // "setrlimit06",
        // "setsid01",
        // "setsockopt01",
        // "setsockopt02",
        "setsockopt03",
        "setsockopt04",
        "setsockopt05",
        // "setsockopt06",
        // "setsockopt07",
        // "setsockopt08",
        // "setsockopt09",
        // "setsockopt10",
        "settimeofday01",
        "settimeofday02",
        // "setuid01",
        // "setuid01_16",
        // "setuid03",
        // "setuid03_16",
        // "setuid04",
        // "setuid04_16",
        // "setxattr01",
        // "setxattr03",
        // "sgetmask01",
        // "shm_comm",
        // "shm_test",
        // "shmat01",
        // "shmat02",
        // "shmat03",
        // "shmat04",
        // "shmat1",
        // "shmctl01",
        // "shmctl02",
        // "shmctl03",
        // "shmctl04",
        // "shmctl05",
        // "shmctl06",
        // "shmctl07",
        // "shmctl08",
        // "shmdt01",
        // "shmdt02",
        // "shmem_2nstest",
        // "shmget02",
        // "shmget03",
        // "shmget04",
        // "shmget05",
        "shmget06",
        "shmnstest",
        // "shmt02",
        // "shmt03",
        // "shmt04",
        // "shmt05",
        // "shmt06",
        // "shmt07",
        // "shmt08",
        // "shmt09",
        // "shmt10",
        // "sigaction01",
        // "sigaction02",
        "sigaltstack01",
        "sigaltstack02",
        "sighold02",
        "signal01",
        "signal02",
        "signal03",
        "signal04",
        "signal05",
        "signal06",
        // "signalfd01",
        // "signalfd4_01",
        // "signalfd4_02",
        // "sigpending02",
        // "sigprocmask01",
        // "sigrelse01",
        // "sigsuspend01",
        //"sigtimedwait01",
        "sigwait01",
        // "sigwaitinfo01",
        //"smack_notroot",
        // "snd_seq01",
        // "snd_timer01",
        "socket01",
        "socket02",
        // "socketcall01",
        // "socketcall02",
        // "socketcall03",
        // "socketpair01",
        // "socketpair02",
        // "sockioctl01",
        // "splice01",
        // "splice02",
        // "splice03",
        // "splice04",
        // "splice05",
        // "splice06",
        // "splice07",
        // "splice08",
        // "splice09",
        // "squashfs01",
        // "ssetmask01",
        // "stack_clash",
        "stack_space",
        "starvation",
        "stat01",
        "stat01_64",
        "stat02",
        "stat02_64",
        // "stat03",
        // "stat03_64",
        // "statfs01",
        // "statfs01_64",
        // "statfs02",
        // "statfs02_64",
        // "statfs03",
        // "statfs03_64",
        // "statvfs01",
        // "statvfs02",
        "statx01",
        "statx02",
        "statx03",
        // "statx04",
        // "statx05",
        // "statx06",
        // "statx07",
        // "statx08",
        // "statx09",
        // "statx10",
        // "statx11",
        // "statx12",
        // "stime01",
        "stime02",
        "stream01",
        "stream02",
        "stream03",
        "stream04",
        "stream05",
        "stress",
        "support_numa",
        // "swapoff01",
        // "swapoff02",
        // "swapon01",
        // "swapon02",
        // "swapon03",
        // "swapping01",
        // "symlink01",
        // "symlink02",
        // "symlink03",
        // "symlink04",
        // "symlinkat01",
        // "sync01",
        // "sync_file_range01",
        // "sync_file_range02",
        // "syncfs01",
        "syscall01",
        "sysconf01",
        // "sysctl01",
        // "sysctl03",
        // "sysctl04",
        // "sysfs01",
        // "sysfs02",
        // "sysfs03",
        // "sysfs04",
        // "sysfs05",
        // "sysinfo01",
        // "sysinfo02",
        // "sysinfo03",
        // "syslog11",
        // "syslog12",
        // "tbi",
        // "tcindex01",
        // "tee01",
        // "tee02",
        // "test_1_to_1_accept_close",
        // "test_1_to_1_addrs",
        // "test_1_to_1_connect",
        // "test_1_to_1_connectx",
        // "test_1_to_1_events",
        // "test_1_to_1_initmsg_connect",
        // "test_1_to_1_nonblock",
        // "test_1_to_1_recvfrom",
        // "test_1_to_1_recvmsg",
        // "test_1_to_1_rtoinfo",
        // "test_1_to_1_send",
        // "test_1_to_1_sendmsg",
        // "test_1_to_1_sendto",
        // "test_1_to_1_shutdown",
        // "test_1_to_1_socket_bind_listen",
        // "test_1_to_1_sockopt",
        // "test_1_to_1_threads",
        // "test_assoc_abort",
        // "test_assoc_shutdown",
        // "test_autoclose",
        // "test_basic",
        // "test_basic_v6",
        // "test_connect",
        // "test_connectx",
        // "test_fragments",
        // "test_fragments_v6",
        // "test_getname",
        // "test_getname_v6",
        // "test_inaddr_any",
        // "test_inaddr_any_v6",
        // "test_ioctl",
        // "test_peeloff",
        // "test_peeloff_v6",
        // "test_recvmsg",
        // "test_sctp_sendrecvmsg",
        // "test_sctp_sendrecvmsg_v6",
        // "test_sockopt",
        // "test_sockopt_v6",
        // "test_tcp_style",
        // "test_tcp_style_v6",
        // "test_timetolive",
        // "test_timetolive_v6",
        // "testsf_c",
        // "testsf_c6",
        // "testsf_s",
        // "testsf_s6",
        "tgkill01",
        //"tgkill02",
        //"tgkill03",
        // "thp01",
        // "thp02",
        // "thp03",
        // "thp04",
        "time-schedule",
        // "time01",
        // "timed_forkbomb",
        // "timens01",
        // "timer_delete01",
        // "timer_delete02",
        // "timer_getoverrun01",
        // "timer_gettime01",
        // "timer_settime01",
        // "timer_settime02",
        // "timer_settime03",
        // "timerfd01",
        // "timerfd02",
        // "timerfd04",
        // "timerfd_create01",
        // "timerfd_gettime01",
        // "timerfd_settime01",
        // "timerfd_settime02",
        "times01",
        "times03",
        "tkill01",
        //"tkill02",
        // "tpci",
        // "trace_sched",
        // "truncate02",
        // "truncate02_64",
        // "truncate03",
        // "truncate03_64",
        // "tst_brk",
        // "tst_brkm",
        // "tst_cgctl",
        // "tst_checkpoint",
        // "tst_device",
        // "tst_exit",
        // "tst_fs_has_free",
        // "tst_fsfreeze",
        // "tst_get_free_pids",
        // "tst_get_median",
        // "tst_get_unused_port",
        // "tst_getconf",
        // "tst_hexdump",
        // "tst_kvcmp",
        // "tst_lockdown_enabled",
        // "tst_ncpus",
        // "tst_ncpus_conf",
        // "tst_ncpus_max",
        // "tst_random",
        // "tst_res",
        // "tst_resm",
        // "tst_rod",
        // "tst_secureboot_enabled",
        // "tst_supported_fs",
        // "uaccess",
        // "udp4-multi-diffip01",
        // "udp4-multi-diffip02",
        // "udp4-multi-diffip03",
        // "udp4-multi-diffip04",
        // "udp4-multi-diffip05",
        // "udp4-multi-diffip06",
        // "udp4-multi-diffip07",
        // "udp4-multi-diffnic01",
        // "udp4-multi-diffnic02",
        // "udp4-multi-diffnic03",
        // "udp4-multi-diffnic04",
        // "udp4-multi-diffnic05",
        // "udp4-multi-diffnic06",
        // "udp4-multi-diffnic07",
        // "udp4-multi-diffport01",
        // "udp4-multi-diffport02",
        // "udp4-multi-diffport03",
        // "udp4-multi-diffport04",
        // "udp4-multi-diffport05",
        // "udp4-multi-diffport06",
        // "udp4-multi-diffport07",
        // "udp4-uni-basic01",
        // "udp4-uni-basic02",
        // "udp4-uni-basic03",
        // "udp4-uni-basic04",
        // "udp4-uni-basic05",
        // "udp4-uni-basic06",
        // "udp4-uni-basic07",
        // "udp6-multi-diffip01",
        // "udp6-multi-diffip02",
        // "udp6-multi-diffip03",
        // "udp6-multi-diffip04",
        // "udp6-multi-diffip05",
        // "udp6-multi-diffip06",
        // "udp6-multi-diffip07",
        // "udp6-multi-diffnic01",
        // "udp6-multi-diffnic02",
        // "udp6-multi-diffnic03",
        // "udp6-multi-diffnic04",
        // "udp6-multi-diffnic05",
        // "udp6-multi-diffnic06",
        // "udp6-multi-diffnic07",
        // "udp6-multi-diffport01",
        // "udp6-multi-diffport02",
        // "udp6-multi-diffport03",
        // "udp6-multi-diffport04",
        // "udp6-multi-diffport05",
        // "udp6-multi-diffport06",
        // "udp6-multi-diffport07",
        // "udp6-uni-basic01",
        // "udp6-uni-basic02",
        // "udp6-uni-basic03",
        // "udp6-uni-basic04",
        // "udp6-uni-basic05",
        // "udp6-uni-basic06",
        // "udp6-uni-basic07",
        "uevent01",
        "uevent02",
        "uevent03",
        "ulimit01",
        "umask01",
        "umip_basic_test",
        "umount01",
        "umount02",
        "umount03",
        "umount2_01",
        "umount2_02",
        "uname01",
        "uname02",
        "uname04",
        "unlink05",
        "unlink07",
        "unlink08",
        // "unlink09",
        "unlinkat01",
        "unshare01",
        "unshare02",
        "userfaultfd01",
        // "userns01",
        // "userns02",
        // "userns03",
        // "userns04",
        // "userns05",
        // "userns06",
        // "userns06_capcheck",
        // "userns07",
        // "userns08",
        // "ustat01",
        // "ustat02",
        // "utime01",
        // "utime02",
        // "utime03",
        // "utime04",
        // "utime05",
        // "utime06",
        // "utime07",
        // "utimensat01",
        // "utimes01",
        // "utsname01",
        // "utsname02",
        // "utsname03",
        // "utsname04",
        // "verify_caps_exec",
        // "vfork",
        // "vfork01",
        // "vfork02",
        // "vhangup01",
        // "vhangup02",
        // "vma01",
        // "vma02",
        // "vma03",
        // "vma04",
        // "vma05_vdso",
        // "vmsplice01",
        // "vmsplice02",
        // "vmsplice03",
        // "vmsplice04",
        //"vsock01",
        "wait01",
        "wait02",
        "wait401",
        "wait402",
        "wait403",
        "waitid01",
        "waitid02",
        "waitid03",
        "waitid04",
        "waitid05",
        "waitid06",
        // "waitid07",
        // "waitid08",
        // "waitid09",
        // "waitid10",
        // "waitid11",
        "waitpid01",
        "waitpid03",
        "waitpid04",
        // "waitpid06",
        // "waitpid07",
        // "waitpid08",
        // "waitpid09",
        // "waitpid10",
        // "waitpid11",
        // "waitpid12",
        // "waitpid13",
        "write01",
        "write02",
        "write03",
        "write04",
        "write05",
        "write06",
        "writetest",
        // "writev01",
        // "writev02",
        // "writev03",
        // "writev05",
        // "writev06",
        // "writev07",
        //"zram03",
    ];

    for test_name in &ltp_tests {
        command(
            &format!("/glibc/busybox echo RUN LTP CASE {}", test_name),
            glibc_home_dir.clone(),
        )
        .await;
        command(
            &format!("/glibc/ltp/testcases/bin/{}", test_name),
            glibc_home_dir.clone(),
        )
        .await;
        command(
            &format!("/glibc/busybox echo FAIL LTP CASE {} : 0", test_name),
            glibc_home_dir.clone(),
        )
        .await;
    }
}

async fn run_musl_tests(home_dir: PathBuf, exclude: Vec<(&str, &str, &str, PathBuf)>) {
    let musl_static_tests = vec![
        ("/musl/runtest.exe", "entry-static.exe", "argv"),
        ("/musl/runtest.exe", "entry-static.exe", "basename"),
        ("/musl/runtest.exe", "entry-static.exe", "clocale_mbfuncs"),
        ("/musl/runtest.exe", "entry-static.exe", "clock_gettime"),
        ("/musl/runtest.exe", "entry-static.exe", "dirname"),
        ("/musl/runtest.exe", "entry-static.exe", "env"),
        ("/musl/runtest.exe", "entry-static.exe", "fdopen"),
        ("/musl/runtest.exe", "entry-static.exe", "fnmatch"),
        ("/musl/runtest.exe", "entry-static.exe", "fscanf"),
        ("/musl/runtest.exe", "entry-static.exe", "fwscanf"),
        ("/musl/runtest.exe", "entry-static.exe", "iconv_open"),
        ("/musl/runtest.exe", "entry-static.exe", "inet_pton"),
        ("/musl/runtest.exe", "entry-static.exe", "mbc"),
        ("/musl/runtest.exe", "entry-static.exe", "memstream"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_cancel_points",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "pthread_cancel"),
        ("/musl/runtest.exe", "entry-static.exe", "pthread_cond"),
        ("/musl/runtest.exe", "entry-static.exe", "pthread_tsd"),
        ("/musl/runtest.exe", "entry-static.exe", "qsort"),
        ("/musl/runtest.exe", "entry-static.exe", "random"),
        ("/musl/runtest.exe", "entry-static.exe", "search_hsearch"),
        ("/musl/runtest.exe", "entry-static.exe", "search_insque"),
        ("/musl/runtest.exe", "entry-static.exe", "search_lsearch"),
        ("/musl/runtest.exe", "entry-static.exe", "search_tsearch"),
        ("/musl/runtest.exe", "entry-static.exe", "setjmp"),
        ("/musl/runtest.exe", "entry-static.exe", "snprintf"),
        ("/musl/runtest.exe", "entry-static.exe", "socket"),
        ("/musl/runtest.exe", "entry-static.exe", "sscanf"),
        ("/musl/runtest.exe", "entry-static.exe", "sscanf_long"),
        ("/musl/runtest.exe", "entry-static.exe", "stat"),
        ("/musl/runtest.exe", "entry-static.exe", "strftime"),
        ("/musl/runtest.exe", "entry-static.exe", "string"),
        ("/musl/runtest.exe", "entry-static.exe", "string_memcpy"),
        ("/musl/runtest.exe", "entry-static.exe", "string_memmem"),
        ("/musl/runtest.exe", "entry-static.exe", "string_memset"),
        ("/musl/runtest.exe", "entry-static.exe", "string_strchr"),
        ("/musl/runtest.exe", "entry-static.exe", "string_strcspn"),
        ("/musl/runtest.exe", "entry-static.exe", "string_strstr"),
        ("/musl/runtest.exe", "entry-static.exe", "strptime"),
        ("/musl/runtest.exe", "entry-static.exe", "strtod"),
        ("/musl/runtest.exe", "entry-static.exe", "strtod_simple"),
        ("/musl/runtest.exe", "entry-static.exe", "strtof"),
        ("/musl/runtest.exe", "entry-static.exe", "strtol"),
        ("/musl/runtest.exe", "entry-static.exe", "strtold"),
        ("/musl/runtest.exe", "entry-static.exe", "swprintf"),
        ("/musl/runtest.exe", "entry-static.exe", "tgmath"),
        ("/musl/runtest.exe", "entry-static.exe", "time"),
        ("/musl/runtest.exe", "entry-static.exe", "tls_align"),
        ("/musl/runtest.exe", "entry-static.exe", "udiv"),
        ("/musl/runtest.exe", "entry-static.exe", "ungetc"),
        ("/musl/runtest.exe", "entry-static.exe", "utime"),
        ("/musl/runtest.exe", "entry-static.exe", "wcsstr"),
        ("/musl/runtest.exe", "entry-static.exe", "wcstol"),
        ("/musl/runtest.exe", "entry-static.exe", "daemon_failure"),
        ("/musl/runtest.exe", "entry-static.exe", "dn_expand_empty"),
        ("/musl/runtest.exe", "entry-static.exe", "dn_expand_ptr_0"),
        ("/musl/runtest.exe", "entry-static.exe", "fflush_exit"),
        ("/musl/runtest.exe", "entry-static.exe", "fgets_eof"),
        ("/musl/runtest.exe", "entry-static.exe", "fgetwc_buffering"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "fpclassify_invalid_ld80",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "ftello_unflushed_append",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "getpwnam_r_crash"),
        ("/musl/runtest.exe", "entry-static.exe", "getpwnam_r_errno"),
        ("/musl/runtest.exe", "entry-static.exe", "iconv_roundtrips"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "inet_ntop_v4mapped",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "inet_pton_empty_last_field",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "iswspace_null"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "lrand48_signextend",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "lseek_large"),
        ("/musl/runtest.exe", "entry-static.exe", "malloc_0"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "mbsrtowcs_overflow",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "memmem_oob_read"),
        ("/musl/runtest.exe", "entry-static.exe", "memmem_oob"),
        ("/musl/runtest.exe", "entry-static.exe", "mkdtemp_failure"),
        ("/musl/runtest.exe", "entry-static.exe", "mkstemp_failure"),
        ("/musl/runtest.exe", "entry-static.exe", "printf_1e9_oob"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "printf_fmt_g_round",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "printf_fmt_g_zeros",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "printf_fmt_n"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_robust_detach",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_cancel_sem_wait",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_cond_smasher",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_condattr_setclock",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_exit_cancel",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_once_deadlock",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "pthread_rwlock_ebusy",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "putenv_doublefree"),
        ("/musl/runtest.exe", "entry-static.exe", "regex_backref_0"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "regex_bracket_icase",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "regex_ere_backref"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "regex_escaped_high_byte",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "regex_negated_range",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "regexec_nosub"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "rewind_clear_error",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "rlimit_open_files"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "scanf_bytes_consumed",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "scanf_match_literal_eof",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "scanf_nullbyte_char",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "setvbuf_unget"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "sigprocmask_internal",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "sscanf_eof"),
        ("/musl/runtest.exe", "entry-static.exe", "statvfs"),
        ("/musl/runtest.exe", "entry-static.exe", "strverscmp"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "syscall_sign_extend",
        ),
        ("/musl/runtest.exe", "entry-static.exe", "uselocale_0"),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "wcsncpy_read_overflow",
        ),
        (
            "/musl/runtest.exe",
            "entry-static.exe",
            "wcsstr_false_negative",
        ),
    ];

    let musl_dynamic_tests = vec![
        ("/musl/runtest.exe", "entry-dynamic.exe", "argv"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "basename"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "clocale_mbfuncs"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "clock_gettime"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "dirname"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "dlopen"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "env"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fdopen"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fnmatch"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fscanf"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fwscanf"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "iconv_open"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "inet_pton"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "mbc"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "memstream"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_cancel_points",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "pthread_cancel"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "pthread_cond"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "pthread_tsd"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "qsort"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "random"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "search_hsearch"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "search_insque"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "search_lsearch"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "search_tsearch"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "sem_init"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "setjmp"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "snprintf"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "socket"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "sscanf"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "sscanf_long"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "stat"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strftime"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_memcpy"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_memmem"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_memset"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_strchr"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_strcspn"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "string_strstr"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strptime"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strtod"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strtod_simple"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strtof"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strtol"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strtold"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "swprintf"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "tgmath"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "time"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "tls_init"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "tls_local_exec"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "udiv"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "ungetc"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "utime"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "wcsstr"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "wcstol"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "daemon_failure"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "dn_expand_empty"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "dn_expand_ptr_0"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fflush_exit"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fgets_eof"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "fgetwc_buffering"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "fpclassify_invalid_ld80",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "ftello_unflushed_append",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "getpwnam_r_crash"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "getpwnam_r_errno"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "iconv_roundtrips"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "inet_ntop_v4mapped",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "inet_pton_empty_last_field",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "iswspace_null"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "lrand48_signextend",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "lseek_large"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "malloc_0"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "mbsrtowcs_overflow",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "memmem_oob_read"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "memmem_oob"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "mkdtemp_failure"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "mkstemp_failure"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "printf_1e9_oob"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "printf_fmt_g_round",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "printf_fmt_g_zeros",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "printf_fmt_n"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_robust_detach",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_cond_smasher",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_condattr_setclock",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_exit_cancel",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_once_deadlock",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "pthread_rwlock_ebusy",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "putenv_doublefree",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "regex_backref_0"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "regex_bracket_icase",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "regex_ere_backref",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "regex_escaped_high_byte",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "regex_negated_range",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "regexec_nosub"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "rewind_clear_error",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "rlimit_open_files",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "scanf_bytes_consumed",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "scanf_match_literal_eof",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "scanf_nullbyte_char",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "setvbuf_unget"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "sigprocmask_internal",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "sscanf_eof"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "statvfs"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "strverscmp"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "syscall_sign_extend",
        ),
        ("/musl/runtest.exe", "entry-dynamic.exe", "tls_get_new_dtv"),
        ("/musl/runtest.exe", "entry-dynamic.exe", "uselocale_0"),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "wcsncpy_read_overflow",
        ),
        (
            "/musl/runtest.exe",
            "entry-dynamic.exe",
            "wcsstr_false_negative",
        ),
    ];

    for tests in vec![musl_static_tests, musl_dynamic_tests] {
        for (runtest, entry, test_name) in tests {
            let test_dir = home_dir.clone();
            if !exclude
                .iter()
                .any(|&(ex_runtest, ex_entry, ex_test_name, ref ex_dir)| {
                    ex_runtest == runtest
                        && ex_entry == entry
                        && ex_test_name == test_name
                        && ex_dir == &test_dir
                })
            {
                command(
                    &alloc::format!("{} -w {} {}", runtest, entry, test_name),
                    test_dir,
                )
                .await;
            }
        }
    }
}

async fn run_glibc_tests(glibc_home_dir: PathBuf, exclude: Vec<(&str, &str, &str, PathBuf)>) {
    let glibc_static_tests = vec![
        ("/glibc/runtest.exe", "entry-static.exe", "argv"),
        ("/glibc/runtest.exe", "entry-static.exe", "basename"),
        ("/glibc/runtest.exe", "entry-static.exe", "clocale_mbfuncs"),
        ("/glibc/runtest.exe", "entry-static.exe", "clock_gettime"),
        ("/glibc/runtest.exe", "entry-static.exe", "dirname"),
        ("/glibc/runtest.exe", "entry-static.exe", "env"),
        ("/glibc/runtest.exe", "entry-static.exe", "fdopen"),
        ("/glibc/runtest.exe", "entry-static.exe", "fnmatch"),
        ("/glibc/runtest.exe", "entry-static.exe", "fscanf"),
        ("/glibc/runtest.exe", "entry-static.exe", "fwscanf"),
        ("/glibc/runtest.exe", "entry-static.exe", "iconv_open"),
        ("/glibc/runtest.exe", "entry-static.exe", "inet_pton"),
        ("/glibc/runtest.exe", "entry-static.exe", "mbc"),
        ("/glibc/runtest.exe", "entry-static.exe", "memstream"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_cancel_points",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "pthread_cancel"),
        ("/glibc/runtest.exe", "entry-static.exe", "pthread_cond"),
        ("/glibc/runtest.exe", "entry-static.exe", "pthread_tsd"),
        ("/glibc/runtest.exe", "entry-static.exe", "qsort"),
        ("/glibc/runtest.exe", "entry-static.exe", "random"),
        ("/glibc/runtest.exe", "entry-static.exe", "search_hsearch"),
        ("/glibc/runtest.exe", "entry-static.exe", "search_insque"),
        ("/glibc/runtest.exe", "entry-static.exe", "search_lsearch"),
        ("/glibc/runtest.exe", "entry-static.exe", "search_tsearch"),
        ("/glibc/runtest.exe", "entry-static.exe", "setjmp"),
        ("/glibc/runtest.exe", "entry-static.exe", "snprintf"),
        ("/glibc/runtest.exe", "entry-static.exe", "socket"),
        ("/glibc/runtest.exe", "entry-static.exe", "sscanf"),
        ("/glibc/runtest.exe", "entry-static.exe", "sscanf_long"),
        ("/glibc/runtest.exe", "entry-static.exe", "stat"),
        ("/glibc/runtest.exe", "entry-static.exe", "strftime"),
        ("/glibc/runtest.exe", "entry-static.exe", "string"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_memcpy"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_memmem"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_memset"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_strchr"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_strcspn"),
        ("/glibc/runtest.exe", "entry-static.exe", "string_strstr"),
        ("/glibc/runtest.exe", "entry-static.exe", "strptime"),
        ("/glibc/runtest.exe", "entry-static.exe", "strtod"),
        ("/glibc/runtest.exe", "entry-static.exe", "strtod_simple"),
        ("/glibc/runtest.exe", "entry-static.exe", "strtof"),
        ("/glibc/runtest.exe", "entry-static.exe", "strtol"),
        ("/glibc/runtest.exe", "entry-static.exe", "strtold"),
        ("/glibc/runtest.exe", "entry-static.exe", "swprintf"),
        ("/glibc/runtest.exe", "entry-static.exe", "tgmath"),
        ("/glibc/runtest.exe", "entry-static.exe", "time"),
        ("/glibc/runtest.exe", "entry-static.exe", "tls_align"),
        ("/glibc/runtest.exe", "entry-static.exe", "udiv"),
        ("/glibc/runtest.exe", "entry-static.exe", "ungetc"),
        ("/glibc/runtest.exe", "entry-static.exe", "utime"),
        ("/glibc/runtest.exe", "entry-static.exe", "wcsstr"),
        ("/glibc/runtest.exe", "entry-static.exe", "wcstol"),
        ("/glibc/runtest.exe", "entry-static.exe", "daemon_failure"),
        ("/glibc/runtest.exe", "entry-static.exe", "dn_expand_empty"),
        ("/glibc/runtest.exe", "entry-static.exe", "dn_expand_ptr_0"),
        ("/glibc/runtest.exe", "entry-static.exe", "fflush_exit"),
        ("/glibc/runtest.exe", "entry-static.exe", "fgets_eof"),
        ("/glibc/runtest.exe", "entry-static.exe", "fgetwc_buffering"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "fpclassify_invalid_ld80",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "ftello_unflushed_append",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "getpwnam_r_crash"),
        ("/glibc/runtest.exe", "entry-static.exe", "getpwnam_r_errno"),
        ("/glibc/runtest.exe", "entry-static.exe", "iconv_roundtrips"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "inet_ntop_v4mapped",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "inet_pton_empty_last_field",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "iswspace_null"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "lrand48_signextend",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "lseek_large"),
        ("/glibc/runtest.exe", "entry-static.exe", "malloc_0"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "mbsrtowcs_overflow",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "memmem_oob_read"),
        ("/glibc/runtest.exe", "entry-static.exe", "memmem_oob"),
        ("/glibc/runtest.exe", "entry-static.exe", "mkdtemp_failure"),
        ("/glibc/runtest.exe", "entry-static.exe", "mkstemp_failure"),
        ("/glibc/runtest.exe", "entry-static.exe", "printf_1e9_oob"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "printf_fmt_g_round",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "printf_fmt_g_zeros",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "printf_fmt_n"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_robust_detach",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_cancel_sem_wait",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_cond_smasher",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_condattr_setclock",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_exit_cancel",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_once_deadlock",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "pthread_rwlock_ebusy",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "putenv_doublefree",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "regex_backref_0"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "regex_bracket_icase",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "regex_ere_backref",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "regex_escaped_high_byte",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "regex_negated_range",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "regexec_nosub"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "rewind_clear_error",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "rlimit_open_files",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "scanf_bytes_consumed",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "scanf_match_literal_eof",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "scanf_nullbyte_char",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "setvbuf_unget"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "sigprocmask_internal",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "sscanf_eof"),
        ("/glibc/runtest.exe", "entry-static.exe", "statvfs"),
        ("/glibc/runtest.exe", "entry-static.exe", "strverscmp"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "syscall_sign_extend",
        ),
        ("/glibc/runtest.exe", "entry-static.exe", "uselocale_0"),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "wcsncpy_read_overflow",
        ),
        (
            "/glibc/runtest.exe",
            "entry-static.exe",
            "wcsstr_false_negative",
        ),
    ];

    let glibc_dynamic_tests = vec![
        ("/glibc/runtest.exe", "entry-dynamic.exe", "argv"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "basename"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "clocale_mbfuncs"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "clock_gettime"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "dirname"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "dlopen"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "env"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fdopen"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fnmatch"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fscanf"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fwscanf"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "iconv_open"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "inet_pton"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "mbc"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "memstream"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_cancel_points",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "pthread_cancel"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "pthread_cond"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "pthread_tsd"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "qsort"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "random"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "search_hsearch"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "search_insque"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "search_lsearch"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "search_tsearch"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "sem_init"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "setjmp"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "snprintf"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "socket"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "sscanf"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "sscanf_long"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "stat"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strftime"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_memcpy"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_memmem"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_memset"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_strchr"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_strcspn"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "string_strstr"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strptime"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strtod"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strtod_simple"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strtof"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strtol"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strtold"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "swprintf"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "tgmath"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "time"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "tls_init"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "tls_local_exec"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "udiv"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "ungetc"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "utime"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "wcsstr"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "wcstol"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "daemon_failure"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "dn_expand_empty"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "dn_expand_ptr_0"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fflush_exit"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "fgets_eof"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "fgetwc_buffering",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "fpclassify_invalid_ld80",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "ftello_unflushed_append",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "getpwnam_r_crash",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "getpwnam_r_errno",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "iconv_roundtrips",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "inet_ntop_v4mapped",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "inet_pton_empty_last_field",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "iswspace_null"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "lrand48_signextend",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "lseek_large"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "malloc_0"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "mbsrtowcs_overflow",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "memmem_oob_read"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "memmem_oob"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "mkdtemp_failure"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "mkstemp_failure"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "printf_1e9_oob"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "printf_fmt_g_round",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "printf_fmt_g_zeros",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "printf_fmt_n"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_robust_detach",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_cond_smasher",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_condattr_setclock",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_exit_cancel",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_once_deadlock",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "pthread_rwlock_ebusy",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "putenv_doublefree",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "regex_backref_0"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "regex_bracket_icase",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "regex_ere_backref",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "regex_escaped_high_byte",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "regex_negated_range",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "regexec_nosub"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "rewind_clear_error",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "rlimit_open_files",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "scanf_bytes_consumed",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "scanf_match_literal_eof",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "scanf_nullbyte_char",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "setvbuf_unget"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "sigprocmask_internal",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "sscanf_eof"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "statvfs"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "strverscmp"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "syscall_sign_extend",
        ),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "tls_get_new_dtv"),
        ("/glibc/runtest.exe", "entry-dynamic.exe", "uselocale_0"),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "wcsncpy_read_overflow",
        ),
        (
            "/glibc/runtest.exe",
            "entry-dynamic.exe",
            "wcsstr_false_negative",
        ),
    ];

    for tests in vec![glibc_static_tests, glibc_dynamic_tests] {
        for (runtest, entry, test_name) in tests {
            let test_dir = glibc_home_dir.clone();
            if !exclude
                .iter()
                .any(|&(ex_runtest, ex_entry, ex_test_name, ref ex_dir)| {
                    ex_runtest == runtest
                        && ex_entry == entry
                        && ex_test_name == test_name
                        && ex_dir == &test_dir
                })
            {
                command(
                    &alloc::format!("{} -w {} {}", runtest, entry, test_name),
                    test_dir,
                )
                .await;
            }
        }
    }
}
